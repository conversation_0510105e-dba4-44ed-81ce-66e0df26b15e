import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/modules/account/account_model.dart';

class AccountGroupRepo {
  /// 获取分组及分组下账户列表
  static Future<BaseModel<List<AccountGroupModel>>> getAccountGroupList() async {
    var resp = await HttpUtil().post('api/Account_group/list');
    return BaseModel.fromJson(resp, (json) => (json as List<dynamic>).map((dynamic e) => AccountGroupModel.fromJson(e)).toList());
  }

  /// 新增分组
  static Future<BaseModel<dynamic>> addAccountGroup({required String name}) async {
    var resp = await HttpUtil().post('api/Account_group/add', data: {'name': name});
    return BaseModel.fromJson(resp, (json) => json);
  }

  /// 修改分组（名称）
  static Future<BaseModel<dynamic>> editAccountGroup({required int id, required String name}) async {
    var resp = await HttpUtil().post('api/Account_group/edit', data: {'id': id, 'name': name});
    return BaseModel.fromJson(resp, (json) => json);
  }

  /// 删除分组
  static Future<BaseModel<dynamic>> deleteAccountGroup({required int id}) async {
    var resp = await HttpUtil().post('api/Account_group/delete', data: {'id': id});
    return BaseModel.fromJson(resp, (json) => json);
  }

  /// 批量排序分组
  static Future<BaseModel<dynamic>> sortAccountGroups({required List<int> sortedIds}) async {
    String sortedIdString = sortedIds.join(',');
    var resp = await HttpUtil().post('api/Account_group/sort', data: {'sortedId': sortedIdString});
    return BaseModel.fromJson(resp, (json) => json);
  }

  /// 批量绑定账户到分组
  static Future<BaseModel<dynamic>> bindAccountsToGroup({required int groupId, required List<int> cardIds}) async {
    String cardIdString = cardIds.join(',');
    var resp = await HttpUtil().post('api/Account_group/bindCard', data: {'groupId': groupId, 'cardIds': cardIds});
    return BaseModel.fromJson(resp, (json) => json);
  }

  /// 批量解绑账户
  static Future<BaseModel<dynamic>> unbindAccountsFromGroup({required int groupId, required List<int> cardIds}) async {
    String cardIdString = cardIds.join(',');
    var resp = await HttpUtil().post('api/Account_group/unbindCard', data: {'groupId': groupId, 'cardIds': cardIdString});
    return BaseModel.fromJson(resp, (json) => json);
  }
}
