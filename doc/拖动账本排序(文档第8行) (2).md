# Global Parameters

**Global Param Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Global Param Query**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Global Param Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Global Auth**

> NO Auth

# Response Codes

| Response Codes | Description |
| -------------- | ----------- |
| No parameters |

# 迭代二期

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2025-04-08 15:39:56

> Update Time: 2025-04-08 15:39:56

```text
No description
```

**Folder Param Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Query**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Auth**

> Inherit auth from parent

**Query**

## 拖动账本排序(文档第8行)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2025-04-08 15:40:20

> Update Time: 2025-04-29 10:12:03

```text
No description
```

**API Status**

> In Progress

**URL**

> api/bookkeeping/updateBookkeepingSort

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> json

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 0f009598-6956-4e62-9ea0-4f2e34348301 | string | Yes | - |

**Body**

```javascript
{
    "categoryType": "1",
    "sortedId": "1,9,4,55",
    "token": "b39cff19-14d0-491c-856f-14574005f720"
}
```

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| categoryType | 1 | string | Yes | - |
| sortedId | 1,9,4,55 | string | Yes | 账本ID  约靠前,排序越大 |
| token | b39cff19-14d0-491c-856f-14574005f720 | string | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 0f009598-6956-4e62-9ea0-4f2e34348301 | string | Yes | - |

**Query**

## 指定分类下流水账单(参数内categoryId从收入来源&&支出分布获取)(文档第16行)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2025-04-08 16:10:00

> Update Time: 2025-04-29 10:12:27

```text
No description
```

**API Status**

> In Progress

**URL**

> api/chart/getCategoryMoneyLog

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> json

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 43f6de9b-f044-4c31-8960-26417944ce54 | string | Yes | - |

**Body**

```javascript
{
  "categoryId": "1610",
  "sortField": "money",
  "sortOrder": "desc"
}
```

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| categoryId | 1610 | string | Yes | 分类ID |
| sortField | money | string | Yes | 排序字段  金额:money  时间:time |
| sortOrder | desc | string | Yes | 排序方式: 倒序:desc  正序:asc |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 43f6de9b-f044-4c31-8960-26417944ce54 | string | Yes | - |

**Query**

## 计划删除(文档第18)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2025-01-07 10:47:00

> Update Time: 2025-04-29 10:12:42

```text
No description
```

**API Status**

> In Progress

**URL**

> api/plan/deletePlan

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> json

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 8d901f08-21e8-4352-bd22-0e86c0346ab9 | string | Yes | - |

**Body**

```javascript
{
    "planId":"11"
}
```

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 8d901f08-21e8-4352-bd22-0e86c0346ab9 | string | Yes | - |

**Query**

## 计划调整排序(文档第19)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2025-01-07 13:20:56

> Update Time: 2025-04-29 10:12:55

```text
No description
```

**API Status**

> In Progress

**URL**

> api/plan/updatePlanSort

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

**Query**

## 账户流水(文档27)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2025-04-08 17:45:49

> Update Time: 2025-04-29 10:13:41

```text
No description
```

**API Status**

> In Progress

**URL**

> api/card/getAccountLog

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> json

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 43f6de9b-f044-4c31-8960-26417944ce54 | string | Yes | - |

**Body**

```javascript
{
  "accountId": "495",
  "sortField": "",
  "sortOrder": "desc"
}
```

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 43f6de9b-f044-4c31-8960-26417944ce54 | string | Yes | - |

**Query**

## 账本详情页面(文档37)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2025-04-23 10:01:41

> Update Time: 2025-04-29 10:13:54

```text
No description
```

**API Status**

> In Progress

**URL**

> api/bookkeeping/indexDetail

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

**Query**
