import 'package:flutter/material.dart';
import 'package:flutter/src/scheduler/ticker.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:math_expressions/math_expressions.dart';
import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/loading.dart';

import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/bookkeeping_category_view.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/bookkeeping_keyboard_view.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/bookkeeping_controller.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/remark_panel_view.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/bookkeeping_shortcut_view.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/modules/mine_tab/user_store.dart';
import 'package:qiazhun/providers/bookkeeping_list_provider.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/extensions.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/models/price_info.dart';
import 'package:qiazhun/widgets/price_view.dart';

class BookkeepingPage extends GetView<BookkeepingController> {
  const BookkeepingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      extendBody: true,
      body: SafeArea(
        top: false,
        bottom: false,
        maintainBottomViewPadding: false,
        child: Container(
          child: Stack(
            children: [
              Container(
                color: Colors.transparent,
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  child: Image.asset(
                    'assets/images/add_bill_bg.png',
                    fit: BoxFit.fitWidth,
                    width: MediaQuery.of(context).size.width,
                  ),
                  top: 0),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                    leading: IconButton(
                      icon: Image.asset(
                        'assets/images/ic_back.png',
                        width: 24,
                        height: 24,
                      ),
                      onPressed: () {
                        RouterHelper.router.pop();
                      },
                    ),
                    backgroundColor: Colors.transparent,
                    scrolledUnderElevation: 0,
                    title: Image.asset(
                      'assets/images/icon_title.png',
                      width: 129,
                      height: 30,
                    ),
                    actions: [
                      GestureDetector(
                        onTap: () {
                          controller.onBookkeepingTap();
                        },
                        child: Row(
                          children: [
                            Image.asset(
                              'assets/images/hand.png',
                              width: 24,
                              height: 24,
                            ),
                            const SizedBox(
                              width: 6,
                            ),
                            Obx(() {
                              if (controller.bookkeepingInfo.value == null) {
                                return const Text(
                                  '选择账本',
                                  style: TextStyle(color: MColor.xFF1B1C1A, fontSize: 16, fontWeight: FontWeight.w500),
                                );
                              }
                              return Text(
                                '${controller.bookkeepingInfo.value?.accountBookName}',
                                style: TextStyle(color: MColor.xFF1B1C1A, fontSize: 16, fontWeight: FontWeight.w500),
                              );
                            }),
                            const SizedBox(
                              width: 14,
                            )
                          ],
                        ),
                      )
                    ],
                  )),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: Builder(builder: (context) {
                    return _calculationView;
                  }))
            ],
          ),
        ),
      ),
    );
  }

  Widget get _calculationView {
    return Column(
      children: [
        const Spacer(),
        Container(
          decoration: BoxDecoration(borderRadius: BorderRadius.vertical(top: Radius.circular(20)), color: MColor.xFFFFFFFF),
          child: Column(
            children: [
              const SizedBox(
                height: 14,
              ),
              Row(
                children: [
                  Expanded(
                      child: GestureDetector(
                    onTap: () {
                      controller.selectedTab.value = 0;
                    },
                    child: Obx(() {
                      return Text(
                        '支出',
                        style: TextStyle(fontSize: 16, height: 1.4, color: controller.selectedTab.value == 0 ? MColor.skin : MColor.xFF999999),
                        textAlign: TextAlign.center,
                      );
                    }),
                  )),
                  Expanded(
                      child: GestureDetector(
                    onTap: () {
                      controller.selectedTab.value = 1;
                      controller.isSavingInput.value = false;
                      controller.savingResult.value = null;
                      controller.textController.text = controller.bookkeepingResult.value?.toStringAsFixed(2) ?? '';
                    },
                    child: Obx(() {
                      return Text(
                        '收入',
                        style: TextStyle(fontSize: 16, height: 1.4, color: controller.selectedTab.value == 1 ? MColor.skin : MColor.xFF999999),
                        textAlign: TextAlign.center,
                      );
                    }),
                  )),
                  Expanded(
                      child: GestureDetector(
                    onTap: () {
                      RouterHelper.router.pushNamed(Routes.debtPath);
                    },
                    child: Obx(() {
                      return Text(
                        '借支',
                        style: TextStyle(fontSize: 16, height: 1.4, color: controller.selectedTab.value == 2 ? MColor.skin : MColor.xFF999999),
                        textAlign: TextAlign.center,
                      );
                    }),
                  )),
                ],
              ),
              Divider(
                height: 28,
                thickness: 0.5,
                color: MColor.skin.withOpacity(0.2),
                indent: 14,
              ),
              const BookkeepingCategoryView()
            ],
          ),
        ),
        Container(
            padding: EdgeInsets.all(14),
            decoration: BoxDecoration(color: MColor.xFFD7F5E6),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Container(
                          decoration: BoxDecoration(
                            color: Colors.yellow,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          // padding: EdgeInsets.symmetric(horizontal: 14, vertical: 10),
                          height: 44,
                          // child: Text(
                          //   '123',
                          //   style: TextStyle(height: 1.1, fontSize: 20, color: MColor.xFF1B1C1A),
                          // ),
                          child: Obx(() {
                            var prefix = '';
                            var label = '';
                            var labelColor;
                            if (controller.isSavingInput.value == true) {
                              label = controller.selectedTab.value == 0
                                  ? '支出¥${controller.bookkeepingResult.value?.toStringAsFixed(2) ?? ''}'
                                  : '收入¥${controller.bookkeepingResult.value?.toStringAsFixed(2) ?? ''}';
                              prefix = '节省';
                              labelColor = MColor.skin;
                            } else {
                              label = controller.savingResult.value != null ? '节省¥${controller.savingResult.value?.toStringAsFixed(2) ?? ''}' : '';
                              prefix = controller.selectedTab.value == 0 ? '支出' : '收入';
                              labelColor = MColor.xFFFFBE4A;
                            }
                            return TextField(
                              controller: controller.textController,
                              focusNode: controller.textNode,
                              keyboardType: TextInputType.none,
                              style: TextStyle(fontSize: 20, color: MColor.xFF1B1C1A, height: 1),
                              decoration: InputDecoration(
                                floatingLabelBehavior: FloatingLabelBehavior.always,
                                labelText: label,
                                labelStyle: TextStyle(fontSize: 14, color: labelColor, height: 1),
                                hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1),
                                border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
                                filled: true,
                                fillColor: MColor.xFFFFFFFF,
                                prefixIcon: Container(
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const SizedBox(
                                        width: 10,
                                      ),
                                      Text(
                                        prefix,
                                        style: TextStyle(color: controller.isSavingInput.value ? MColor.xFFFFBE4A : MColor.skin, fontSize: 16, height: 1.4),
                                      )
                                    ],
                                  ),
                                ),
                                suffixIcon: Container(
                                  // color: MColor.skin,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          RouterHelper.router.pushNamed(Routes.writeRemarkPath, extra: {'remarkStr': controller.remarkStr.value}).then((value) {
                                            if (value != null) {
                                              controller.remarkStr.value = value.toString();
                                            }
                                          });
                                          // showModalBottomSheet(
                                          //     context: context,
                                          //     builder: (context) {
                                          //       return const RemarkPanel();
                                          //     },
                                          //     useRootNavigator: true);
                                        },
                                        child: RichText(
                                          textAlign: TextAlign.center,
                                          text: WidgetSpan(
                                              child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text(
                                                '备注',
                                                style: TextStyle(
                                                    color: controller.remarkStr.value.isNotEmpty ? MColor.skin : MColor.xFF999999, fontSize: 12, height: 1.4),
                                              ),
                                              const SizedBox(
                                                width: 2,
                                              ),
                                              Icon(
                                                Icons.edit_document,
                                                size: 14,
                                                color: controller.remarkStr.value.isNotEmpty ? MColor.skin : MColor.xFF999999,
                                              )
                                            ],
                                          )),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 0),
                                isDense: true,
                              ),
                              onChanged: (value) {
                                logger.i('BookkeepingPage onTextChanged $value');
                              },
                            );
                          })),
                    )
                  ],
                ),
                const SizedBox(
                  height: 14,
                ),
                // 横滑组件
                const BookkeepingShortcutView(),
                const SizedBox(
                  height: 14,
                ),
                const BookkeepingKeyboardView(),
              ],
            )),
      ],
    );
  }
}
