import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_add_page.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class AccountAddLoanSection extends StatefulWidget {
  final AccountAddController? addController;
  final AccountModel? accountModel;
  final int? selectedSubType;

  const AccountAddLoanSection({super.key, this.addController, this.accountModel, this.selectedSubType});
  @override
  State<StatefulWidget> createState() => _AccountAddLoanState();
}

class _AccountAddLoanState extends State<AccountAddLoanSection> {
  bool _loanType = true;
  bool _inAssets = true;

  bool _isEditing = false;
  final TextEditingController _totalController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _remainController = TextEditingController();

  DateTime? _loanDate;
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');

  @override
  void initState() {
    widget.addController?.onAction = _addAccount;
    if (widget.accountModel != null) {
      _loanType = widget.accountModel!.subType == '1';
      _nameController.text = widget.accountModel!.accountName ?? '';
      _totalController.text = widget.accountModel!.amount ?? '';
      _remainController.text = widget.accountModel!.balance ?? '';
      _loanDate = _dateFormat.tryParse(widget.accountModel!.dueDate ?? '') ?? DateTime.now();
      _inAssets = widget.accountModel!.isJoinTotal == 1;
      _isEditing = true;
    } else if (widget.selectedSubType != null) {
      _loanType = widget.selectedSubType == 0;
    }
    super.initState();
  }

  Future<void> _addAccount() async {
    if (_totalController.text.isEmpty) {
      showToast(_loanType ? '请填写借入金额' : '请填写借出金额');
      return;
    }
    if (_nameController.text.isEmpty) {
      showToast(_loanType ? '请填写借给谁' : '请填写借给谁');
      return;
    }
    var remainText = _remainController.text;
    if (remainText.isEmpty) {
      showToast(_loanType ? '请填写未还金额' : '请填写待收回余额');
      return;
    }

    Loading.show();
    try {
      var resp = await AccountRepo.addSavingCard(
          id: widget.accountModel?.id,
          accountType: '6',
          price: _totalController.text,
          balance: _remainController.text,
          isJoinTotal: _inAssets ? '1' : '2',
          accountName: _nameController.text,
          date: _dateFormat.format(_loanDate ?? DateTime.now()),
          loanType: _loanType ? '1' : '2');
      if (resp.code == 1) {
        showToast('添加账号成功');
        RouterHelper.router.pop();
      } else {
        showToast(resp.msg ?? '添加账号失败');
      }
    } catch (e) {
      showToast('添加账号失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(20)),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    setState(() {
                      if (!_loanType) {
                        _loanType = true;
                      }
                    });
                  },
                  child: Container(
                    height: 40,
                    decoration: _loanType
                        ? BoxDecoration(
                            color: MColor.xFFFFD180, borderRadius: BorderRadius.only(topLeft: Radius.circular(20), bottomRight: Radius.circular(20)))
                        : null,
                    child: Center(
                        child: Text(
                      '向谁借',
                      style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 15),
                    )),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    setState(() {
                      if (_loanType) {
                        _loanType = false;
                      }
                    });
                  },
                  child: Container(
                    height: 40,
                    decoration: !_loanType
                        ? BoxDecoration(
                            color: MColor.xFFFFD180, borderRadius: BorderRadius.only(topLeft: Radius.circular(20), bottomRight: Radius.circular(20)))
                        : null,
                    child: Center(
                        child: Text(
                      '借给谁',
                      style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 15),
                    )),
                  ),
                ),
              )
            ],
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 14.0),
            child: Column(
              children: [
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.zero,
                  leading: Text(
                    _loanType ? '向谁借' : '借给谁',
                    style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
                  ),
                  trailing: Container(
                    width: 200,
                    // color: Colors.blue,
                    child: TextField(
                      controller: _nameController,
                      keyboardType: TextInputType.text,
                      textAlign: TextAlign.end,
                      decoration: InputDecoration(
                        hintText: '请输入',
                        hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                        border: OutlineInputBorder(borderSide: BorderSide.none),
                        filled: true,
                        fillColor: Colors.transparent,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                        isDense: true,
                      ),
                    ),
                  ),
                ),
                Divider(
                  height: 0.5,
                  thickness: 0.5,
                  color: MColor.xFFF5F5F5,
                ),
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.zero,
                  leading: Text(
                    _loanType ? '借入金额' : '借出金额',
                    style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
                  ),
                  trailing: Container(
                    width: 200,
                    // color: Colors.blue,
                    child: TextField(
                      controller: _totalController,
                      keyboardType: TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()],
                      textAlign: TextAlign.end,
                      decoration: InputDecoration(
                        hintText: '请输入',
                        hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                        border: OutlineInputBorder(borderSide: BorderSide.none),
                        filled: true,
                        fillColor: Colors.transparent,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                        isDense: true,
                      ),
                      onChanged: (text) {
                        if (!_isEditing) {
                          _remainController.text = text;
                        }
                      },
                    ),
                  ),
                ),
                Divider(
                  height: 0.5,
                  thickness: 0.5,
                  color: MColor.xFFF5F5F5,
                ),
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.zero,
                  leading: Text(
                    _loanType ? '还款日' : '收回日',
                    style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
                  ),
                  onTap: () {
                    showDatePicker(
                            locale: Locale('zh', 'CN'),
                            context: context,
                            initialEntryMode: DatePickerEntryMode.calendarOnly,
                            currentDate: _loanDate ?? DateTime.now(),
                            firstDate: DateTime(2020, 1, 1),
                            lastDate: DateTime(2199, 1, 1))
                        .then((date) {
                      if (date != null) {
                        setState(
                          () {
                            _loanDate = date;
                          },
                        );
                      }
                    });
                  },
                  trailing: Container(
                    // color: Colors.yellow,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(_loanDate == null ? '选择日期' : _dateFormat.format(_loanDate!), style: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4)),
                        const SizedBox(
                          width: 12,
                        ),
                        Image.asset(
                          'assets/images/ic_calendar.png',
                          width: 16,
                          height: 16,
                          fit: BoxFit.fill,
                        )
                      ],
                    ),
                  ),
                ),
                Divider(
                  height: 0.5,
                  thickness: 0.5,
                  color: MColor.xFFF5F5F5,
                ),
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.zero,
                  leading: Text(
                    _loanType ? '未还金额' : '待收回余额',
                    style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
                  ),
                  trailing: Container(
                    width: 200,
                    // color: Colors.blue,
                    child: TextField(
                      controller: _remainController,
                      keyboardType: TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()],
                      textAlign: TextAlign.end,
                      decoration: InputDecoration(
                        hintText: '请输入',
                        hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                        border: OutlineInputBorder(borderSide: BorderSide.none),
                        filled: true,
                        fillColor: Colors.transparent,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                        isDense: true,
                      ),
                    ),
                  ),
                ),
                Divider(
                  height: 0.5,
                  thickness: 0.5,
                  color: MColor.xFFF5F5F5,
                ),
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.zero,
                  leading: Text(
                    '是否计入资产',
                    style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
                  ),
                  trailing: SizedBox(
                    height: 24,
                    child: Switch(
                      value: _inAssets,
                      activeTrackColor: MColor.skin,
                      activeColor: MColor.xFFFFFFFF,
                      onChanged: (value) {
                        setState(() {
                          _inAssets = !_inAssets;
                        });
                      },
                    ),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
