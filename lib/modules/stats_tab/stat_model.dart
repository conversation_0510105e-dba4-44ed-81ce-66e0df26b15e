import 'package:json_annotation/json_annotation.dart';

part 'stat_model.g.dart';

@JsonSerializable()
class ChartIndexResp extends Object {
  @JsonKey(name: 'result')
  List<ChartIndexResult>? result;

  @Json<PERSON>ey(name: 'bookkeepingNumber')
  String? bookkeepingNumber;
  @Json<PERSON>ey(name: 'total')
  String? total;

  ChartIndexResp(this.result, this.bookkeepingNumber, this.total);

  factory ChartIndexResp.fromJson(Map<String, dynamic> srcJson) => _$ChartIndexRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$ChartIndexRespToJson(this);
}

@JsonSerializable()
class ChartIndexResult extends Object {
  @JsonKey(name: 'date')
  String? date;

  @JsonKey(name: 'expenditurePrice')
  String? expenditurePrice;

  @JsonKey(name: 'incomePrice')
  String? incomePrice;

  ChartIndexResult(
    this.date,
    this.expenditurePrice,
    this.incomePrice,
  );

  factory ChartIndexResult.fromJson(Map<String, dynamic> srcJson) => _$ChartIndexResultFromJson(srcJson);

  Map<String, dynamic> toJson() => _$ChartIndexResultToJson(this);
}

@JsonSerializable()
class IncomePaymentResp extends Object {
  @JsonKey(name: 'incomeItems')
  IncomeOutcomeItems? incomeItems;

  @JsonKey(name: 'expenditureItems')
  IncomeOutcomeItems? expenditureItems;

  @JsonKey(name: 'bookkeepingNumber')
  String? bookkeepingNumber;

  IncomePaymentResp(
    this.incomeItems,
    this.expenditureItems,
    this.bookkeepingNumber,
  );

  factory IncomePaymentResp.fromJson(Map<String, dynamic> srcJson) => _$IncomePaymentRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$IncomePaymentRespToJson(this);
}

@JsonSerializable()
class IncomeOutcomeItems extends Object {
  @JsonKey(name: 'total')
  String? total;

  @JsonKey(name: 'items')
  List<dynamic>? items;

  IncomeOutcomeItems(
    this.total,
    this.items,
  );

  factory IncomeOutcomeItems.fromJson(Map<String, dynamic> srcJson) => _$IncomeOutcomeItemsFromJson(srcJson);

  Map<String, dynamic> toJson() => _$IncomeOutcomeItemsToJson(this);
}

@JsonSerializable()
class PropertyTrendResp {
  @JsonKey(name: 'result')
  List<PropertyTrendItem>? result;
  @JsonKey(name: 'total')
  dynamic total;

  PropertyTrendResp(
    this.total,
    this.result,
  );

  factory PropertyTrendResp.fromJson(Map<String, dynamic> srcJson) => _$PropertyTrendRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$PropertyTrendRespToJson(this);
}

@JsonSerializable()
class PropertyTrendItem extends Object {
  @JsonKey(name: 'date')
  String? date;

  @JsonKey(name: 'netAssets')
  String? netAssets;

  PropertyTrendItem(
    this.date,
    this.netAssets,
  );

  factory PropertyTrendItem.fromJson(Map<String, dynamic> srcJson) => _$PropertyTrendItemFromJson(srcJson);

  Map<String, dynamic> toJson() => _$PropertyTrendItemToJson(this);
}

@JsonSerializable()
class PropertySummaryResp extends Object {
  @JsonKey(name: 'result')
  List<PropertySummaryItem>? result;
  @JsonKey(name: 'total')
  dynamic total;

  PropertySummaryResp(
    this.total,
    this.result,
  );

  factory PropertySummaryResp.fromJson(Map<String, dynamic> srcJson) => _$PropertySummaryRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$PropertySummaryRespToJson(this);
}

@JsonSerializable()
class PropertySummaryItem extends Object {
  @JsonKey(name: 'accountName')
  String? accountName;

  @JsonKey(name: 'accountTypeCount')
  String? accountTypeCount;
  @JsonKey(name: 'accountType')
  String? accountType;

  @JsonKey(name: 'money')
  String? money;
  @JsonKey(name: 'proportion')
  String? proportion;
  @JsonKey(name: 'icon')
  String? icon;

  PropertySummaryItem(this.accountName, this.accountType, this.money, this.accountTypeCount, this.proportion, this.icon);

  factory PropertySummaryItem.fromJson(Map<String, dynamic> srcJson) => _$PropertySummaryItemFromJson(srcJson);

  Map<String, dynamic> toJson() => _$PropertySummaryItemToJson(this);
}

@JsonSerializable()
class LiabilityResp extends Object {
  @JsonKey(name: 'total')
  int? total;

  @JsonKey(name: 'consumptionAmount')
  int? consumptionAmount;

  @JsonKey(name: 'amountDueBalance')
  int? amountDueBalance;

  LiabilityResp(
    this.total,
    this.consumptionAmount,
    this.amountDueBalance,
  );

  factory LiabilityResp.fromJson(Map<String, dynamic> srcJson) => _$LiabilityRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$LiabilityRespToJson(this);
}

@JsonSerializable()
class SaveUnnecessaryResp extends Object {
  @JsonKey(name: 'saveDepositTotal')
  String? saveDepositTotal;

  @JsonKey(name: 'necessaryTotal')
  String? necessaryTotal;

  @JsonKey(name: 'bookkeepingNumber')
  String? bookkeepingNumber;

  SaveUnnecessaryResp(
    this.saveDepositTotal,
    this.necessaryTotal,
    this.bookkeepingNumber,
  );

  factory SaveUnnecessaryResp.fromJson(Map<String, dynamic> srcJson) => _$SaveUnnecessaryRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$SaveUnnecessaryRespToJson(this);
}
