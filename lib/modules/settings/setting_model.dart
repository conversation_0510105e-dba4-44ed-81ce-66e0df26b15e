import 'package:json_annotation/json_annotation.dart';

part 'setting_model.g.dart';

@JsonSerializable()
class IconResp {
  @Json<PERSON>ey(name: 'accountIconList')
  List<IconInfo>? accountIconList;
  @J<PERSON><PERSON><PERSON>(name: 'flowingWaterIconList')
  List<IconInfo>? flowingWaterIconList;
  @Json<PERSON><PERSON>(name: 'planIconList')
  List<IconInfo>? planIconList;

  IconResp(this.accountIconList, this.flowingWaterIconList, this.planIconList);

  factory IconResp.fromJson(Map<String, dynamic> srcJson) => _$IconRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$IconRespToJson(this);
}

@JsonSerializable()
class IconInfo extends Object {
  @JsonKey(name: 'id')
  int? id;
  @Json<PERSON>ey(name: 'pid')
  int? pid;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'icon')
  String? icon;
  @J<PERSON><PERSON><PERSON>(name: 'name')
  String? name;

  @J<PERSON><PERSON><PERSON>(name: 'createtime')
  String? createtime;

  IconInfo(
    this.id,
    this.pid,
    this.name,
    this.icon,
    this.createtime,
  );

  factory IconInfo.fromJson(Map<String, dynamic> srcJson) => _$IconInfoFromJson(srcJson);

  Map<String, dynamic> toJson() => _$IconInfoToJson(this);
}
