import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';

Widget AccountInputTile(
    {required String leadingText,
    required TextEditingController textController,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    FocusNode? focusNode,
    String? hint,
    int? maxLength}) {
  return ListTile(
    dense: true,
    contentPadding: EdgeInsets.zero,
    leading: Text(
      leadingText,
      style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
    ),
    trailing: Container(
      width: 200,
      // color: Colors.blue,
      child: TextField(
        controller: textController,
        focusNode: focusNode,
        keyboardType: keyboardType ?? TextInputType.text,
        inputFormatters: inputFormatters,
        textAlign: TextAlign.end,
        decoration: InputDecoration(
            hintText: hint ?? '请输入',
            hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
            border: OutlineInputBorder(borderSide: BorderSide.none),
            filled: true,
            fillColor: Colors.transparent,
            contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
            isDense: true,
            counterText: ''),
        maxLength: maxLength,
      ),
    ),
  );
}

Widget AccountArrowTile({required String leadingText, GestureTapCallback? onTap, String? trailingText}) {
  return ListTile(
    onTap: () {
      onTap?.call();
    },
    dense: true,
    contentPadding: EdgeInsets.zero,
    leading: Text(
      leadingText,
      style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
    ),
    trailing: Container(
      width: 200,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (trailingText?.isNotEmpty == true) ...{
            Text(
              trailingText!,
              style: TextStyle(height: 1.4, fontSize: 13, color: MColor.xFF999999),
            ),
            const SizedBox(
              width: 4,
            ),
          },
          Icon(
            Icons.arrow_forward_ios,
            size: 13,
            color: MColor.xFF999999,
          ),
        ],
      ),
    ),
  );
}

Widget AccountSwitchTile({required String leadingText, required bool initValue, Function(bool v)? onChanged}) {
  return ListTile(
    dense: true,
    contentPadding: EdgeInsets.zero,
    leading: Text(
      leadingText,
      style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
    ),
    trailing: SizedBox(
      height: 24,
      child: Switch(
        value: initValue,
        activeTrackColor: MColor.skin,
        activeColor: MColor.xFFFFFFFF,
        onChanged: (value) {
          onChanged?.call(value);
        },
      ),
    ),
  );
}

Widget AccountRadiosTile({required List<String> choices, required int selected, double? marginBetween, Function(int selected)? onSelected}) {
  List<Widget> conditionViews = [];
  List.generate(choices.length, (index) {
    conditionViews.add(GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        onSelected?.call(index + 1);
      },
      child: Row(
        children: [
          Icon(selected == index + 1 ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              size: 16, color: selected == index + 1 ? MColor.skin : MColor.xFF999999),
          const SizedBox(
            width: 6,
          ),
          Text(
            choices[index],
            style: TextStyle(height: 1.4, fontSize: 14, color: selected == index + 1 ? MColor.xFF333333 : MColor.xFF999999),
          ),
        ],
      ),
    ));
    if (index != choices.length - 1) {
      if (marginBetween == null) {
        conditionViews.add(const Spacer());
      } else {
        conditionViews.add(SizedBox(
          width: marginBetween!,
        ));
      }
    }
  });
  return Row(
    crossAxisAlignment: CrossAxisAlignment.center,
    children: conditionViews,
  );
}

Widget AccountDateTile(
    {required String leadingText,
    DateTime? initDate,
    DateTime? firstDate,
    Function(DateTime? dt)? onDatePicked,
    Function()? onTap,
    DateTime? lastDate,
    bool? showYear = true,
    bool? showMonth = true}) {
  DateFormat df = showYear == true ? DateFormat('yyyy-MM-dd') : (showMonth == true ? DateFormat('MM-dd') : DateFormat('dd'));
  return Builder(builder: (context) {
    return ListTile(
      dense: true,
      contentPadding: EdgeInsets.zero,
      leading: Text(
        leadingText,
        style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
      ),
      trailing: Container(
        // color: Colors.yellow,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(initDate == null ? '选择日期' : df.format(initDate), style: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4)),
            const SizedBox(
              width: 12,
            ),
            Image.asset(
              'assets/images/ic_calendar.png',
              width: 16,
              height: 16,
              fit: BoxFit.fill,
            )
          ],
        ),
      ),
      onTap: () {
        if (onTap != null) {
          onTap.call();
        } else if (onDatePicked != null) {
          showDatePicker(
                  locale: Locale('zh', 'CN'),
                  context: context,
                  currentDate: initDate ?? DateTime.now(),
                  firstDate: firstDate ?? DateTime(2020, 1, 1),
                  lastDate: lastDate ?? DateTime(2100, 1, 1))
              .then((date) {
            onDatePicked.call(date);
          });
        }
      },
    );
  });
}
