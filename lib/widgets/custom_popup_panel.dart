import 'package:flutter/material.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/widgets/submit_button.dart';

class PopupPanel extends StatefulWidget {
  final Widget widget;
  final AppBar? appBar;
  final List<Widget>? footerButtons;

  const PopupPanel(this.widget, {super.key, this.appBar, this.footerButtons});
  @override
  State<StatefulWidget> createState() => _PopupState();
}

class _PopupState extends State<PopupPanel> {
  @override
  Widget build(BuildContext context) {
    return widget.widget;
  }
}

class CustomPopupPanel extends StatefulWidget {
  final String title;
  final Widget? widget;
  final Function()? onConfirm;
  final double? heightFraction; // 屏幕高度的比例，如 0.5 表示一半高度，0.33 表示三分之一高度
  const CustomPopupPanel(
      {required this.title,
      required this.widget,
      this.onConfirm,
      this.heightFraction = 0.5, // 默认为屏幕的一半高度
      super.key});

  @override
  State<StatefulWidget> createState() => _CustomPopupState();
}

class _CustomPopupState extends State<CustomPopupPanel> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerTheme: DividerThemeData(
            color: Colors.transparent,
          ),
        ),
        child: Scaffold(
          appBar: AppBar(
            backgroundColor: MColor.xFFF5F5F5,
            title: Text(widget.title, style: TextStyle(height: 1.4, fontSize: 16, color: MColor.xFF1B1C1A)),
            scrolledUnderElevation: 0,
            centerTitle: true,
            automaticallyImplyLeading: false,
            actions: [
              IconButton(
                  onPressed: () {
                    RouterHelper.router.pop();
                  },
                  icon: Icon(Icons.close, size: 22, color: MColor.xFF999999))
            ],
          ),
          backgroundColor: MColor.xFFF5F5F5,
          persistentFooterButtons: widget.onConfirm != null
              ? [
                  SubmitButton(
                    onTap: () async {
                      await widget.onConfirm?.call();
                      RouterHelper.router.pop();
                    },
                  )
                ]
              : null,
          body: SafeArea(
            child: Container(
              // color: Colors.yellow,
              padding: EdgeInsets.all(14),
              child: SingleChildScrollView(
                child: Column(
                  children: [if (widget.widget != null) widget.widget!],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
