import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:qiazhun/common/storage_util.dart';
import 'package:qiazhun/models/user_model.dart';
import 'package:qiazhun/modules/mine_tab/user_repo.dart';
import 'package:qiazhun/modules/search/search_history_model.dart';
import 'package:qiazhun/tools/tools.dart';

class UserStore extends GetxController {
  static UserStore get to => Get.find();

  UserInfo? currentUser;
  UserInfoResp? userInfoResp;
  String token = '';

  String? lastIndexBookkeepingNumber;
  String? lastStaticisticsBookkeepingNumberArr;

  List<SearchHistoryModel> searchHistories = [];

  @override
  void onInit() {
    try {
      currentUser = UserInfo.fromJson(json.decode(StorageUtil.getString(StorageKey.USER_INFO)));
    } catch (e) {}
    loadSearchHistory();

    super.onInit();
  }

  @override
  void onReady() {
    if (isUserLogin()) {
      getUserInfo();
    }

    super.onReady();
  }

  void loadSearchHistory() {
    String history = StorageUtil.getString('search_history');
    if (history.isNotEmpty) {
      searchHistories.clear();
      searchHistories = (json.decode(history) as List<dynamic>).map((dynamic e) => SearchHistoryModel.fromJson(e)).toList();
    }
    update(['search_history']);
  }

  void updateSearchHistory(String query) {
    searchHistories.removeWhere((el) => el.query == query);
    searchHistories.insert(0, SearchHistoryModel(query, DateTime.now().millisecondsSinceEpoch));
    StorageUtil.setString('search_history', json.encode(searchHistories));
    update(['search_history']);
  }

  void clearSearchHistory() {
    searchHistories.clear();
    StorageUtil.setString('search_history', json.encode(searchHistories));
    update(['search_history']);
  }

  void logout() {
    StorageUtil.setString(StorageKey.USER_INFO, '');
    currentUser = null;
    userInfoResp = null;
    token = '';
    lastIndexBookkeepingNumber = '';
    lastStaticisticsBookkeepingNumberArr = '';
    update();
  }

  String? getToken() {
    return currentUser?.token;
    // return 'ca33711e-1a71-4e30-97de-8776f51406d0';
  }

  bool isUserLogin() {
    return getToken()?.isNotEmpty == true;
  }

  void saveUserInfo(UserInfo userInfo) {
    currentUser = userInfo;
    StorageUtil.setString(StorageKey.USER_INFO, json.encode(userInfo.toJson()));
    update();
  }

  Future<UserInfoResp?> getUserInfo() async {
    try {
      var resp = await UserRepo.getUserInfo();
      if (resp.code == 1) {
        lastIndexBookkeepingNumber = resp.data?.lastIndexBookkeepingNumber;
        lastStaticisticsBookkeepingNumberArr = resp.data?.lastStaticisticsBookkeepingNumberArr;
        userInfoResp = resp.data;
        return Future.value(resp.data);
      } else {
        return Future.value(null);
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace, maxFrames: 5);
      logger.i('UserStore getUserInfo error $e');
      return Future.value(null);
    } finally {
      update();
    }
  }
}
