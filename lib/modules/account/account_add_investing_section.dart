import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_add_page.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class AccountAddInvestingSection extends StatefulWidget {
  final AccountAddController? addController;
  final AccountModel? accountModel;

  const AccountAddInvestingSection({super.key, this.addController, this.accountModel});
  @override
  State<StatefulWidget> createState() => _AccountAddInvestingState();
}

class _AccountAddInvestingState extends State<AccountAddInvestingSection> {
  bool _inAssets = true;
  final TextEditingController _balanceController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _profitController = TextEditingController();
  final TextEditingController _remarkController = TextEditingController();

  @override
  void initState() {
    widget.addController?.onAction = _addAccount;
    if (widget.accountModel != null) {
      _nameController.text = widget.accountModel!.accountName ?? '';
      _balanceController.text = widget.accountModel!.balance ?? '';
      _profitController.text = widget.accountModel!.amount ?? '';
      _remarkController.text = widget.accountModel!.memo ?? '';
      _inAssets = widget.accountModel!.isJoinTotal == 1;
    }
    super.initState();
  }

  Future<void> _addAccount() async {
    if (_balanceController.text.isEmpty) {
      showToast('请填写余额');
      return;
    }
    if (_nameController.text.isEmpty) {
      showToast('请填写账号名称');
      return;
    }
    if (_profitController.text.isEmpty) {
      showToast('请填写收益');
      return;
    }

    Loading.show();
    try {
      var resp = await AccountRepo.addSavingCard(
          id: widget.accountModel?.id,
          accountType: '5',
          balance: _balanceController.text,
          isJoinTotal: _inAssets ? '1' : '2',
          accountName: _nameController.text,
          accountIncome: _profitController.text,
          memo: _remarkController.text);
      if (resp.code == 1) {
        showToast('添加账号成功');
        RouterHelper.router.pop();
      } else {
        showToast(resp.msg ?? '添加账号失败');
      }
    } catch (e) {
      showToast('添加账号失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14, vertical: 0),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
      child: Column(children: [
        ListTile(
          dense: true,
          contentPadding: EdgeInsets.zero,
          leading: Text(
            '账户名称',
            style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
          ),
          trailing: Container(
            width: 200,
            // color: Colors.blue,
            child: TextField(
              controller: _nameController,
              keyboardType: TextInputType.text,
              textAlign: TextAlign.end,
              decoration: InputDecoration(
                hintText: '请输入',
                hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                border: OutlineInputBorder(borderSide: BorderSide.none),
                filled: true,
                fillColor: Colors.transparent,
                contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                isDense: true,
              ),
            ),
          ),
        ),
        Divider(
          height: 0.5,
          thickness: 0.5,
          color: MColor.xFFF5F5F5,
        ),
        ListTile(
          dense: true,
          contentPadding: EdgeInsets.zero,
          leading: Text(
            '余额',
            style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
          ),
          trailing: Container(
            width: 200,
            // color: Colors.blue,
            child: TextField(
              controller: _balanceController,
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()],
              textAlign: TextAlign.end,
              decoration: InputDecoration(
                hintText: '请输入',
                hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                border: OutlineInputBorder(borderSide: BorderSide.none),
                filled: true,
                fillColor: Colors.transparent,
                contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                isDense: true,
              ),
            ),
          ),
        ),
        Divider(
          height: 0.5,
          thickness: 0.5,
          color: MColor.xFFF5F5F5,
        ),
        ListTile(
          dense: true,
          contentPadding: EdgeInsets.zero,
          leading: Text(
            '收益（0-100百分比/年）',
            style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
          ),
          trailing: Container(
            width: 200,
            // color: Colors.blue,
            child: TextField(
              controller: _profitController,
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()],
              textAlign: TextAlign.end,
              decoration: InputDecoration(
                hintText: '请输入百分比',
                hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                border: OutlineInputBorder(borderSide: BorderSide.none),
                filled: true,
                fillColor: Colors.transparent,
                contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                isDense: true,
              ),
            ),
          ),
        ),
        Divider(
          height: 0.5,
          thickness: 0.5,
          color: MColor.xFFF5F5F5,
        ),
        ListTile(
          dense: true,
          contentPadding: EdgeInsets.zero,
          leading: Text(
            '备注',
            style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
          ),
          trailing: Container(
            width: 200,
            // color: Colors.blue,
            child: TextField(
              controller: _remarkController,
              keyboardType: TextInputType.text,
              textAlign: TextAlign.end,
              decoration: InputDecoration(
                hintText: '请输入',
                hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                border: OutlineInputBorder(borderSide: BorderSide.none),
                filled: true,
                fillColor: Colors.transparent,
                contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                isDense: true,
              ),
            ),
          ),
        ),
        Divider(
          height: 0.5,
          thickness: 0.5,
          color: MColor.xFFF5F5F5,
        ),
        ListTile(
          dense: true,
          contentPadding: EdgeInsets.zero,
          leading: Text(
            '是否计入资产',
            style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
          ),
          trailing: SizedBox(
            height: 24,
            child: Switch(
              value: _inAssets,
              activeTrackColor: MColor.skin,
              activeColor: MColor.xFFFFFFFF,
              onChanged: (value) {
                setState(() {
                  _inAssets = !_inAssets;
                });
              },
            ),
          ),
        ),
      ]),
    );
  }
}
