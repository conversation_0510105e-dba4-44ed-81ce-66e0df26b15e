import 'package:get/get.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/settings/setting_model.dart';
import 'package:qiazhun/modules/settings/setting_repo.dart';
import 'package:qiazhun/tools/tools.dart';

class SettingStore extends GetxController {
  static SettingStore get to => Get.find();

  final List<IconInfo> accountIconList = [];
  final List<IconInfo> flowingWaterIconList = [];
  final List<IconInfo> planIconList = [];

  @override
  void onInit() {
    super.onInit();
    getIconList();
  }

  Future<void> getIconList() async {
    try {
      var resp = await SettingRepo.getIconList();
      if (resp.code == 1) {
        accountIconList.clear();
        accountIconList.addAll(resp.data?.accountIconList ?? []);
        flowingWaterIconList.clear();
        flowingWaterIconList.addAll(resp.data?.flowingWaterIconList ?? []);
        planIconList.clear();
        planIconList.addAll(resp.data?.planIconList ?? []);
        update(['icon_list']);
      }
    } catch (e) {
      logger.e('SettingStore getIcons error $e');
    }
  }
}
