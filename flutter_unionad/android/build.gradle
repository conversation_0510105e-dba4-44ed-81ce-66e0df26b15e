group 'com.gstory.flutter_unionad'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.6.10'
    repositories {
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:4.1.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
        flatDir {
            dirs project(':flutter_unionad').file('libs')
        }
        maven {
            url 'https://artifact.bytedance.com/repository/pangle'
        }
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    compileSdkVersion 31
    namespace = "com.gstory.flutter_unionad"

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    defaultConfig {
        minSdkVersion 16
        consumerProguardFiles 'proguard-rules.pro'
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
    jvmTarget = 17
    }
    lintOptions {
        disable 'InvalidPackage'
    }

}

repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "androidx.appcompat:appcompat:1.3.1"
//    implementation 'com.pangle.cn:ads-sdk:+'
//    implementation 'com.pangle.cn:ads-sdk-pro:6.4.1.5'
    implementation(name: 'open_ad_sdk_6.4.1.5', ext: 'aar')
//    implementation(name: 'tools_release', ext: 'aar')

//    implementation "com.pangle.cn:mediation-sdk:6.4.0.9"  //融合SDK
//    implementation "com.pangle.cn:mediation-ks-adapter:3.3.67.0"//ks adapter
//    implementation "com.pangle.cn:mediation-gdt-adapter:4.591.1461.1" //gdt adapter
//    implementation "com.pangle.cn:mediation-admob-adapter:17.2.0.62"//admob adapter
//    implementation "com.pangle.cn:mediation-baidu-adapter:9.36.2"//baidu adapter
//    implementation "com.pangle.cn:mediation-klevin-adapter:2.11.0.3.23"//游可赢 adapter
//    implementation "com.pangle.cn:mediation-mintegral-adapter:16.5.57.5"//mintegral adapter
//    implementation "com.pangle.cn:mediation-sigmob-adapter:4.19.4.0"//sigmob adapter
//    // wind-sdk 和 common版本必须匹配使用，不然可能存在兼容性问题
//    implementation "com.pangle.cn:mediation-unity-adapter:4.3.0.29"//unity adapter
}
