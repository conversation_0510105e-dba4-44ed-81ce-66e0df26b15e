import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

class RemarkPanelController extends GetxController {
  final TextEditingController remarkController = TextEditingController();
  final FocusNode remarkNode = FocusNode();

  final String remarkStr;

  RemarkPanelController(this.remarkStr);
  @override
  void onInit() {
    super.onInit();
    remarkController.text = remarkStr;
  }

  @override
  void dispose() {
    remarkController.dispose();
    remarkNode.dispose();
    super.dispose();
  }
}
