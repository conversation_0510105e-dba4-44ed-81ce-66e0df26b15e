import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/section_header.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/modules/plan_tab/plan_model.dart';
import 'package:qiazhun/modules/settings/setting_model.dart';
import 'package:qiazhun/modules/settings/setting_store.dart';
import 'package:qiazhun/providers/bookkeeping_list_provider.dart';
import 'package:qiazhun/repo/plan_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/page_container_view.dart';
import 'package:qiazhun/widgets/round_image.dart';

class PlanEditPage extends ConsumerStatefulWidget {
  final String planId;
  const PlanEditPage({required this.planId, super.key});

  @override
  ConsumerState<PlanEditPage> createState() => _PlanEditState();
}

class _PlanEditState extends ConsumerState<PlanEditPage> {
  String? _iconInfo = SettingStore.to.planIconList.firstOrNull?.icon;

  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _requiredController = TextEditingController(text: '0.00');
  final TextEditingController _alreadyController = TextEditingController(text: '0.00');
  final TextEditingController _remarkController = TextEditingController();
  final TextEditingController _ruleAmountController = TextEditingController();
  final TextEditingController _timesController = TextEditingController();
  final TextEditingController _proportionController = TextEditingController();
  final _timeCycles = ['日', '周', '月', '年'];

  bool _isRealized = false;

  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');
  DateTime? _beginDate;
  DateTime? _endDate;

  String? _backgroundUrl;

  String _storeType = '1';
  PlanStoreRule? _planStoreRule;

  List<BookkeepingInfo> _selectedBookkeepings = [];

  @override
  void initState() {
    _getData();
    super.initState();
  }

  @override
  void didUpdateWidget(covariant PlanEditPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    _sortBookkeepings();
  }

  Future<void> _getData() async {
    if (widget.planId.isNotEmpty == true && widget.planId != '0') {
      Loading.show();
      try {
        var resp = await PlanRepo.getPlanDetail(widget.planId!);
        if (resp.code == 1) {
          PlanInfo? planInfo = resp.data?.detail;
          if (planInfo != null) {
            _nameController.text = planInfo.name;
            _backgroundUrl = planInfo.backgroundImage;
            _iconInfo = planInfo.icon;
            _requiredController.text = planInfo.requiredAmount ?? '0.00';
            _alreadyController.text = planInfo.accumulatedAmount ?? '0.00';
            _selectedBookkeepings = planInfo.accountBookkeepingNumberArr ?? [];
            _beginDate = DateTime.tryParse(planInfo.startDate ?? '');
            _endDate = DateTime.tryParse(planInfo.expectedAchieveDate ?? '');
            _remarkController.text = planInfo.memo ?? '';
            _storeType = planInfo.storageMethod ?? '1';
            _planStoreRule = planInfo.rule;
            _timesController.text = _planStoreRule?.times ?? '';
            _ruleAmountController.text = _planStoreRule?.price ?? '';
            _proportionController.text = _planStoreRule?.proportion ?? '';
            _isRealized = planInfo.status == 2 ? true : false;
            _sortBookkeepings();
            setState(() {});
          }
        } else {
          showToast(resp.msg ?? '');
        }
      } catch (e) {
        showToast(e.toString());
      } finally {
        Loading.dismiss();
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _requiredController.dispose();
    _alreadyController.dispose();
    _remarkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
      title: '编辑计划',
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 14.0),
          child: Column(
            children: [
              _nameSection,
              const SizedBox(
                height: 14,
              ),
              _iconSection,
              const SizedBox(
                height: 14,
              ),
              _amountRequiredSection,
              const SizedBox(
                height: 14,
              ),
              _amountSavedSection,
              const SizedBox(
                height: 14,
              ),
              _beginSection,
              const SizedBox(
                height: 14,
              ),
              _expireSection,
              const SizedBox(
                height: 14,
              ),
              _savingMethodSection,
              const SizedBox(
                height: 14,
              ),
              _bookkeepingSection,
              const SizedBox(
                height: 14,
              ),
              _remarkSection,
              const SizedBox(
                height: 14,
              ),
              _realizedSection,
              const SizedBox(
                height: 14,
              ),
            ],
          ),
        ),
      ),
      footerButtons: [_submitButton],
    );
  }

  Widget get _nameSection {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionHeader('计划名称'),
        const SizedBox(
          height: 14,
        ),
        _inputTextView(controller: _nameController, hintText: '请输入30个汉字内计划名称', keyboardType: TextInputType.text),
        const SizedBox(
          height: 14,
        ),
        if (_backgroundUrl == null)
          Container(
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: MColor.xFFFFFFFF),
            height: 80,
            width: 80,
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                showUploadDialog(context, (filePath) {
                  _backgroundUrl = filePath;
                  setState(() {});
                }, (errorMsg) {
                  showToast(errorMsg ?? '上传失败');
                });
              },
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.add_outlined,
                    color: MColor.xFF999999,
                    size: 24,
                  ),
                  const SizedBox(
                    height: 8,
                  ),
                  Text(
                    '上传背景图',
                    style: TextStyle(height: 1.4, fontSize: 14, color: MColor.xFF999999),
                  )
                ],
              ),
            ),
          )
        else
          SizedBox(
            height: 80,
            width: 80,
            child: Stack(
              children: [
                CachedNetworkImage(
                  imageUrl: getImageUrl(_backgroundUrl),
                  height: 80,
                  width: 80,
                ),
                Positioned(
                    top: 0,
                    right: 0,
                    left: 0,
                    bottom: 0,
                    child: IconButton(
                        onPressed: () {
                          setState(() {
                            _backgroundUrl = null;
                          });
                        },
                        icon: Icon(Icons.do_not_disturb_on_outlined, color: MColor.xFFCB322E, size: 20)))
              ],
            ),
          )
      ],
    );
  }

  Widget get _iconSection {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionHeader('愿望图标'),
        const SizedBox(
          height: 14,
        ),
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            RouterHelper.router.pushNamed(Routes.iconListPath, extra: {'iconList': SettingStore.to.planIconList}).then((value) {
              if (value != null && value is Map<String, IconInfo> && value.containsKey('result')) {
                setState(() {
                  _iconInfo = value['result']?.icon;
                });
              }
            });
          },
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: CachedNetworkImage(
                imageUrl: getImageUrl(_iconInfo ?? ''),
                width: 36,
                height: 36,
                fit: BoxFit.cover,
                placeholder: (ctx, e) {
                  return Container(
                    decoration: BoxDecoration(
                      color: MColor.xFFECECEC,
                    ),
                  );
                },
                errorWidget: (ctx, e, x) {
                  return Image.asset(
                    'assets/images/ic_default_ledger.png',
                    width: 40,
                    height: 40,
                    fit: BoxFit.fill,
                  );
                }),
          ),
        )
      ],
    );
  }

  Widget get _amountRequiredSection {
    return Column(
      children: [
        SectionHeader('所需金额'),
        const SizedBox(
          height: 14,
        ),
        _inputTextView(controller: _requiredController, hintText: '所需金额', inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))]),
      ],
    );
  }

  Widget get _amountSavedSection {
    return Column(
      children: [
        SectionHeader('已攒金额'),
        const SizedBox(
          height: 14,
        ),
        _inputTextView(controller: _alreadyController, hintText: '已攒金额', inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))]),
      ],
    );
  }

  Widget get _beginSection {
    return Column(
      children: [
        SectionHeader('开始日期'),
        const SizedBox(
          height: 14,
        ),
        GestureDetector(
          onTap: () {
            showDatePicker(locale: Locale('zh', 'CN'), context: context, currentDate: _beginDate, firstDate: DateTime(2020, 1, 1), lastDate: DateTime.now())
                .then((date) {
              if (date != null) {
                setState(
                  () {
                    _beginDate = date;
                  },
                );
              }
            });
          },
          child: Container(
            height: 50,
            padding: EdgeInsets.symmetric(horizontal: 14),
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: MColor.xFFFFFFFF),
            child: Row(
              children: [
                Text(
                  _beginDate == null ? '' : _dateFormat.format(_beginDate!),
                  style: TextStyle(fontSize: 14, height: 1.4, color: MColor.xFF999999),
                ),
                const Spacer(),
                Image.asset('assets/images/ic_calendar.png', width: 16, height: 16)
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget get _expireSection {
    return Column(
      children: [
        SectionHeader('预计达成'),
        const SizedBox(
          height: 14,
        ),
        GestureDetector(
          onTap: () {
            showDatePicker(locale: Locale('zh', 'CN'), context: context, currentDate: _endDate, firstDate: DateTime(2020, 1, 1), lastDate: DateTime(3000, 1, 1))
                .then((date) {
              if (date != null) {
                setState(
                  () {
                    _endDate = date;
                  },
                );
              }
            });
          },
          child: Container(
            height: 50,
            padding: EdgeInsets.symmetric(horizontal: 14),
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: MColor.xFFFFFFFF),
            child: Row(
              children: [
                Text(
                  _endDate == null ? '' : _dateFormat.format(_endDate!),
                  style: TextStyle(fontSize: 14, height: 1.4, color: MColor.xFF999999),
                ),
                const Spacer(),
                Image.asset('assets/images/ic_calendar.png', width: 16, height: 16)
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget get _savingMethodSection {
    List<String> methodStr = ['手动输入', '自动存入', '收入存入', '节省存入'];
    List<Widget> widgets = List.generate(methodStr.length, (index) {
      bool isSelected = '${index + 1}' == _storeType;
      return Expanded(
          child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          _storeType = '${index + 1}';
          setState(() {});
        },
        child: Row(
          children: [
            Icon(isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked, size: 16, color: isSelected ? MColor.skin : MColor.xFF999999),
            const SizedBox(
              width: 6,
            ),
            Text(
              methodStr[index],
              style: TextStyle(height: 1.4, fontSize: 14, color: isSelected ? MColor.xFF333333 : MColor.xFF999999),
            )
          ],
        ),
      ));
    });
    return Column(
      children: [
        SectionHeader('存储方式'),
        const SizedBox(
          height: 14,
        ),
        Row(
          children: widgets,
        ),
        if (_storeType == '2') ...{
          const SizedBox(
            height: 14,
          ),
          SectionHeader('存储周期'),
          const SizedBox(
            height: 14,
          ),
          Row(
            children: [
              Text(
                '次数',
                style: TextStyle(height: 1.4, fontSize: 14, color: MColor.xFF1B1C1A, fontWeight: FontWeight.w500),
              ),
              const SizedBox(
                width: 12,
              ),
              SizedBox(
                width: 70,
                child: TextField(
                  controller: _timesController,
                  style: TextStyle(fontSize: 12, color: MColor.xFF777777, height: 1.4),
                  textAlign: TextAlign.end,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))],
                  decoration: InputDecoration(
                    hintText: '输入数字',
                    hintStyle: TextStyle(fontSize: 12, color: MColor.xFF777777, height: 1.4),
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: BorderSide(width: 0.5, color: MColor.xFF999999)),
                    enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: BorderSide(width: 0.5, color: MColor.xFF999999)),
                    focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: BorderSide(width: 0.5, color: MColor.skin)),
                    filled: true,
                    fillColor: MColor.xFFF5F5F5,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3.5),
                    isDense: true,
                  ),
                  onChanged: (value) {
                    if (_planStoreRule == null) {
                      _planStoreRule = PlanStoreRule();
                    }
                    _planStoreRule!.times = value;
                  },
                ),
              ),
              const SizedBox(
                width: 7,
              ),
              Text(
                '/',
                style: TextStyle(height: 1.4, fontSize: 14, color: MColor.xFF999999),
              ),
              const SizedBox(
                width: 7,
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 7.5, vertical: 3.5),
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(4), border: Border.all(color: MColor.xFF999999, width: 0.5)),
                child: GestureDetector(
                  onTap: () {
                    List<Widget> actions = List.generate(_timeCycles.length, (index) {
                      return CupertinoActionSheetAction(
                        onPressed: () {
                          Navigator.of(context).pop();
                          if (_planStoreRule == null) {
                            _planStoreRule = PlanStoreRule();
                          }
                          _planStoreRule!.cycle = index + 1;
                          setState(() {});
                        },
                        child: Text(
                          _timeCycles[index],
                          style: TextStyle(fontSize: 16.0, color: MColor.xFF333333),
                        ),
                      );
                    });
                    showCupertinoModalPopup(
                        context: context,
                        builder: (context) {
                          return CupertinoActionSheet(
                            cancelButton: CupertinoActionSheetAction(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              child: Text('取消', style: TextStyle(fontSize: 16.0, color: MColor.xFF333333, fontWeight: FontWeight.bold)),
                            ),
                            actions: actions,
                          );
                        });
                  },
                  child: Row(
                    children: [
                      Text(
                        _planStoreRule?.cycle == null ? _timeCycles[0] : _timeCycles[_planStoreRule!.cycle! - 1],
                        style: TextStyle(height: 1.4, fontSize: 12, fontWeight: FontWeight.w500, color: MColor.xFF999999),
                      ),
                      Icon(
                        Icons.keyboard_arrow_down,
                        size: 12,
                        color: MColor.xFF999999,
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
          const SizedBox(
            height: 14,
          ),
          SectionHeader('存储金额'),
          const SizedBox(
            height: 14,
          ),
          _inputTextView(
            controller: _ruleAmountController,
            hintText: '存储金额',
            inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
            onChanged: (p0) {
              _planStoreRule ??= PlanStoreRule();
              _planStoreRule!.price = p0;
            },
          )
        } else if (_storeType == '3') ...{
          const SizedBox(
            height: 14,
          ),
          SectionHeader('存储比例'),
          const SizedBox(
            height: 14,
          ),
          _inputTextView(
            controller: _proportionController,
            hintText: '请输入存储比例(%)',
            inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()],
            onChanged: (p0) {
              _planStoreRule ??= PlanStoreRule();
              _planStoreRule!.proportion = p0;
            },
          )
        }
      ],
    );
  }

  late List<BookkeepingInfo> _sortedBookkeepings = [];

  void _sortBookkeepings() {
    _sortedBookkeepings = BookkeepingStore.to.bookkeepingList.toList();
    _sortedBookkeepings.sort((BookkeepingInfo bk0, BookkeepingInfo bk1) {
      bool selected0 = _selectedBookkeepings.any((el) => el.bookkeepingNumber == bk0.bookkeepingNumber) ?? false;
      bool selected1 = _selectedBookkeepings.any((el) => el.bookkeepingNumber == bk1.bookkeepingNumber) ?? false;
      if (selected0 && selected1) {
        return bk0.bookkeepingNumber?.compareTo(bk1.bookkeepingNumber ?? '') ?? 1;
      } else if (!selected0 && !selected1) {
        return bk0.bookkeepingNumber?.compareTo(bk1.bookkeepingNumber ?? '') ?? 1;
      } else if (selected0) {
        return -1;
      } else {
        return 1;
      }
    });
  }

  Widget get _bookkeepingSection {
    List<Widget> bookkeepingList = _sortedBookkeepings
        .map((account) => GestureDetector(
              onTap: () {
                bool isSelected = _selectedBookkeepings.any((el) => el.bookkeepingNumber == account.bookkeepingNumber) ?? false;
                if (isSelected) {
                  _selectedBookkeepings.removeWhere((el) => el.bookkeepingNumber == account.bookkeepingNumber);
                  _sortBookkeepings();
                  setState(() {});
                } else {
                  if (account.bookkeepingNumber?.isNotEmpty == true) {
                    _selectedBookkeepings.add(account);

                    _sortBookkeepings();
                    setState(() {});
                  }
                }
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _selectedBookkeepings.any((el) => el.bookkeepingNumber == account.bookkeepingNumber) ?? false ? MColor.xFFF5F5F5 : Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: MColor.xFFF0F0F0),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    RoundImage(
                      imageUrl: account.bookkeepingIcon ?? '',
                      size: 20,
                      radius: 10,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      account.accountBookName ?? '',
                      style: TextStyle(
                        height: 1.4,
                        fontSize: 14,
                        color: MColor.xFF1B1C1A,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      _selectedBookkeepings.any((el) => el.bookkeepingNumber == account.bookkeepingNumber) ?? false ? Icons.close : Icons.add,
                      size: 16,
                      color: MColor.xFF999999,
                    ),
                  ],
                ),
              ),
            ))
        .toList();
    return Column(
      children: [
        SectionHeader(
          '展示账本',
        ),
        const SizedBox(
          height: 14,
        ),
        Row(
          children: [
            Expanded(
              child: Wrap(
                direction: Axis.horizontal,
                alignment: WrapAlignment.start,
                spacing: 14.0,
                runSpacing: 8.0,
                children: bookkeepingList,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget get _remarkSection {
    return Column(
      children: [
        SectionHeader('备注'),
        const SizedBox(
          height: 14,
        ),
        _inputTextView(controller: _remarkController, hintText: '请输入备注内容', keyboardType: TextInputType.text),
      ],
    );
  }

  Widget get _realizedSection {
    return Container(
      height: 50,
      padding: EdgeInsets.symmetric(horizontal: 14),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: MColor.xFFFFFFFF),
      child: ListTile(
        dense: true,
        contentPadding: EdgeInsets.zero,
        leading: Text(
          '是否实现',
          style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A, fontWeight: FontWeight.bold),
        ),
        trailing: SizedBox(
          height: 24,
          child: Switch(
            value: _isRealized,
            activeTrackColor: MColor.skin,
            activeColor: MColor.xFFFFFFFF,
            onChanged: (value) {
              setState(() {
                _isRealized = !_isRealized;
              });
            },
          ),
        ),
      ),
    );
  }

  Widget _inputTextView(
      {required TextEditingController controller,
      required String hintText,
      TextInputType? keyboardType,
      List<TextInputFormatter>? inputFormatters,
      Function(String)? onChanged,
      int? maxLength}) {
    return TextField(
      controller: controller,
      keyboardType: keyboardType ?? TextInputType.numberWithOptions(decimal: true),
      inputFormatters: inputFormatters,
      decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 15),
          isDense: true,
          counterText: ''),
      maxLength: maxLength,
      onChanged: (value) {
        onChanged?.call(value);
      },
    );
  }

  Widget get _submitButton {
    return GestureDetector(
      onTap: () async {
        if (_nameController.text.isEmpty) {
          showToast('请填写计划名称');

          return;
        }
        if (_backgroundUrl?.isNotEmpty != true) {
          showToast('请选择背景图');
          return;
        }
        if (_iconInfo?.isNotEmpty != true) {
          showToast('请选择愿望图标');
          return;
        }
        if (_requiredController.text.isEmpty) {
          showToast('请填写所需金额');
          return;
        }
        if (_beginDate == null) {
          showToast('请选择开始日期');
          return;
        }
        if (_endDate == null) {
          showToast('请选择预计达成日期');
          return;
        }
        if (_storeType == '2') {
          if (_timesController.text.isEmpty) {
            showToast('请填写存储次数');
            return;
          }
          if ((NumberFormat().tryParse(_timesController.text) ?? 0) <= 0) {
            showToast('存储次数不能为0或空');
            return;
          }
          if (_ruleAmountController.text.isEmpty) {
            showToast('请填写存储金额');
            return;
          }
          if ((NumberFormat().tryParse(_ruleAmountController.text) ?? 0) <= 0) {
            showToast('存储金额不能为0或空');
            return;
          }
        } else if (_storeType == '3') {
          if (_proportionController.text.isEmpty) {
            showToast('请填写存储比例');
            return;
          }
        }
        // if (_selectedBookkeepings.isEmpty) {
        //   showToast('请选择账本');
        //   return;
        // }
        Loading.show();
        try {
          List<String> idList = List.generate(_selectedBookkeepings.length, (index) {
            return _selectedBookkeepings[index].bookkeepingNumber!;
          });
          var resp = await PlanRepo.addPlan(
              planId: widget.planId,
              name: _nameController.text,
              planIcon: _iconInfo!,
              background: _backgroundUrl,
              alreadyAmount: _alreadyController.text.isEmpty ? '0.00' : _alreadyController.text,
              requiredAmount: _requiredController.text,
              remark: _remarkController.text,
              bookkeepingNumbers: idList.join(','),
              storeType: _storeType,
              rule: _planStoreRule,
              status: _isRealized ? '2' : '1',
              startDate: _dateFormat.format(_beginDate!),
              endDate: _dateFormat.format(_endDate!));
          if (resp.code == 1) {
            showToast('成功');
            RouterHelper.router.pop();
          } else {
            showToast(resp.msg ?? '失败');
          }
        } catch (e, stackTrace) {
          debugPrintStack(stackTrace: stackTrace, maxFrames: 5);
          showToast('失败 $e');
        } finally {
          Loading.dismiss();
        }

        // _submit();
      },
      child: Container(
        height: 50,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            gradient:
                LinearGradient(begin: const FractionalOffset(0.0, 0.0), end: const FractionalOffset(0.0, 1.0), colors: [Color(0xFF68C2BF), Color(0xFF4C9B93)])),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '提交',
              style: TextStyle(color: MColor.xFFFFFFFF, fontWeight: FontWeight.w500, fontSize: 16, height: 1),
            ),
          ],
        ),
      ),
    );
  }
}
