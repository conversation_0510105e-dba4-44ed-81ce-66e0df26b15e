import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:qiazhun/constants/design.dart';
import 'dart:ui' as ui;

import 'package:qiazhun/router/router.dart';

var logger = Logger(
  printer: SimplePrinter(printTime: true),
);

var loggerNoStack = Logger(
  printer: PrettyPrinter(methodCount: 0),
);

void showToast(String message, {bool short = true, ToastGravity gravity = ToastGravity.CENTER}) {
  Fluttertoast.showToast(
    msg: message,
    toastLength: short ? Toast.LENGTH_SHORT : Toast.LENGTH_LONG,
    gravity: gravity,
    timeInSecForIosWeb: 2,
    textColor: Colors.white,
    backgroundColor: MColor.skin,
  );
}

double getDefaultAppbarHeight() {
  return AppBar().preferredSize.height;
}

String formatUtcTime(String? inTime, String format, {String? toFormat}) {
  if (inTime?.isNotEmpty == false) {
    return '';
  }
  try {
    DateFormat df = DateFormat(format);
    DateTime dt = df.parseUTC(inTime!);
    if (toFormat != null) {
      df = DateFormat(toFormat);
      return df.format(dt.toLocal());
    } else {
      return df.format(dt.toLocal());
    }
  } catch (_) {
    return '';
  }
}

// String formatElapsedTime(BuildContext context, Duration duration) {
//   String twoDigits(int n, {int pad = 2}) => n.toString().padLeft(pad, '0');
//   var elapsedTime = '';
//   if (duration.inDays != 0) {
//     final days = twoDigits(duration.inDays, pad: 1);
//     elapsedTime = '$days${S.of(context).day}';
//   }
//   if (duration.inHours != 0) {
//     final hours = twoDigits(duration.inHours.remainder(24));
//     elapsedTime = '$elapsedTime$hours${S.of(context).hour}';
//   }
//   if (duration.inMinutes != 0 || (duration.inMinutes == 0 && duration.inDays == 0 && duration.inHours == 0)) {
//     final minutes = twoDigits(duration.inMinutes.remainder(60));
//     elapsedTime = '$elapsedTime$minutes${S.of(context).minute}';
//   }
//   return elapsedTime;
// }

// String formatVolumn(String? volumn) {
//   if (volumn == null) return '';
//   double dVolumn = double.tryParse(volumn) ?? 0.0;
//   if (dVolumn >= 1000000) {
//     return '${(dVolumn / 1000000).toStringAsFixed(2)}M';
//   } else if (dVolumn >= 1000) {
//     return '${(dVolumn / 1000).toStringAsFixed(2)}K';
//   } else {
//     return dVolumn.toStringAsFixed(2);
//   }
// }

// String formatMoney(double? money) {
//   if (money == null) return '';
//   var formatter = NumberFormat.decimalPattern(SettingStore.to.getCurrentLocale().toString());
//   return formatter.format(money);
// }

Future<void> showCustomDialog(String text,
    {VoidCallback? onConfirm,
    TextEditingController? textController,
    TextInputType? textInputType,
    String? textPlaceHolder,
    bool cancel = false,
    bool dismissWhenConfirm = true,
    String? content,
    String? okTitle,
    VoidCallback? onCancel}) async {
  if (Get.isDialogOpen ?? false) {
    return;
  }
  return Get.generalDialog(
      navigatorKey: RouterHelper.parentNavigatorKey,
      barrierDismissible: false,
      pageBuilder: (ctx, a, s) {
        return Builder(builder: (context) {
          return WillPopScope(
              onWillPop: () async => false,
              child: CupertinoAlertDialog(
                content: Column(
                  children: [
                    Text(
                      text,
                      style: MFont.semi_Bold17.apply(color: MColor.xFF333333),
                    ),
                    if (content?.isNotEmpty == true) ...{
                      Container(
                        height: 4,
                      ),
                      Text(
                        content!,
                        style: MFont.semi_Bold14.apply(color: MColor.xFF777777),
                      )
                    }
                  ],
                ),
                actions: <Widget>[
                  if (cancel) ...{
                    TextButton(
                      onPressed: () {
                        RouterHelper.router.pop();
                        if (onCancel != null) {
                          onCancel();
                        }
                      },
                      child: Text(
                        '取消',
                        style: MFont.regular17.apply(color: MColor.xFF333333),
                      ),
                    ),
                  },
                  TextButton(
                    onPressed: () {
                      if (dismissWhenConfirm) {
                        RouterHelper.router.pop();
                      }
                      if (onConfirm != null) {
                        onConfirm();
                      }
                    },
                    child: Text(
                      '确定',
                      style: MFont.semi_Bold17.apply(color: MColor.xFF333333),
                    ),
                  ),
                ],
              ));
        });
      });
}

String calculatePriceChange(String? nowPrice, String? change) {
  if (nowPrice == null || change == null) {
    return '';
  }
  int numberOfDecimals = getNumberOfDecimals(nowPrice);
  double price = double.tryParse(nowPrice!) ?? 0.0;
  double cg = double.tryParse(change!) ?? 0.0;
  double changePrice = price * cg;
  return changePrice.toStringAsFixed(numberOfDecimals);
}

getNumberOfDecimals(String number) {
  if (number.contains('.')) {
    return number.split(".")[1].length;
  }
  return 0;
}

Size textSize(String text, TextStyle style) {
  final TextPainter textPainter = TextPainter(text: TextSpan(text: text, style: style), maxLines: 1, textDirection: ui.TextDirection.ltr)
    ..layout(minWidth: 0, maxWidth: double.infinity);
  return textPainter.size;
}

String generateDateRange(String rangeType) {
  //1 本周 2 本月 3 本年
  DateTime now = DateTime.now();
  DateTime _dateTimeStart;
  DateTime _dateTimeEnd;
  if (rangeType == '1') {
    _dateTimeStart = now.subtract(Duration(days: now.weekday - 1));
    _dateTimeEnd = _dateTimeStart.add(Duration(days: 6));
  } else if (rangeType == '2') {
    _dateTimeStart = DateTime(now.year, now.month, 1);
    _dateTimeEnd = DateTime(now.year, now.month + 1, 0);
  } else {
    _dateTimeStart = DateTime(now.year, 1, 1);
    _dateTimeEnd = DateTime(now.year + 1, 1, 0);
  }
  DateFormat dt = DateFormat('yyyy-MM-dd');
  String timeInterval = '';
  DateTime begin = DateTime(_dateTimeStart.year, _dateTimeStart.month, _dateTimeStart.day);
  DateTime end = DateTime(_dateTimeEnd.year, _dateTimeEnd.month, _dateTimeEnd.day);
  timeInterval = '${dt.format(begin)},${dt.format(end)}';
  return timeInterval;
}

String generateDateRange2(DateTime begin, DateTime end) {
  DateFormat dt = DateFormat('yyyy-MM-dd');
  String timeInterval = '';
  timeInterval = '${dt.format(begin)},${dt.format(end)}';
  return timeInterval;
}

String monthDateRange(DateTime day) {
  DateTime begin = DateTime(day.year, day.month, 1);
  DateTime end = DateTime(day.year, day.month + 1, 0);
  DateFormat dt = DateFormat('yyyy-MM-dd');
  String timeInterval = '';
  timeInterval = '${dt.format(begin)},${dt.format(end)}';
  return timeInterval;
}
