import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/modules/calendar/calendar_model.dart';

class CalendarRepo {
  static String dayFlowingWaterLog = 'api/detail/getDayFlowingWaterLog';
  static Future<BaseModel<CalendarResp>> getDayFlowingWaterLog({String? date, String? month}) async {
    var data = {};
    if (date?.isNotEmpty == true) {
      data['date'] = date;
    }
    if (month?.isNotEmpty == true) {
      data['month'] = month;
    }
    var resp = await HttpUtil().post(dayFlowingWaterLog, data: data);
    return BaseModel.fromJson(resp, (json) => CalendarResp.fromJson(json));
  }
}
