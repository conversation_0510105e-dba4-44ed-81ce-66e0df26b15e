import 'dart:io';

import 'package:flutter/material.dart';
import 'package:fluwx/fluwx.dart';
import 'package:get/get.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/modules/auth/auth_repo.dart';
import 'package:qiazhun/modules/mine_tab/user_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/store/privacy_store.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

abstract class WechatCallback {
  void onLoginSuccess({required String thirdUid, required String thirdData});
}

class ThirdLoginStore extends GetxController {
  static ThirdLoginStore get to => Get.find();

  final Fluwx fluwx = Fluwx();

  bool isWechatInstalled = false;
  bool _isFluWxInited = false;

  List<WechatCallback> wechatCallbacks = [];

  @override
  void onInit() {
    logger.i('ThirdLoginStore init');
    if (PrivacyStore.to.isPrivacyAgreed() || Platform.isIOS) {
      initFluwx();
    }

    super.onInit();
  }

  void addWechatCallback(WechatCallback callback) {
    wechatCallbacks.add(callback);
  }

  void removeWechatCallback(WechatCallback callback) {
    wechatCallbacks.remove(callback);
  }

  Future<void> initFluwx() async {
    logger.i('initFluwx $_isFluWxInited');
    if (_isFluWxInited) {
      return;
    }
    // AppSecret:c746c4076bb59d95f8a6fa71420a5470
    await fluwx.registerApi(
        appId: 'wxfa8f65bc326d6783', //传入注册的应用id
        doOnAndroid: true, //在android上运行
        doOnIOS: true, // 在ios上运行
        universalLink: 'https://ji.qiazhun.com');
    // if (Platform.isAndroid) {
    // } else if (Platform.isIOS) {

    // }
    isWechatInstalled = await fluwx.isWeChatInstalled;
    update();
    if (isWechatInstalled) {
      fluwx.addSubscriber((response) {
        if (response is WeChatAuthResponse) {
          // setState(() {
          logger.i('state :${response.state} \n code:${response.code}');
          // });
          if (response.state == 'wechat_sdk_login' && response.code?.isNotEmpty == true) {
            _userLoginByWechat(response.code!);
          } else if (response.state == 'rebindWechat' && response.code?.isNotEmpty == true) {
            _userRebindWechat(response.code!);
          }
        }
      });
    }
    _isFluWxInited = true;
  }

  Future<void> _userLoginByWechat(String code) async {
    logger.i('ThirdLoginStore _userLoginByWechat $code');
    try {
      var resp = await AuthRepo.wxLogin(code);
      if (resp.code == 1) {
        if (resp.data?.userInfo.token?.isNotEmpty == true) {
          UserStore.to.saveUserInfo(resp.data!.userInfo);
        }
        if (resp.data?.mobileBindStatus == 1) {
          if (resp.data?.userInfo != null) {
            RouterHelper.router.go(Routes.detailTabPath);
          }
        } else if (resp.data?.mobileBindStatus == 0) {
          RouterHelper.router.pushNamed(Routes.verificationPath, extra: {'bindType': 0});
        }
        // for (var callback in wechatCallbacks) {
        //   callback.onLoginSuccess(thirdUid: uid, thirdData: json.encode(resp.data));
        // }
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e) {
      logger.e('_userLoginByWechat error $e');
    } finally {
      Loading.dismiss();
    }
  }

  Future<void> _userLoginByApple(String identityToken) async {
    logger.i('ThirdLoginStore _userLoginByApple $identityToken');
    try {
      var resp = await AuthRepo.appleLogin(identityToken);
      if (resp.code == 1) {
        if (resp.data?.userInfo.token?.isNotEmpty == true) {
          UserStore.to.saveUserInfo(resp.data!.userInfo);
        }
        if (resp.data?.mobileBindStatus == 1) {
          if (resp.data?.userInfo != null) {
            RouterHelper.router.go(Routes.detailTabPath);
          }
        } else if (resp.data?.mobileBindStatus == 0) {
          RouterHelper.router.pushNamed(Routes.verificationPath, extra: {'bindType': 0});
        }
        // for (var callback in wechatCallbacks) {
        //   callback.onLoginSuccess(thirdUid: uid, thirdData: json.encode(resp.data));
        // }
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace, maxFrames: 3);
      logger.e('_userLoginByApple error $e');
    } finally {
      Loading.dismiss();
    }
  }

  Future<void> _userRebindWechat(String code) async {
    Loading.show();
    logger.i('ThirdLoginStore _userRebindWechat $code');
    try {
      var resp = await AuthRepo.bindWechat(code);
      if (resp.code == 1) {
        await UserStore.to.getUserInfo();
        showToast('绑定成功');
      } else {
        showToast(resp.msg ?? '绑定失败');
      }
    } catch (e) {
      logger.e('_userRebindWechat error $e');
      showToast('绑定失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  Future<void> signInWithWechat() async {
    Loading.show();
    if (!_isFluWxInited) {
      await initFluwx();
    }
    fluwx.authBy(which: NormalAuth(scope: 'snsapi_userinfo', state: 'wechat_sdk_login')).then((_) {
      Loading.dismiss();
    });
  }

  Future<void> signInWithApple() async {
    final credential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );

    logger.i(credential);
    _userLoginByApple(credential.identityToken!);
  }

  void rebindWechat() {
    Loading.show();
    fluwx.authBy(which: NormalAuth(scope: 'snsapi_userinfo', state: 'rebindWechat')).then((_) {
      Loading.dismiss();
    });
  }
}
