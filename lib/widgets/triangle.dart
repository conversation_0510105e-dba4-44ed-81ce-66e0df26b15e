import 'package:flutter/material.dart';
import 'package:qiazhun/constants/design.dart';

class Triangle extends StatelessWidget {
  final double height;
  final double width;
  const Triangle({required this.height, required this.width, super.key});

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: <PERSON><PERSON><PERSON><PERSON>(),
      child: Container(
        height: height,
        width: width,
      ),
    );
  }
}

class TrianglePainter extends CustomPainter {
  final Color strokeColor;
  final PaintingStyle paintingStyle;
  final double strokeWidth;

  TrianglePainter({this.strokeColor = MColor.skin, this.strokeWidth = 0, this.paintingStyle = PaintingStyle.fill});

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = strokeColor
      ..strokeWidth = strokeWidth
      ..style = paintingStyle;

    canvas.drawPath(getTrianglePath(size.width, size.height), paint);
  }

  Path getTrianglePath(double x, double y) {
    return Path()
      ..moveTo(0, 0)
      ..lineTo(x, 0)
      ..lineTo(x / 2, y)
      ..lineTo(0, 0);
  }

  @override
  bool shouldRepaint(TrianglePainter oldDelegate) {
    return oldDelegate.strokeColor != strokeColor || oldDelegate.paintingStyle != paintingStyle || oldDelegate.strokeWidth != strokeWidth;
  }
}
