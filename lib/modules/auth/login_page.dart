import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/auth/third_login_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/store/privacy_store.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<StatefulWidget> createState() => _LoginState();
}

class _LoginState extends State<LoginPage> {
  bool policyChecked = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!PrivacyStore.to.isPrivacyAgreed()) {
        PrivacyStore.to.showPrivacyDialog(
            onConfirm: () {
              PrivacyStore.to.setPrivacyAgreed();
              Future.delayed(const Duration(milliseconds: 500), () async {
                await ThirdLoginStore.to.initFluwx();
              });
            },
            onRefused: () {});
      }
      policyChecked = PrivacyStore.to.isUserAgreementApproved();
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        extendBodyBehindAppBar: true,
        backgroundColor: Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: Colors.transparent,
        ),
        body: SafeArea(
          top: false,
          child: SafeArea(
            top: false,
            child: Stack(
              children: [
                Container(),
                Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
                Positioned(
                  top: 133,
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    child: Column(
                      children: [
                        Image.asset(
                          'assets/images/icon_title.png',
                          width: 150,
                          height: 35,
                        ),
                        const SizedBox(
                          height: 67,
                        ),
                        Image.asset(
                          'assets/images/cat.png',
                          width: 201,
                          height: 201,
                        ),
                        const SizedBox(
                          height: 63,
                        ),
                        GetBuilder<ThirdLoginStore>(builder: (store) {
                          return store.isWechatInstalled
                              ? Row(
                                  children: [
                                    const SizedBox(
                                      width: 57,
                                    ),
                                    Expanded(
                                      child: TextButton(
                                        onPressed: () {
                                          if (!policyChecked) {
                                            showCustomDialog(
                                              '请先同意隐私政策与服务协议',
                                              onConfirm: () {},
                                            );
                                          } else {
                                            PrivacyStore.to.setPrivacyAgreed();
                                            ThirdLoginStore.to.signInWithWechat();
                                          }
                                        },
                                        child: Text('微信一键登录', style: TextStyle(color: MColor.xFFFFFFFF)),
                                        style: TextButton.styleFrom(backgroundColor: MColor.skin, padding: EdgeInsets.fromLTRB(10, 10, 10, 10)),
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 57,
                                    ),
                                  ],
                                )
                              : const SizedBox();
                        }),
                        Row(
                          children: [
                            const SizedBox(
                              width: 57,
                            ),
                            Expanded(
                              child: TextButton(
                                onPressed: () {
                                  if (!policyChecked) {
                                    showCustomDialog(
                                      '请先同意隐私政策与服务协议',
                                      onConfirm: () {},
                                    );
                                  } else {
                                    PrivacyStore.to.setPrivacyAgreed();
                                    ThirdLoginStore.to.initFluwx();
                                    RouterHelper.router.pushNamed(Routes.verificationPath, extra: {'bindType': 1});
                                  }
                                },
                                child: Text('手机号登录', style: TextStyle(color: MColor.xFFFFFFFF)),
                                style: TextButton.styleFrom(backgroundColor: MColor.skin, padding: EdgeInsets.fromLTRB(10, 10, 10, 10)),
                              ),
                            ),
                            const SizedBox(
                              width: 57,
                            ),
                          ],
                        ),
                        if (Platform.isIOS) ...{
                          Row(
                            children: [
                              const SizedBox(
                                width: 57,
                              ),
                              Expanded(
                                child: TextButton(
                                  onPressed: () {
                                    if (!policyChecked) {
                                      showCustomDialog(
                                        '请先同意隐私政策与服务协议',
                                        onConfirm: () {},
                                      );
                                    } else {
                                      PrivacyStore.to.setPrivacyAgreed();
                                      ThirdLoginStore.to.signInWithApple();
                                    }
                                  },
                                  child: Text('Apple 登录', style: TextStyle(color: MColor.xFFFFFFFF)),
                                  style: TextButton.styleFrom(backgroundColor: MColor.skin, padding: EdgeInsets.fromLTRB(10, 10, 10, 10)),
                                ),
                              ),
                              const SizedBox(
                                width: 57,
                              ),
                            ],
                          ),
                        }
                      ],
                    ),
                  ),
                ),
                Positioned(
                  bottom: 30,
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      setState(() {
                        policyChecked = !policyChecked;
                        PrivacyStore.to.approveUserAgreement(policyChecked);
                      });
                    },
                    child: Container(
                      padding: EdgeInsets.fromLTRB(0, 10, 0, 10),
                      width: MediaQuery.of(context).size.width,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          RichText(
                              textAlign: TextAlign.center,
                              text: WidgetSpan(
                                  child: Row(
                                children: [
                                  Icon(
                                    policyChecked ? Icons.check_circle : Icons.radio_button_unchecked,
                                    size: 14,
                                    color: MColor.skin,
                                  ),
                                  const SizedBox(
                                    width: 2,
                                  ),
                                  Text('登录及接受', style: TextStyle(height: 1, fontSize: 12, color: MColor.xFF777777)),
                                  GestureDetector(
                                      onTap: () {
                                        RouterHelper.router
                                            .pushNamed(Routes.webPath, extra: {'url': '${HttpUtil.kBaseUrl}/index/index/privacy', 'title': '隐私政策'});
                                      },
                                      child: Text('《隐私政策》', style: TextStyle(height: 1, fontSize: 12, color: MColor.skin))),
                                  Text('与', style: TextStyle(height: 1, fontSize: 12, color: MColor.xFF777777)),
                                  GestureDetector(
                                      onTap: () {
                                        RouterHelper.router
                                            .pushNamed(Routes.webPath, extra: {'url': '${HttpUtil.kBaseUrl}/index/index/userAgreement', 'title': '服务协议'});
                                      },
                                      child: Text('《服务协议》', style: TextStyle(height: 1, fontSize: 12, color: MColor.skin)))
                                ],
                              ))),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
  }
}
