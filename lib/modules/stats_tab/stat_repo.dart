import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/modules/stats_tab/stat_model.dart';

class StatRepo {
  static Future<BaseModel<ChartIndexResp>> getChartIndex({String? bookkeepingNumber, String? type, String? timeInterval}) async {
    var resp = await HttpUtil().post('api/chart/index', data: {'bookkeepingNumber': bookkeepingNumber, 'type': type, 'timeInterval': timeInterval});
    return BaseModel.fromJson(resp, (json) => ChartIndexResp.fromJson(json));
  }

  //收入来源&&支出分布
  static Future<BaseModel<IncomePaymentResp>> getIncomeAndPayment({String? bookkeepingNumber, String? type, String? timeInterval}) async {
    var resp = await HttpUtil().post('api/chart/incomeAndPayment', data: {'bookkeepingNumber': bookkeepingNumber, 'type': type, 'timeInterval': timeInterval});
    return BaseModel.fromJson(resp, (json) => IncomePaymentResp.fromJson(json));
  }

  //资产走势
  static Future<BaseModel<PropertyTrendResp>> getPropertyTrend({String? type, String? timeInterval}) async {
    var resp = await HttpUtil().post('api/chart/propertyWalk', data: {'type': type, 'timeInterval': timeInterval});
    return BaseModel.fromJson(resp, (json) => PropertyTrendResp.fromJson(json));
  }

  //资产汇总
  static Future<BaseModel<PropertySummaryResp>> getPropertySummary({String? type, String? timeInterval}) async {
    var resp = await HttpUtil().post('api/chart/assetSummary', data: {'type': type, 'timeInterval': timeInterval});
    return BaseModel.fromJson(resp, (json) => PropertySummaryResp.fromJson(json));
  }

  //负债汇总
  static Future<BaseModel<PropertySummaryResp>> getLiabilitiesSummary({String? type, String? timeInterval}) async {
    var resp = await HttpUtil().post('api/chart/liabilitiesSummary', data: {'type': type, 'timeInterval': timeInterval});
    return BaseModel.fromJson(resp, (json) => PropertySummaryResp.fromJson(json));
  }

  //节省金额&&非必要金额
  static Future<BaseModel<SaveUnnecessaryResp>> getSavingAndUnnecessary({String? bookkeepingNumber, String? type, String? timeInterval}) async {
    var resp = await HttpUtil().post('api/chart/saveMoney', data: {'bookkeepingNumber': bookkeepingNumber, 'type': type, 'timeInterval': timeInterval});
    return BaseModel.fromJson(resp, (json) => SaveUnnecessaryResp.fromJson(json));
  }
}
