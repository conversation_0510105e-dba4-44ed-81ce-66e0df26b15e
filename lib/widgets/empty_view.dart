import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qiazhun/constants/design.dart';

class EmptyView extends StatelessWidget {
  final String? tips;
  const EmptyView({super.key, this.tips});

  @override
  Widget build(BuildContext context) {
    double height = MediaQuery.of(context).size.height;
    return SizedBox(
      width: double.infinity,
      height: height / 2,
      child: Stack(
        children: [
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.hourglass_empty,
                  size: 40,
                  color: MColor.xFF999999,
                ),
                const SizedBox(height: 10),
                Text(tips ?? '暂无数据', style: TextStyle(height: 1.4, fontSize: 14, color: MColor.skin)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
