import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:qiazhun/widgets/custom_popup_panel.dart';

void main() {
  group('CustomPopupPanel', () {
    testWidgets('should create with default height fraction', (WidgetTester tester) async {
      const panel = CustomPopupPanel(
        title: 'Test Title',
        widget: Text('Test Content'),
      );

      expect(panel.heightFraction, equals(0.5));
    });

    testWidgets('should create with custom height fraction', (WidgetTester tester) async {
      const panel = CustomPopupPanel(
        title: 'Test Title',
        widget: Text('Test Content'),
        heightFraction: 0.33,
      );

      expect(panel.heightFraction, equals(0.33));
    });

    testWidgets('should create with one third height fraction', (WidgetTester tester) async {
      const panel = CustomPopupPanel(
        title: 'Test Title',
        widget: Text('Test Content'),
        heightFraction: 1/3,
      );

      expect(panel.heightFraction, closeTo(0.33, 0.01));
    });

    testWidgets('should create with two thirds height fraction', (WidgetTester tester) async {
      const panel = CustomPopupPanel(
        title: 'Test Title',
        widget: Text('Test Content'),
        heightFraction: 2/3,
      );

      expect(panel.heightFraction, closeTo(0.67, 0.01));
    });

    testWidgets('should handle null height fraction', (WidgetTester tester) async {
      const panel = CustomPopupPanel(
        title: 'Test Title',
        widget: Text('Test Content'),
        heightFraction: null,
      );

      expect(panel.heightFraction, isNull);
    });

    testWidgets('should render with title and content', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomPopupPanel(
              title: 'Test Title',
              widget: Text('Test Content'),
              heightFraction: 0.5,
            ),
          ),
        ),
      );

      expect(find.text('Test Title'), findsOneWidget);
      expect(find.text('Test Content'), findsOneWidget);
    });

    testWidgets('should have close button', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomPopupPanel(
              title: 'Test Title',
              widget: Text('Test Content'),
              heightFraction: 0.5,
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.close), findsOneWidget);
    });

    testWidgets('should have submit button when onConfirm is provided', (WidgetTester tester) async {
      bool confirmCalled = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomPopupPanel(
              title: 'Test Title',
              widget: Text('Test Content'),
              heightFraction: 0.5,
              onConfirm: () async {
                confirmCalled = true;
              },
            ),
          ),
        ),
      );

      // 查找提交按钮
      expect(find.byType(ElevatedButton), findsOneWidget);
      
      // 点击提交按钮
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      expect(confirmCalled, isTrue);
    });
  });
}
