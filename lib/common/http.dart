import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart' as Get;
import 'package:logger/logger.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/storage_util.dart';
import 'package:qiazhun/modules/mine_tab/user_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

import '../generated/l10n.dart';

/*
  * http 操作类
  *
  * 手册
  * https://github.com/flutterchina/dio/blob/master/README-ZH.md
  *
  * 从 3 升级到 4
  * https://github.com/flutterchina/dio/blob/master/migration_to_4.x.md
*/
class HttpUtil {
  static final HttpUtil _instance = HttpUtil._internal();

  factory HttpUtil() => _instance;

  // static String kBaseUrl = 'https://show.keepbit.top/';
  static String kBaseUrl = 'https://ji.qiazhun.com';
  // static String kBaseUrl = 'http://bookkeeping.sudada123.cn';

  late Dio dio;
  CancelToken cancelToken = CancelToken();

  HttpUtil._internal() {
    // BaseOptions、Options、RequestOptions 都可以配置参数，优先级别依次递增，且可以根据优先级别覆盖参数
    String baseUrl = '$kBaseUrl/';
    // switch (AppLogic.to.flavor) {
    //   case Flavor.dev:
    //     baseUrl = SERVICE_API_BASEURL_DEV;
    //     break;
    //   case Flavor.test:
    //     baseUrl = SERVICE_API_BASEURL_TEST;
    //     break;
    //   case Flavor.prod:
    //     baseUrl = SERVICE_API_BASEURL_PROD;
    //     break;
    // }

    BaseOptions options = BaseOptions(
      // 请求基地址,可以包含子路径
      baseUrl: baseUrl,

      // baseUrl: storage.read(key: STORAGE_KEY_APIURL) ?? SERVICE_API_BASEURL,
      //连接服务器超时时间，单位是毫秒.
      connectTimeout: const Duration(seconds: 60),

      // 响应流上前后两次接受到数据的间隔，单位为毫秒。
      receiveTimeout: const Duration(seconds: 60),

      sendTimeout: const Duration(seconds: 60),

      // Http请求头.
      headers: {},

      /// 请求的Content-Type，默认值是"application/json; charset=utf-8".
      /// 如果您想以"application/x-www-form-urlencoded"格式编码请求数据,
      /// 可以设置此选项为 `Headers.formUrlEncodedContentType`,  这样[Dio]
      /// 就会自动编码请求体.
      contentType: 'application/json; charset=utf-8',

      /// [responseType] 表示期望以那种格式(方式)接受响应数据。
      /// 目前 [ResponseType] 接受三种类型 `JSON`, `STREAM`, `PLAIN`.
      ///
      /// 默认值是 `JSON`, 当响应头中content-type为"application/json"时，dio 会自动将响应内容转化为json对象。
      /// 如果想以二进制方式接受响应数据，如下载一个二进制文件，那么可以使用 `STREAM`.
      ///
      /// 如果想以文本(字符串)格式接收响应数据，请使用 `PLAIN`.
      responseType: ResponseType.json,
    );

    dio = Dio(options);
    // 添加拦截器
    dio.interceptors.add(InterceptorsWrapper(
      onResponse: (response, handler) {
        if (response.data is String) {
          response.data = json.decode(response.data);
        }
        handler.next(response);
      },
    ));
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        //打印请求地址
        logger.d('request: ${options.method} ${options.uri} headers :${options.headers} data:${options.data}');

        return handler.next(options); //continue
        // 如果你想完成请求并返回一些自定义数据，你可以resolve一个Response对象 `handler.resolve(response)`。
        // 这样请求将会被终止，上层then会被调用，then中返回的数据将是你的自定义response.
        //
        // 如果你想终止请求并触发一个错误,你可以返回一个`DioError`对象,如`handler.reject(error)`，
        // 这样请求将被中止并触发异常，上层catchError会被调用。
      },
      onResponse: (response, handler) {
        // Do something with response data
        // if (response.data is String) {
        //   response.data = json.decode(response.data);
        // }
        logger.d('respose: ${response.requestOptions.uri} ${response.requestOptions.data.toString()} ${response.data?.toString()}');
        // logger.i(response.headers);
        return handler.next(response); // continue
        // 如果你想终止请求并触发一个错误,你可以 reject 一个`DioError`对象,如`handler.reject(error)`，
        // 这样请求将被中止并触发异常，上层catchError会被调用。
      },
      onError: (DioError e, handler) {
        // Do something with response error
        Loading.dismiss();
        ErrorEntity eInfo = createErrorEntity(e);
        onError(eInfo);
        return handler.next(e); //continue
        // 如果你想完成请求并返回一些自定义数据，可以resolve 一个`Response`,如`handler.resolve(response)`。
        // 这样请求将会被终止，上层then会被调用，then中返回的数据将是你的自定义response.
      },
    ));
  }

  /*
   * error统一处理
   */

  // 错误处理
  void onError(ErrorEntity eInfo) {
    print('error.code -> ${eInfo.code}, error.message -> ${eInfo.message}');
    switch (eInfo.code) {
      case 40004:
        // UserStore.to.onLogout();
        // EasyLoading.showError(eInfo.message);
        break;
      default:
        // EasyLoading.showError('未知错误');
        break;
    }
  }

  // 错误信息
  ErrorEntity createErrorEntity(DioError error) {
    Logger().e(error.error.toString());
    switch (error.type) {
      case DioErrorType.cancel:
        return ErrorEntity(code: -1, message: "请求取消");
      case DioErrorType.connectionTimeout:
        return ErrorEntity(code: -1, message: "连接超时");
      case DioErrorType.sendTimeout:
        return ErrorEntity(code: -1, message: "请求超时");
      case DioErrorType.receiveTimeout:
        return ErrorEntity(code: -1, message: "响应超时");
      case DioErrorType.badResponse:
        {
          try {
            int errCode = error.response != null ? error.response!.statusCode! : -1;
            if (errCode == 401) {
              UserStore.to.logout();
              RouterHelper.router.goNamed(Routes.loginPath);
            }
            return ErrorEntity(code: errCode, message: error.response?.data['msg'] ?? (error.response!.statusMessage ?? '${errCode}错误'));
          } on Exception catch (_) {
            return ErrorEntity(code: -1, message: "未知错误");
          }
        }
      default:
        {
          return ErrorEntity(code: -1, message: error.message ?? '');
        }
    }
  }

  /*
   * 取消请求
   *
   * 同一个cancel token 可以用于多个请求，当一个cancel token取消时，所有使用该cancel token的请求都会被取消。
   * 所以参数可选
   */
  void cancelRequests(CancelToken token) {
    token.cancel("cancelled");
  }

  /// 读取本地配置
  Map<String, dynamic>? getAuthorizationHeader() {
    var headers = <String, dynamic>{};
    var token = UserStore.to.getToken();
    // var token = '4a0d7191-be7e-4357-818a-14c4f9c96803';

    // var token = UserStore.to.getToken();
    debugPrint('token -> $token');
    if (token?.isNotEmpty == true) {
      headers['token'] = token;
    }
    // headers['sign'] = false;
    // var locale = SettingStore.to.getCurrentLocale();
    // var language = locale.languageCode;
    // if (locale.countryCode?.isNotEmpty == true) {
    //   language = '$language-${locale.countryCode}';
    // }
    // headers['Accept-Language'] = language;
    // headers['Terminal-Version'] = GlobalTools.versionName;
    return headers;
  }

  /// restful get 操作
  /// refresh 是否下拉刷新 默认 false
  /// noCache 是否不缓存 默认 true
  /// list 是否列表 默认 false
  /// cacheKey 缓存key
  /// cacheDisk 是否磁盘缓存
  Future get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    bool refresh = false,
    bool noCache = true,
    bool list = false,
    String cacheKey = '',
    bool cacheDisk = false,
  }) async {
    try {
      Options requestOptions = options ?? Options();
      requestOptions.extra ??= {};
      requestOptions.extra!.addAll({
        "refresh": refresh,
        "noCache": noCache,
        "list": list,
        "cacheKey": cacheKey,
        "cacheDisk": cacheDisk,
      });
      requestOptions.headers = requestOptions.headers ?? {};
      Map<String, dynamic>? authorization = getAuthorizationHeader();
      if (authorization != null) {
        requestOptions.headers!.addAll(authorization);
      }

      // print('queryParameters: $queryParameters');
      var response = await dio.get(
        path,
        queryParameters: queryParameters,
        options: requestOptions,
        cancelToken: cancelToken,
      );
      debugPrint('get resource: ${response.realUri}');
      return response.data;
    } catch (e) {
      return e;
    } finally {}
  }

  Future getUri(
    String url, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    bool refresh = false,
    bool noCache = true,
    bool list = false,
    String cacheKey = '',
    bool cacheDisk = false,
  }) async {
    try {
      // print('queryParameters: $queryParameters');
      var response = await dio.getUri(
        Uri.parse(url),
        cancelToken: cancelToken,
      );
      debugPrint('getUri resource: ${response.realUri}');
      return response.data;
    } catch (e) {
      return e;
    } finally {}
  }

  /// restful post 操作
  Future<dynamic> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      Options requestOptions = options ?? Options();
      requestOptions.headers = requestOptions.headers ?? {};
      Map<String, dynamic>? authorization = getAuthorizationHeader();
      if (authorization != null) {
        requestOptions.headers!.addAll(authorization);
      }
      var response = await dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: requestOptions,
        cancelToken: cancelToken,
      );
      debugPrint('res : ${response.data}');
      return response.data;
    } on DioException catch (e) {
      logger.e('Http post err $e');
      // try {

      //   int errCode = e.response != null ? error.response!.statusCode! : -1;
      //   return ErrorEntity(code: errCode, message: error.response?.data['msg'] ?? (error.response!.statusMessage ?? '${errCode}错误'));
      // } on Exception catch (_) {
      //   return ErrorEntity(code: -1, message: "未知错误");
      // }
      // return e;
      return e.response?.data;
    } finally {}
  }

  Future postForm(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      Options requestOptions = options ?? Options();
      requestOptions.headers = requestOptions.headers ?? {};
      Map<String, dynamic>? authorization = getAuthorizationHeader();
      if (authorization != null) {
        requestOptions.headers!.addAll(authorization);
      }
      Logger().d(requestOptions);
      var response = await dio.post(
        path,
        data: FormData.fromMap(data),
        queryParameters: queryParameters,
        options: requestOptions,
        cancelToken: cancelToken,
      );
      debugPrint('get resource: ${response.realUri}');
      return response.data;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace, maxFrames: 5);
      logger.e('postForm failed, $e');
    } finally {}
  }

  /// restful post Stream 流数据
  Future postStream(
    String path, {
    dynamic data,
    int dataLength = 0,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      Options requestOptions = options ?? Options();
      requestOptions.headers = requestOptions.headers ?? {};
      Map<String, dynamic>? authorization = getAuthorizationHeader();
      if (authorization != null) {
        requestOptions.headers!.addAll(authorization);
      }
      requestOptions.headers!.addAll({
        Headers.contentLengthHeader: dataLength.toString(),
      });
      var response = await dio.post(
        path,
        data: Stream.fromIterable(data.map((e) => [e])),
        queryParameters: queryParameters,
        options: requestOptions,
        cancelToken: cancelToken,
      );
      return response.data;
    } catch (e) {
    } finally {}
  }
}

// 异常处理
class ErrorEntity implements Exception {
  int code = -1;
  String message = "";

  ErrorEntity({required this.code, required this.message});

  String toString() {
    if (message == "") return "Exception";
    return "Exception: code $code, $message";
  }
}

class LogInterceptor extends Interceptor {
  @override
  Future<void> onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    Map<String, dynamic> headers = {};
    // headers['versionName'] = GlobalConst.versionName;
    // headers['versionCode'] = GlobalConst.versionCode;
    // headers['osType'] = DeviceUtils.osType;
    // headers['osVersion'] = DeviceUtils.sysVersion;
    // headers['deviceType'] = DeviceUtils.deviceName;
    // headers['deviceId'] = DeviceUtils.deviceId;
    // headers['timestamp'] = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    // if (GlobalConst.userModel?.token != null) {
    //   headers['token'] = GlobalConst.userModel!.token;
    // }

    // int language = GlobalConst.sharedPreferences?.getInt(Constant.kLanguage) ?? 999;
    // if (language != 999 && language < TranslationService.locales.length) {
    //   headers['language'] = TranslationService.locales[language].toString();
    // } else {
    //   headers['language'] = TranslationService.locale.toString();
    // }
    // options.headers.addAll(headers);

    // log('request: ${options.method} ${options.uri} headers :${options.headers} data:${options.data}');
    super.onRequest(options, handler);
  }

  // @override
  // void onResponse(Response response, ResponseInterceptorHandler handler) {
  //   if (kDebugMode) {
  //     logger.d('respose: ${response.requestOptions.uri} ${response.requestOptions.data.toString()} ${response.data?.toString()}');
  //   }

  //   super.onResponse(response, handler);
  // }

  // @override
  // void onError(DioError err, ErrorInterceptorHandler handler) {
  //   logger.e('error ${err.requestOptions.uri} ${err.message} ${err.response.toString()}');
  //   super.onError(err, handler);
  // }
}
