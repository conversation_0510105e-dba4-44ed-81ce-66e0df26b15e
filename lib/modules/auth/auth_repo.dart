import 'dart:convert';

import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/models/user_model.dart';

class AuthRepo {
  static Future<BaseModel<AuthLoginResp>> wxLogin(String code) async {
    var resp = await HttpUtil().post('api/user/wxLogin', data: {'appCode': code});
    return BaseModel.fromJson(resp, (json) => AuthLoginResp.fromJson(json));
  }

  static Future<BaseModel<AuthLoginResp>> appleLogin(String identityToken) async {
    var resp = await HttpUtil().post('api/user/appleLogin', data: {'identityToken': identityToken});
    return BaseModel.fromJson(resp, (json) => AuthLoginResp.fromJson(json));
  }

  static Future<BaseModel<dynamic>> bindMobile(String mobile, String code) async {
    var resp = await HttpUtil().post('api/user/bindMobile', data: {'mobile': mobile, 'code': code});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<AuthLoginResp>> mobileLogin(String mobile, String code) async {
    var resp = await HttpUtil().post('api/user/mobilelogin', data: {'mobile': mobile, 'captcha': code});
    return BaseModel.fromJson(resp, (json) => AuthLoginResp.fromJson(json));
  }

  static Future<BaseModel<dynamic>> reBindMobile(String mobile, String code) async {
    var resp = await HttpUtil().post('api/user/updateBindMobile', data: {'mobile': mobile, 'code': code});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> sendSms(String mobile) async {
    var resp = await HttpUtil().post('api/sms/send', data: {'mobile': mobile});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> bindWechat(String appCode) async {
    var resp = await HttpUtil().post('api/user/bindWechat', data: {'appCode': appCode});
    return BaseModel.fromJson(resp, (json) => json);
  }
}
