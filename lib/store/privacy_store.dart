import 'dart:io';
import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/common/storage_util.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class PrivacyStore extends GetxController {
  static PrivacyStore get to => Get.find();

  bool isPrivacyAgreed() {
    return StorageUtil.getBool('agree_license');
  }

  void setPrivacyAgreed() {
    StorageUtil.setBool('agree_license', true);
    update();
  }

  bool isUserAgreementApproved() {
    return StorageUtil.getBool('user_agreement_approved', defValue: false);
  }

  void approveUserAgreement(bool approved) {
    StorageUtil.setBool('user_agreement_approved', approved);
  }

  bool hasShowCameraTips() {
    return StorageUtil.getBool('camera_tips') || Platform.isIOS;
  }

  void setCameraTips() {
    StorageUtil.setBool('camera_tips', true);
  }

  Future<void> showCameraTips({VoidCallback? onConfirm}) async {
    setCameraTips();
    return showCustomDialog(
      '为方便您使用App时可以正常上传图片、文件或拍照上传，需要您允许使用照相机、存储权限',
      okTitle: '下一步',
      onConfirm: () {
        onConfirm?.call();
      },
    );
  }

  void showPrivacySheet({VoidCallback? onConfirm}) {
    Get.bottomSheet(
        persistent: false,
        isScrollControlled: true,
        ignoreSafeArea: false,
        SafeArea(
          bottom: false,
          child: Builder(builder: (context) {
            return Container(
              padding: const EdgeInsets.fromLTRB(15, 20, 15, 20),
              decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16))),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '隐私政策授权提示',
                    style: MFont.semi_Bold17.apply(color: MColor.xFF333333),
                  ),
                  const SizedBox(height: 10),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12.0),
                    child: RichText(
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        softWrap: true,
                        text: TextSpan(
                            text: '进入下一步前，请先阅读并同意',
                            style: MFont.regular14.apply(color: MColor.xFF333333),
                            //手势监听
                            // recognizer: ,
                            children: [
                              TextSpan(
                                  text: ' 服务协议 ',
                                  style: MFont.regular14.apply(color: MColor.skin),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () {
                                      RouterHelper.router
                                          .pushNamed(Routes.webPath, extra: {'url': '${HttpUtil.kBaseUrl}/index/index/userAgreement', 'title': '服务协议'});
                                    }),
                              TextSpan(
                                text: '与',
                                style: MFont.regular14.apply(color: MColor.xFF333333),
                              ),
                              TextSpan(
                                  text: ' 隐私政策',
                                  style: MFont.regular14.apply(color: MColor.skin),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () {
                                      RouterHelper.router
                                          .pushNamed(Routes.webPath, extra: {'url': '${HttpUtil.kBaseUrl}/index/index/privacy', 'title': '隐私政策'});
                                    }),
                            ])),
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      TextButton(
                        style: ButtonStyle(
                          shape: MaterialStateProperty.all(const StadiumBorder()),
                          minimumSize: MaterialStateProperty.all(const Size(100, 28)),
                        ),
                        onPressed: () {
                          Get.back();
                        },
                        child: Text(
                          '拒绝',
                          style: MFont.regular17.apply(color: MColor.xFF333333),
                        ),
                      ),
                      const SizedBox(width: 20),
                      TextButton(
                        style: ButtonStyle(
                          shape: MaterialStateProperty.all(const StadiumBorder()),
                          backgroundColor: MaterialStateProperty.all(MColor.skin),
                          minimumSize: MaterialStateProperty.all(const Size(200, 28)),
                        ),
                        onPressed: () {
                          Get.back();
                          PrivacyStore.to.setPrivacyAgreed();
                          if (onConfirm != null) {
                            onConfirm();
                          }
                        },
                        child: Text(
                          '确定',
                          style: MFont.regular17.apply(color: Colors.white),
                        ),
                      )
                    ],
                  ),
                ],
              ),
            );
          }),
        ),
        isDismissible: false,
        enterBottomSheetDuration: const Duration(milliseconds: 200),
        exitBottomSheetDuration: const Duration(milliseconds: 200));
  }

  void showPrivacyDialog({Function? onConfirm, Function? onRefused}) {
    Get.generalDialog(
        navigatorKey: RouterHelper.parentNavigatorKey,
        pageBuilder: (ctx, a, s) {
          return Builder(builder: (context) {
            return WillPopScope(
                onWillPop: () async => false,
                child: CupertinoAlertDialog(
                  title: Column(
                    children: [
                      Text(
                        '隐私政策与服务协议',
                        style: MFont.semi_Bold17.apply(color: MColor.xFF333333),
                      ),
                    ],
                  ),
                  content: buildContent(context),
                  actions: <Widget>[
                    TextButton(
                      onPressed: () {
                        RouterHelper.router.pop();
                        onRefused?.call();
                      },
                      child: Text(
                        '拒绝',
                        style: MFont.regular17.apply(color: MColor.xFF333333),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        RouterHelper.router.pop();
                        onConfirm?.call();
                      },
                      child: Text(
                        '确定',
                        style: MFont.regular17.apply(color: MColor.xFF333333),
                      ),
                    ),
                  ],
                ));
          });
        });
  }

  String privacyContent =
      '''欢迎使用掐准记账\n我们非常重视您的隐私和个人信息保护，在您使用本App的过程中，我们会对您的部分个人信息进行收集和使用\n1. 在您同意App隐私政策后，我们将进行集成SDK的初始化工作，会收集您的设备MAC地址、IMSI、Android ID、IP地址、硬件型号、OAID、操作系统版本号、唯一设备标识符（IMEI等）、网络设备硬件地址（MAC）、软件版本号、网络接入方式、类型、状态、网络质量数据、操作日志、硬件序列号、服务日志信息等以保障App正常数据统计及安全风控。\n2. 未经您的同意，我们不会从第三方获取、共享或对外提供您的信息。\n3. 您可以访问、更正、删除您的个人信息，我们也将提供注销、投诉方式。\n''';

  Widget buildContent(BuildContext context) {
    return SizedBox(
      height: 300,
      //ListView可滑动
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: ListView(
              shrinkWrap: true,
              children: [
                Text(
                  textAlign: TextAlign.start,
                  privacyContent,
                ),
              ],
            ),
          ),
          const SizedBox(
            height: 8,
          ),
          RichText(
            text: TextSpan(
                style: MFont.regular14.apply(color: MColor.xFF333333),
                //手势监听
                // recognizer: ,
                children: [
                  TextSpan(
                    text: '请认真阅读并理解',
                    style: MFont.regular14.apply(color: MColor.xFF333333),
                  ),
                  TextSpan(
                      text: '《隐私政策》',
                      style: MFont.regular14.apply(color: MColor.skin),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          RouterHelper.router.pushNamed(Routes.webPath, extra: {'url': '${HttpUtil.kBaseUrl}/index/index/privacy', 'title': '隐私政策'});
                        }),
                  TextSpan(
                    text: '与',
                    style: MFont.regular14.apply(color: MColor.xFF333333),
                  ),
                  TextSpan(
                      text: '《服务协议》',
                      style: MFont.regular14.apply(color: MColor.skin),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          RouterHelper.router.pushNamed(Routes.webPath, extra: {'url': '${HttpUtil.kBaseUrl}/index/index/userAgreement', 'title': '服务协议'});
                        }),
                ]),
          )
        ],
      ),
    );
  }
}
