class PriceInfo {
  String originPrice;
  bool isNegative;
  List<String> priceParts;

  PriceInfo(this.originPrice, this.isNegative, this.priceParts);

  static PriceInfo parsePrice(String price, {bool? forceNegative}) {
    double dPrice = double.tryParse(price) ?? 0.0;
    return PriceInfo(price, forceNegative ?? dPrice.isNegative, _parseDouble(dPrice, forceNegative: forceNegative));
  }

  static List<String> _parseDouble(double value, {bool? forceNegative}) {
    String sign = (forceNegative == true || value.isNegative) ? "-" : "+";
    double absValue = value.abs();

    String valueStr = absValue.toStringAsFixed(2);
    List<String> parts = valueStr.split('.');

    String integerPart = parts[0];
    String fractionalPart = ".00"; // Default if no fractional part

    if (parts.length > 1) {
      fractionalPart = '.${parts[1]}';
    }

    return [sign, integerPart, fractionalPart];
  }

  String get integerPart {
    if (isNegative) {
      return '-${priceParts[1]}';
    }
    return priceParts[1];
  }
}
