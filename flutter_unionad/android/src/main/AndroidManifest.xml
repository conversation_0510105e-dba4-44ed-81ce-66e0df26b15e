<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.gstory.flutter_unionad">
    <!--必要权限-->
    <uses-permission android:name="android.permission.INTERNET" />

    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <application>
        <provider
            android:name="com.bytedance.sdk.openadsdk.TTFileProvider"
            android:authorities="${applicationId}.TTFileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
        <provider
            android:name="com.bytedance.sdk.openadsdk.multipro.TTMultiProvider"
            android:authorities="${applicationId}.TTMultiProvider"
            android:exported="false" />
        <activity
            android:name=".rewardvideoad.RewardVideoAd"
            android:theme="@android:style/Theme.Black.NoTitleBar"/>
    </application>
</manifest>
