import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/bookkeeping_controller.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/extensions.dart';

class BookkeepingShortcutView extends GetView<BookkeepingController> {
  const BookkeepingShortcutView({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      List<Widget> items = [];

      // 添加日期选择器
      items.add(_buildDateItem(context));

      // 添加非必要开关
      items.add(_buildUnnecessaryItem());

      // 添加节省开关（仅支出时显示）
      if (controller.selectedTab.value == 0) {
        items.add(_buildSavingItem());
      }

      // 添加账户相关项目
      if (controller.selectedTab.value == 0) {
        if (controller.firstOutcomeAccount.value != null) {
          items.add(_buildAccountItem(controller.firstOutcomeAccount.value!));
        }
        controller.shortcutOutcomeAccounts.forEach((account) {
          if (controller.firstOutcomeAccount.value?.id != account.id && controller.selectedOutcomeAccount.value?.id != account.id) {
            items.add(_buildAccountItem(account));
          }
        });
      } else {
        if (controller.firstIncomeAccount.value != null) {
          items.add(_buildAccountItem(controller.firstIncomeAccount.value!));
        }
        controller.shortcutIncomeAccounts.forEach((account) {
          if (controller.firstIncomeAccount.value?.id != account.id && controller.selectedIncomeAccount.value?.id != account.id) {
            items.add(_buildAccountItem(account));
          }
        });
      }

      return SizedBox(
        height: 28,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          itemCount: items.length,
          controller: controller.shortcutScrollController,
          separatorBuilder: (context, index) => const SizedBox(width: 10),
          itemBuilder: (context, index) => items[index],
        ),
      );
    });
  }

  Widget _buildDateItem(BuildContext context) {
    return GestureDetector(
      onTap: () {
        showDatePicker(
          locale: const Locale('zh', 'CN'),
          context: context,
          firstDate: DateTime(2020, 1, 1),
          currentDate: controller.selectedDay.value,
          lastDate: DateTime.now().nextYear(),
        ).then((date) {
          if (date != null) {
            // onDateSelected(date);
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 0),
        decoration: BoxDecoration(
          color: MColor.xFFFFF4E1,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(color: MColor.xFFFFBE4A, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset('assets/images/ic_cal_calendar.png', width: 16, height: 16),
            const SizedBox(width: 2),
            Text(
              controller.selectedDay.value.isSameDay(DateTime.now()) ? '今天' : DateFormat('M月d日').format(controller.selectedDay.value),
              style: const TextStyle(color: MColor.xFFFFBE4A, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUnnecessaryItem() {
    return GestureDetector(
      onTap: () {
        controller.noNeed.value = !controller.noNeed.value;
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 0),
        decoration: BoxDecoration(
          color: controller.noNeed.value ? MColor.xFFFFFFFF : MColor.xFFEEEEEE,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(color: controller.noNeed.value ? MColor.skin : MColor.xFF999999, width: 1),
        ),
        child: Row(
          children: [
            Text(
              '非必要',
              style: TextStyle(
                color: controller.noNeed.value ? MColor.skin : MColor.xFF999999,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSavingItem() {
    return GestureDetector(
      onTap: controller.handleSavingTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 0),
        decoration: BoxDecoration(
          color: MColor.xFFFFF4E1,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(color: controller.isSavingInput.value ? MColor.xFFFFBE4A : MColor.xFF999999, width: 1),
        ),
        child: Row(
          children: [
            Text(
              '节省',
              style: TextStyle(color: controller.isSavingInput.value ? MColor.xFFFFBE4A : MColor.xFF999999, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountItem(AccountModel account) {
    return _AccountShortcutItem(account);
  }
}

class _AccountShortcutItem extends GetView<BookkeepingController> {
  final AccountModel account;
  const _AccountShortcutItem(this.account);
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final selectedAccount = controller.selectedTab.value == 0 ? controller.selectedOutcomeAccount : controller.selectedIncomeAccount;
      final isSelected = selectedAccount.value?.id == account.id;

      Color borderColor;
      Color textColor;

      if (isSelected) {
        borderColor = MColor.skin;
        textColor = MColor.skin;
      } else {
        borderColor = MColor.xFFEEEEEE;
        textColor = MColor.xFF999999;
      }
      return GestureDetector(
        onTap: () {
          if (!isSelected) {
            if (controller.selectedTab.value == 0) {
              controller.firstOutcomeAccount.value = account;
              controller.selectedOutcomeAccount.value = account;
            } else {
              controller.firstIncomeAccount.value = account;
              controller.selectedIncomeAccount.value = account;
            }
            controller.shortcutScrollController.jumpTo(0);
            // } else {
            //   if (controller.selectedTab.value == 0) {
            //     controller.selectedOutcomeAccount.value = null;
            //   } else {
            //     controller.selectedIncomeAccount.value = null;
            //   }
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 0),
          decoration: BoxDecoration(
            color: MColor.xFFFFFFFF,
            borderRadius: BorderRadius.circular(18),
            border: Border.all(color: borderColor, width: 1),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                'assets/images/ic_card.png',
                width: 16,
                height: 16,
                color: textColor,
              ),
              const SizedBox(width: 4),
              Text(
                account.getShortDesc(),
                style: TextStyle(color: textColor, fontSize: 12),
              ),
              const SizedBox(width: 4),
              if (isSelected)
                GestureDetector(
                  onTap: () {
                    RouterHelper.router.pushNamed(Routes.accountListPath,
                        extra: {'selectMode': true, 'limitAccount': controller.selectedTab.value == 0 ? '5' : '4'}).then((value) {
                      if (value != null && value is Map<String, dynamic> && value.containsKey('selected')) {
                        if (controller.selectedTab.value == 0) {
                          controller.firstOutcomeAccount.value = value['selected'] as AccountModel;
                          controller.selectedOutcomeAccount.value = value['selected'] as AccountModel;
                        } else {
                          controller.firstIncomeAccount.value = value['selected'] as AccountModel;
                          controller.selectedIncomeAccount.value = value['selected'] as AccountModel;
                        }
                      }
                    });
                  },
                  child: Icon(
                    Icons.arrow_drop_down_circle_outlined,
                    color: textColor,
                    size: 16,
                  ),
                )
            ],
          ),
        ),
      );
    });
  }
}
