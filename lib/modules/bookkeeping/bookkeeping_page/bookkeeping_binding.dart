import 'package:get/get.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/bookkeeping_controller.dart';

class BookkeepingBinding extends Bindings {
  final dynamic logId;
  final dynamic targetAccountId;
  BookkeepingBinding({this.logId, this.targetAccountId});
  @override
  void dependencies() {
    Get.lazyPut<BookkeepingController>(() => BookkeepingController(logId: logId, targetAccountId: targetAccountId));
  }
}
