import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/mine_tab/user_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/loading_view.dart';
import 'package:qiazhun/widgets/round_image.dart';

class AnnualFeePlanPage extends StatefulWidget {
  const AnnualFeePlanPage({super.key});

  @override
  State<StatefulWidget> createState() => _AnnualFeePlanState();
}

class _AnnualFeePlanState extends State<AnnualFeePlanPage> {
  bool _isLoading = true;

  final List<AnnualFeePlanModel> _annualList = [];
  final List<AnnualFeePlanModel> _noAnnualList = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    _isLoading = true;
    Loading.show();

    try {
      var resp = await UserRepo.getAnnualFeePlan();
      if (resp.code == 1) {
        _annualList.clear();
        _annualList.addAll(resp.data?.annual ?? []);
        _noAnnualList.clear();
        _noAnnualList.addAll(resp.data?.noAnnual ?? []);
      } else {
        showToast(resp.msg ?? '数据获取失败');
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace, maxFrames: 4);
      showToast('数据获取失败 $e');
    } finally {
      Loading.dismiss();
      _isLoading = false;
      setState(() {});
    }
    // _dataList.clear();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: SafeArea(
        top: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        '年费保镖',
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: Builder(builder: (context) {
                    if (_isLoading) {
                      return LoadingView();
                    }
                    if (_noAnnualList.isEmpty && _annualList.isEmpty) {
                      return EmptyView();
                    }
                    return ListView.separated(
                        padding: EdgeInsets.zero,
                        itemBuilder: (context, index) {
                          return Container(
                              color: MColor.xFFF5F5F5,
                              child: _itemView(index >= _noAnnualList.length ? _annualList[index - _noAnnualList.length] : _noAnnualList[index], index,
                                  index >= _noAnnualList.length));
                        },
                        separatorBuilder: (context, index) {
                          if (_noAnnualList.isEmpty || _annualList.isEmpty) {
                            return const SizedBox(
                              height: 18,
                            );
                          } else if (index == _noAnnualList.length - 1) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 18.0),
                              child: Image.asset(
                                'assets/images/bg_separator_repay_diary.png',
                                width: 343,
                                height: 31,
                              ),
                            );
                          }
                          return const SizedBox(
                            height: 18,
                          );
                        },
                        itemCount: _annualList.length + _noAnnualList.length);
                  }))
            ],
          ),
        ),
      ),
    );
  }

  Widget _itemView(AnnualFeePlanModel data, int index, bool isUsed) {
    return Slidable(
      key: ValueKey(data.id),
      // The end action pane is the one at the right or the bottom side.
      endActionPane: data.annualStatus != '1'
          ? ActionPane(extentRatio: 0.3, motion: ScrollMotion(), children: [
              CustomSlidableAction(
                onPressed: (onPressed) async {
                  Loading.show();
                  try {
                    var resp = await UserRepo.exemptAnnualFee(data.id!);
                    if (resp.code == 1) {
                      showToast('免年费成功');
                      await _loadData();
                    } else {
                      showToast(resp.msg ?? '免年费失败');
                    }
                  } catch (e) {
                    showToast('免年费失败 $e');
                  } finally {
                    Loading.dismiss();
                  }
                },
                padding: EdgeInsets.all(0),
                child: Container(
                  child: Text(
                    '免年费',
                    style: TextStyle(height: 1.4, fontSize: 14, fontWeight: FontWeight.w500, color: MColor.skin),
                  ),
                  decoration: BoxDecoration(color: MColor.skin.withOpacity(0.2), borderRadius: BorderRadius.circular(17)),
                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                ),
              )
            ])
          : null,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 14),
        padding: EdgeInsets.all(18),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: Color(int.tryParse(data.bankColour ?? '', radix: 16) ?? MColor.skin.value)),
        child: Row(
          children: [
            RoundImage(imageUrl: getImageUrl(data.bankIcon), radius: 20, size: 40),
            const SizedBox(
              width: 12,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    data.bankName ?? '',
                    style: TextStyle(height: 1.4, fontSize: 15, fontWeight: FontWeight.bold, color: MColor.xFFFFFFFF),
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  Text(
                    '信用账户',
                    style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF99CEC2),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  data.annualMemo ?? '',
                  style: TextStyle(height: 1.4, fontSize: 15, fontWeight: FontWeight.bold, color: MColor.xFFFFFFFF),
                ),
                const SizedBox(
                  height: 4,
                ),
                Builder(builder: (context) {
                  var desc = '';
                  if (data.exemptionConditions?.conditionType == '1') {
                    if (data.exemptionConditions!.countTimeUnit == '1') {
                      desc = '日消费${data.spendPriceCount}/${data.exemptionConditions!.count}次';
                    } else if (data.exemptionConditions!.countTimeUnit == '2') {
                      desc = '周消费${data.spendPriceCount}/${data.exemptionConditions!.count}次';
                    } else if (data.exemptionConditions!.countTimeUnit == '3') {
                      desc = '月消费${data.spendPriceCount}/${data.exemptionConditions!.count}次';
                    } else {
                      desc = '年消费${data.spendPriceCount}/${data.exemptionConditions!.count}次';
                    }
                  } else {
                    desc = '年消费${data.spendPriceCount}/${data.exemptionConditions!.amount}元';
                  }
                  return Text(
                    desc,
                    style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF99CEC2),
                  );
                }),
              ],
            )
          ],
        ),
      ),
    );
  }
}
