import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/bookkeeping_controller.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/remark_panel_controller.dart';
import 'package:qiazhun/tools/tools.dart';

class GetxPageWrapper extends StatefulWidget {
  final Bindings binding;
  final Widget child;

  const GetxPageWrapper({
    super.key,
    required this.binding,
    required this.child,
  });

  @override
  State<GetxPageWrapper> createState() => _GetxPageWrapperState();
}

class _GetxPageWrapperState extends State<GetxPageWrapper> {
  @override
  void initState() {
    super.initState();
    // 在构建 child 之前，先执行依赖注入
    widget.binding.dependencies();
  }

  @override
  void dispose() {
    // 页面销毁时，删除通过此 binding 创建的控制器
    // 这会触发控制器的 onClose 方法
    _cleanupControllers();
    super.dispose();
  }

  void _cleanupControllers() {
    // 根据 binding 类型删除对应的控制器
    final bindingType = widget.binding.runtimeType.toString();

    try {
      if (bindingType.contains('BookkeepingBinding')) {
        if (Get.isRegistered<BookkeepingController>()) {
          Get.delete<BookkeepingController>(force: true);
        }
      } else if (bindingType.contains('RemarkPanelBinding')) {
        if (Get.isRegistered<RemarkPanelController>()) {
          Get.delete<RemarkPanelController>(force: true);
        }
      }
      // 可以根据需要添加更多控制器类型的清理逻辑
    } catch (e) {
      // 忽略清理过程中的错误，避免影响页面正常退出
      logger.w('Error cleaning up controllers: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
