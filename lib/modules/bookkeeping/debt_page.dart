import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/modules/account/section_header.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class DebtPage extends StatefulWidget {
  const DebtPage({super.key});

  @override
  State<StatefulWidget> createState() => _DebtState();
}

class _DebtState extends State<DebtPage> {
  bool _isBorrow = true; //true借入 false借出
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _remarkController = TextEditingController();

  AccountModel? _accountItem1;
  AccountModel? _accountItem2;

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        dividerTheme: DividerThemeData(
          color: Colors.transparent,
        ),
      ),
      child: Scaffold(
        extendBody: true,
        persistentFooterButtons: [_submitButton],
        body: SafeArea(
          // maintainBottomViewPadding: true,
          top: false,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      scrolledUnderElevation: 0,
                      backgroundColor: Colors.transparent,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        '借支',
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                // top: 0,
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 14.0),
                    child: Column(
                      children: [
                        _debtTypeSection,
                        const SizedBox(
                          height: 14,
                        ),
                        _amountSection,
                        const SizedBox(
                          height: 14,
                        ),
                        _subjectSection,
                        const SizedBox(
                          height: 14,
                        ),
                        _accountSection,
                        const SizedBox(
                          height: 14,
                        ),
                        _remarkSection,
                        const SizedBox(
                          height: 14,
                        ),
                      ],
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget get _subjectSection {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionHeader(_isBorrow ? '借款对象' : '借出对象'),
        const SizedBox(
          height: 14,
        ),
        _arrowView(
            onTap: () {
              RouterHelper.router.pushNamed(Routes.accountListPath,
                  extra: {'selectMode': true, 'limitAccount': _isBorrow ? '1' : '2', 'initialTab': 5, 'initialSubTab': _isBorrow ? 0 : 1}).then((value) {
                if (value != null && value is Map<String, dynamic> && value.containsKey('selected')) {
                  _accountItem1 = value['selected'] as AccountModel;
                  setState(() {});
                }
              });
            },
            text: _accountItem1 == null ? (_isBorrow ? '请选择借款对象' : '请选择借出对象') : (_accountItem1?.getShortDesc() ?? '')),
        const SizedBox(
          height: 14,
        ),
      ],
    );
  }

  Widget get _accountSection {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionHeader(_isBorrow ? '存入账户' : '支出账户'),
        const SizedBox(
          height: 14,
        ),
        _arrowView(
            onTap: () {
              RouterHelper.router.pushNamed(Routes.accountListPath,
                  extra: {'selectMode': true, 'limitAccount': _isBorrow ? '3' : '4', 'initialTab': _isBorrow ? 2 : 4}).then((value) {
                if (value != null && value is Map<String, dynamic> && value.containsKey('selected')) {
                  _accountItem2 = value['selected'] as AccountModel;
                  setState(() {});
                }
              });
            },
            text: _accountItem2 == null ? (_isBorrow ? '请选择存入账户' : '请选择支出账户') : (_accountItem2?.getShortDesc() ?? '')),
        const SizedBox(
          height: 14,
        ),
      ],
    );
  }

  Widget get _amountSection {
    return Column(
      children: [
        SectionHeader(_isBorrow ? '借入金额' : '借出金额'),
        const SizedBox(
          height: 14,
        ),
        _inputTextView(
            controller: _amountController,
            hintText: _isBorrow ? '借入金额' : '借出金额',
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))]),
      ],
    );
  }

  Widget get _debtTypeSection {
    List<String> methodStr = ['借入', '借出'];
    List<Widget> widgets = List.generate(methodStr.length, (index) {
      bool isSelected = (_isBorrow && index == 0) || (!_isBorrow && index == 1);
      return GestureDetector(
        onTap: () {
          if (_isBorrow != (index == 0)) {
            _isBorrow = index == 0;
            _accountItem1 = null;
            _accountItem2 = null;
            setState(() {});
          }
        },
        child: Row(
          children: [
            Icon(isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked, size: 16, color: isSelected ? MColor.skin : MColor.xFF999999),
            const SizedBox(
              width: 6,
            ),
            Text(
              methodStr[index],
              style: TextStyle(height: 1.4, fontSize: 14, color: isSelected ? MColor.xFF333333 : MColor.xFF999999),
            ),
            const SizedBox(
              width: 16,
            )
          ],
        ),
      );
    });
    return Column(
      children: [
        SectionHeader('选择类型'),
        const SizedBox(
          height: 14,
        ),
        Row(
          children: widgets,
        )
      ],
    );
  }

  Widget get _remarkSection {
    return Column(
      children: [
        SectionHeader('备注'),
        const SizedBox(
          height: 14,
        ),
        _inputTextView(controller: _remarkController, hintText: '请输入备注内容'),
      ],
    );
  }

  Widget _arrowView({required GestureTapCallback onTap, required String text}) {
    return GestureDetector(
      onTap: () {
        onTap.call();
      },
      child: Container(
        height: 50,
        padding: EdgeInsets.symmetric(horizontal: 14),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: MColor.xFFFFFFFF),
        child: Row(
          children: [
            Expanded(
                child: Text(
              text,
              style: TextStyle(height: 1.4, fontSize: 14, color: MColor.xFF999999),
            )),
            Icon(
              Icons.arrow_forward_ios,
              size: 14,
              color: MColor.xFF999999,
            )
          ],
        ),
      ),
    );
  }

  Widget _inputTextView(
      {required TextEditingController controller, required String hintText, TextInputType? keyboardType, List<TextInputFormatter>? inputFormatters}) {
    return TextField(
      controller: controller,
      keyboardType: keyboardType ?? TextInputType.text,
      inputFormatters: inputFormatters,
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
        filled: true,
        fillColor: Colors.white,
        contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 15),
        isDense: true,
      ),
    );
  }

  Widget get _submitButton {
    return GestureDetector(
      onTap: () async {
        if (_amountController.text.isEmpty) {
          showToast('请填写金额');
          return;
        }
        if (_accountItem1 == null || _accountItem2 == null) {
          showToast('请填写对象和账户');
          return;
        }
        Loading.show();
        try {
          var resp = await AccountRepo.addBorrowLend(
              _isBorrow ? '1' : '2', '${_accountItem1?.id}', '${_accountItem2?.id}', _amountController.text, _remarkController.text);
          if (resp.code == 1) {
            showToast('添加成功');
            RouterHelper.router.pop();
            return;
          } else {
            showToast(resp.msg ?? '失败');
          }
        } catch (e) {
          showToast('失败 $e');
        } finally {
          Loading.dismiss();
        }
      },
      child: Container(
        height: 50,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            gradient:
                LinearGradient(begin: const FractionalOffset(0.0, 0.0), end: const FractionalOffset(0.0, 1.0), colors: [Color(0xFF68C2BF), Color(0xFF4C9B93)])),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '确认记录',
              style: TextStyle(color: MColor.xFFFFFFFF, fontWeight: FontWeight.w500, fontSize: 16, height: 1),
            ),
          ],
        ),
      ),
    );
  }
}
