import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/round_image.dart';

class CategoryListPage extends StatefulWidget {
  final String categoryType;
  const CategoryListPage(this.categoryType, {super.key});

  @override
  State<StatefulWidget> createState() => _CategoryListState();
}

class _CategoryListState extends State<CategoryListPage> with TickerProviderStateMixin {
  final TextEditingController _nameController = TextEditingController();

  late TabController _tabController;
  final List<CategoryItem> allCategories = [];

  int _tabIndex = 0;
  CategoryItem? _selectedItem;

  @override
  void initState() {
    super.initState();
    _tabIndex = 0;
    _initData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _initData() async {
    await BookkeepingStore.to.getBookkeepingCategory(widget.categoryType);
    allCategories.clear();
    allCategories.addAll(widget.categoryType == '1' ? BookkeepingStore.to.allOutcomeCategories : BookkeepingStore.to.allIncomeCategories);
    _tabController = TabController(initialIndex: _tabIndex, length: allCategories.length, vsync: this);
    _tabController.addListener(() {
      logger.i('_initData change tab ${_tabController.index}');
      setState(() {
        _tabIndex = _tabController.index;
      });
    });

    setState(() {});
  }

  Widget _itemView(int index, double width, CategoryItem item) {
    return GestureDetector(
      onTap: () {
        setState(() {
          if (_selectedItem == item) {
            _selectedItem = null;
          } else {
            _selectedItem = item;
          }
        });
      },
      child: SizedBox(
        width: width,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
                width: 40,
                decoration: BoxDecoration(
                    color: MColor.xFFECECEC,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(width: 3, color: _selectedItem == item ? MColor.skin : MColor.xFFECECEC)),
                child: RoundImage(
                  imageUrl: getImageUrl(item.bookkeepingCategoryIcon ?? ''),
                  radius: 17,
                  size: 34,
                  backgroundColor: MColor.skin,
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildTabCell(CategoryItem item) {
    return Builder(builder: (context) {
      double width = (MediaQuery.of(context).size.width - 28) / 5;
      List<Widget> items = List.generate(item.threeCategoryList?.length ?? 0, (index) {
        return _itemView(index, width, item.threeCategoryList![index]);
      });
      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: GridView.count(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      crossAxisCount: 5,
                      // controller: _scrollController,
                      physics: const ClampingScrollPhysics(),
                      scrollDirection: Axis.vertical,
                      // padding: EdgeInsets.symmetric(horizontal: 14),
                      childAspectRatio: 1.2,
                      crossAxisSpacing: 4,
                      mainAxisSpacing: 4,
                      children: items),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      extendBody: true,
      body: SafeArea(
        top: false,
        bottom: false,
        child: Container(
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      AppBar(
                        backgroundColor: Colors.transparent,
                        scrolledUnderElevation: 0,
                        centerTitle: true,
                        leading: IconButton(
                          icon: Image.asset(
                            'assets/images/ic_back.png',
                            width: 24,
                            height: 24,
                          ),
                          onPressed: () {
                            RouterHelper.router.pop();
                          },
                        ),
                        titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                        title: Text(
                          '添加分类',
                          style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                        ),
                      ),
                    ],
                  )),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: Builder(builder: (context) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 14.0),
                          child: SizedBox(
                            height: 35,
                            child: TextField(
                              controller: _nameController,
                              keyboardType: TextInputType.text,
                              inputFormatters: [
                                LengthLimitingTextInputFormatter(4),
                              ],
                              decoration: InputDecoration(
                                hintText: '输入添加分类名称（最长4个字）',
                                hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                                border: OutlineInputBorder(borderRadius: BorderRadius.circular(18), borderSide: BorderSide.none),
                                filled: true,
                                fillColor: Colors.white,
                                isDense: true,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 14,
                        ),
                        if (allCategories.isNotEmpty) ...{
                          SizedBox(
                            height: 56,
                            child: Row(
                              children: [
                                const SizedBox(
                                  width: 14,
                                ),
                                Expanded(
                                  child: Container(
                                    child: TabBar(
                                        // physics: const NeverScrollableScrollPhysics(),
                                        // padding: EdgeInsets.only(bottom: 20),
                                        // indicatorPadding: EdgeInsets.only(top: 20),
                                        labelPadding: EdgeInsets.symmetric(horizontal: 0.0),
                                        controller: _tabController,
                                        indicatorSize: TabBarIndicatorSize.tab,
                                        // indicatorWeight: 1,
                                        isScrollable: true,
                                        tabAlignment: TabAlignment.start,
                                        dividerColor: Colors.transparent,
                                        indicatorColor: Colors.transparent,
                                        labelStyle: MFont.medium14.apply(color: MColor.xFF000000),
                                        tabs: List<Tab>.generate(
                                            allCategories.length,
                                            (index) => Tab(
                                                iconMargin: EdgeInsets.zero,
                                                child: Container(
                                                  padding: EdgeInsets.symmetric(horizontal: 14, vertical: 5),
                                                  decoration: BoxDecoration(
                                                      borderRadius: BorderRadius.circular(28), color: _tabIndex == index ? MColor.skin : Colors.transparent),
                                                  child: Text(
                                                    allCategories[index].bookkeepingCategoryName ?? '',
                                                    style: TextStyle(
                                                        color: _tabIndex == index ? (MColor.xFFFFFFFF) : (MColor.xFF1B1C1A), height: 1.4, fontSize: 14),
                                                  ),
                                                ))).toList()),
                                  ),
                                ),
                                const SizedBox(
                                  width: 14,
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            child: DefaultTabController(
                              length: allCategories.length,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 14.0),
                                child: TabBarView(
                                  controller: _tabController,
                                  children: List.generate(allCategories.length, (index) {
                                    return _buildTabCell(allCategories[index]);
                                    // return const SizedBox();
                                  }),
                                ),
                              ),
                            ),
                          ),
                          _bottomView
                        }
                      ],
                    );
                  }))
            ],
          ),
        ),
      ),
    );
  }

  Widget get _bottomView {
    return GestureDetector(
      onTap: () async {
        if (_selectedItem == null) {
          showToast('请先选择分类图标');
          return;
        }
        if (_nameController.text.isEmpty) {
          showToast('请输入分类名称');
          return;
        }
        Loading.show();
        try {
          var resp = await BookkeepingRepo.addBookkeepingCategory(
              _selectedItem!.pid!, _selectedItem!.bookkeepingCategoryIcon!, _nameController.text, widget.categoryType);
          if (resp.code == 1) {
            showToast('添加成功');
            RouterHelper.router.pop(1);
          } else {
            showToast(resp.msg ?? '添加失败');
          }
        } catch (e, stackTrace) {
          showToast('添加失败');
          debugPrintStack(stackTrace: stackTrace, maxFrames: 4);
          logger.e('add category item failed $e');
        } finally {
          Loading.dismiss();
        }
      },
      child: Container(
        margin: EdgeInsets.fromLTRB(14, 14, 14, 14),
        decoration: BoxDecoration(
          gradient: LinearGradient(colors: [Color(0xFF68C2BF), Color(0xFF315C5B)], begin: Alignment.topCenter, end: Alignment.bottomCenter),
          borderRadius: BorderRadius.circular(25),
        ),
        height: 50,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '保存',
              style: TextStyle(height: 1.4, fontSize: 14, color: MColor.xFFFFFFFF),
            ),
          ],
        ),
      ),
    );
  }
}
