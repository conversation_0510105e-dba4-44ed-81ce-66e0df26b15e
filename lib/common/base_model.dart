import 'package:get/get.dart';
import 'package:json_annotation/json_annotation.dart';

/**
 * {
  "ErrCode": "0",
  "ErrMsg": "Success",
  "Success": true,
  "ResData": {
    "Uid": "17463996863742022",
    "code": "37432058",
    "Nickname": "N37432058",
    "email": "<EMAIL>",
    "mobile": "",
    "nation_code": "",
    "walletid": "TU1u9Yz8wnwv25aYtJevQvjGg7yvfXF4Po",
    "invite_code": "524589",
    "ProfileUrl": "https://show.keepbit.top/",
    "IsCertified": false
  }
}
 */

part 'base_model.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class BaseModel<T> {
  @JsonKey(name: 'code')
  int? code;
  @Json<PERSON>ey(name: 'msg')
  String? msg;
  @Json<PERSON>ey(name: 'serial_number')
  String? serialNumber;
  @Json<PERSON>ey(name: 'time')
  String? time;
  @JsonKey(name: 'data')
  T? data;

  factory BaseModel.fromJson(Map<String, dynamic> resp, T Function(dynamic json) func) {
    return _$BaseModelFromJson<T>(resp, func);
  }

  BaseModel({this.code, this.msg, this.serialNumber, this.time, this.data});
}
