import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// 计划数据模型
class Plan {
  final String id;
  final String title;
  final IconData icon;
  final int currentValue;
  final int targetValue;
  final int daysPassed;
  final int? daysRemaining;

  Plan({
    required this.id,
    required this.title,
    required this.icon,
    required this.currentValue,
    required this.targetValue,
    required this.daysPassed,
    this.daysRemaining,
  });
}

// 计划列表的状态管理
final planListProvider = StateNotifierProvider<PlanListNotifier, List<Plan>>((ref) {
  return PlanListNotifier();
});

class PlanListNotifier extends StateNotifier<List<Plan>> {
  PlanListNotifier() : super([
    Plan(
      id: '1',
      title: '环游世界',
      icon: Icons.flight,
      currentValue: 200,
      targetValue: 200,
      daysPassed: 100,
    ),
    Plan(
      id: '2',
      title: '运动健身',
      icon: Icons.fitness_center,
      currentValue: 200,
      targetValue: 500,
      daysPassed: 5,
      daysRemaining: 25,
    ),
  ]);

  void addPlan(Plan plan) {
    state = [...state, plan];
  }

  void updatePlan(Plan plan) {
    state = [
      for (final item in state)
        if (item.id == plan.id) plan else item
    ];
  }

  void removePlan(String id) {
    state = state.where((plan) => plan.id != id).toList();
  }
}
