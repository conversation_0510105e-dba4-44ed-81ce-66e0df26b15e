# CustomPopupPanel 高度设置使用指南

## 概述

`CustomPopupPanel` 现在支持自定义高度设置，可以通过 `heightFraction` 参数控制弹窗占屏幕高度的比例。

## 参数说明

### heightFraction
- **类型**: `double?`
- **默认值**: `0.5` (屏幕的一半高度)
- **取值范围**: `0.25` - `0.95`
- **说明**: 控制弹窗高度占屏幕高度的比例

### 常用高度比例

| 比例值 | 描述 | 适用场景 |
|--------|------|----------|
| `1/3` (≈0.33) | 三分之一高度 | 简单表单、确认对话框 |
| `0.5` | 一半高度（默认） | 中等内容量的表单 |
| `2/3` (≈0.67) | 三分之二高度 | 较多内容的表单 |
| `0.75` | 四分之三高度 | 复杂表单、列表选择 |
| `0.25` | 最小高度 | 简单提示、快速操作 |
| `0.95` | 最大高度 | 复杂内容、详细信息 |

## 使用方法

### 1. 基本用法（使用默认高度）

```dart
RouterHelper.router.pushNamed(
  Routes.customPopupPath,
  extra: {
    'title': '标题',
    'widget': yourWidget,
    'onConfirm': () async {
      // 确认逻辑
    },
  },
);
```

### 2. 设置三分之一高度

```dart
RouterHelper.router.pushNamed(
  Routes.customPopupPath,
  extra: {
    'title': '简单表单',
    'widget': yourWidget,
    'heightFraction': 1/3, // 三分之一高度
    'onConfirm': () async {
      // 确认逻辑
    },
  },
);
```

### 3. 设置三分之二高度

```dart
RouterHelper.router.pushNamed(
  Routes.customPopupPath,
  extra: {
    'title': '复杂表单',
    'widget': yourWidget,
    'heightFraction': 2/3, // 三分之二高度
    'onConfirm': () async {
      // 确认逻辑
    },
  },
);
```

### 4. 设置自定义高度

```dart
RouterHelper.router.pushNamed(
  Routes.customPopupPath,
  extra: {
    'title': '自定义高度',
    'widget': yourWidget,
    'heightFraction': 0.8, // 80% 屏幕高度
    'onConfirm': () async {
      // 确认逻辑
    },
  },
);
```

## 实际应用示例

### 示例1：添加讨账记录（三分之一高度）

```dart
RouterHelper.router.pushNamed(
  Routes.customPopupPath,
  extra: {
    'title': '添加讨账记录',
    'widget': collectionForm,
    'heightFraction': 1/3, // 适合简单表单
    'onConfirm': () async {
      if (amountController.text.isEmpty) {
        showToast('请输入讨要金额');
        return;
      }
      await _addCollectionRecord(cardId, amountController.text);
    },
  },
);
```

### 示例2：账户选择（一半高度）

```dart
RouterHelper.router.pushNamed(
  Routes.customPopupPath,
  extra: {
    'title': '选择账户',
    'widget': accountListWidget,
    'heightFraction': 0.5, // 默认高度，适合中等内容
    'onConfirm': () async {
      // 确认选择
    },
  },
);
```

### 示例3：详细设置（四分之三高度）

```dart
RouterHelper.router.pushNamed(
  Routes.customPopupPath,
  extra: {
    'title': '详细设置',
    'widget': settingsWidget,
    'heightFraction': 0.75, // 适合复杂内容
    'onConfirm': () async {
      // 保存设置
    },
  },
);
```

## 注意事项

1. **最小高度限制**: 弹窗最小高度为屏幕的 25%，即使设置更小的值也会被限制到 0.25
2. **最大高度限制**: 弹窗最大高度为屏幕的 95%，即使设置更大的值也会被限制到 0.95
3. **拖拽功能**: 用户仍然可以通过拖拽来调整弹窗高度，设置的高度只是初始高度
4. **响应式设计**: 高度比例会根据不同屏幕尺寸自动适配
5. **向后兼容**: 如果不设置 `heightFraction` 参数，将使用默认的 0.5（一半高度）

## 最佳实践

1. **根据内容选择高度**: 
   - 简单表单使用 1/3 高度
   - 中等内容使用默认 1/2 高度
   - 复杂内容使用 2/3 或 3/4 高度

2. **考虑用户体验**: 
   - 避免使用过小的高度导致内容拥挤
   - 避免使用过大的高度导致空间浪费

3. **测试不同屏幕**: 
   - 在不同尺寸的设备上测试效果
   - 确保内容在各种高度下都能正常显示
