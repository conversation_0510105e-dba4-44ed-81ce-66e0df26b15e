import 'dart:async';

import 'package:get/get.dart';

abstract class TimerObserver {
  void onTimerUpdate();
}

class TimerUpdate extends GetxController {
  List<TimerObserver> _observers = [];
  late Timer _timer;
  static TimerUpdate get to => Get.find();

  @override
  void onInit() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      for (var observer in _observers) {
        observer.onTimerUpdate();
      }
    });
    super.onInit();
  }

  @override
  void onClose() {
    _timer.cancel();
    super.onClose();
  }

  void addUpdateListener(TimerObserver observer) {
    _observers.add(observer);
  }

  void removeUpdateListener(TimerObserver observer) {
    _observers.remove(observer);
  }
}
