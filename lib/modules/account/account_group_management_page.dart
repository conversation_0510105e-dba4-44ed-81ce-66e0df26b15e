import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_group_model.dart' as group_model;
import 'package:qiazhun/modules/account/account_group_repo.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_item_view.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/page_container_view.dart';
import 'package:qiazhun/widgets/custom_popup_panel.dart';
import 'package:qiazhun/widgets/submit_button.dart';

class AccountGroupManagementPage extends StatefulWidget {
  const AccountGroupManagementPage({super.key});

  @override
  State<AccountGroupManagementPage> createState() => _AccountGroupManagementPageState();
}

class _AccountGroupManagementPageState extends State<AccountGroupManagementPage> {
  bool _isLoading = true;
  final List<AccountGroupModel> _accountGroups = [];
  final Map<int, bool> _expandedGroups = {}; // Track which groups are expanded
  bool _isReorderMode = false; // Track if we're in reorder mode

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });
    Loading.show();
    try {
      var resp = await AccountGroupRepo.getAccountGroupList();
      if (resp.code == 1) {
        _accountGroups.clear();
        _accountGroups.addAll(resp.data ?? []);
        // Initialize all groups as collapsed
        _expandedGroups.clear();
        for (var group in _accountGroups) {
          _expandedGroups[group.id!] = false;
        }
      } else {
        showToast(resp.msg ?? '获取分组数据失败');
      }
    } catch (e) {
      showToast('获取数据失败 $e');
    } finally {
      Loading.dismiss();
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _addGroup() async {
    final TextEditingController controller = TextEditingController();

    await RouterHelper.router.pushNamed(
      Routes.customPopupPath,
      extra: {
        'title': '添加分组',
        'heightFraction': 1 / 3,
        'widget': Padding(
          padding: const EdgeInsets.all(16),
          child: TextField(
            controller: controller,
            decoration: const InputDecoration(
              hintText: '请输入分组名称',
              border: OutlineInputBorder(),
            ),
            autofocus: true,
          ),
        ),
        'onConfirm': () async {
          if (controller.text.trim().isEmpty) {
            showToast('请输入分组名称');
            return;
          }

          Loading.show();
          try {
            var resp = await AccountGroupRepo.addAccountGroup(name: controller.text.trim());
            if (resp.code == 1) {
              showToast('添加成功');
              await _loadData();
            } else {
              showToast(resp.msg ?? '添加失败');
            }
          } catch (e) {
            showToast('添加失败 $e');
          } finally {
            Loading.dismiss();
          }
        },
      },
    );
  }

  Future<void> _editGroupName(AccountGroupModel group) async {
    final TextEditingController controller = TextEditingController(text: group.name);

    await RouterHelper.router.pushNamed(
      Routes.customPopupPath,
      extra: {
        'title': '修改分组名称',
        'heightFraction': 1 / 3,
        'widget': Padding(
          padding: const EdgeInsets.all(16),
          child: TextField(
            controller: controller,
            decoration: const InputDecoration(
              hintText: '请输入分组名称',
              border: OutlineInputBorder(),
            ),
            autofocus: true,
          ),
        ),
        'onConfirm': () async {
          if (controller.text.trim().isEmpty) {
            showToast('请输入分组名称');
            return;
          }

          Loading.show();
          try {
            var resp = await AccountGroupRepo.editAccountGroup(
              id: group.id!,
              name: controller.text.trim(),
            );
            if (resp.code == 1) {
              showToast('修改成功');
              await _loadData();
            } else {
              showToast(resp.msg ?? '修改失败');
            }
          } catch (e) {
            showToast('修改失败 $e');
          } finally {
            Loading.dismiss();
          }
        },
      },
    );
  }

  Future<void> _deleteGroup(AccountGroupModel group) async {
    showCustomDialog(
      '确认删除',
      content: '删除后不可恢复，请确认是否删除分组"${group.name}"',
      cancel: true,
      onConfirm: () async {
        Loading.show();
        try {
          var resp = await AccountGroupRepo.deleteAccountGroup(id: group.id!);
          if (resp.code == 1) {
            showToast('删除成功');
            await _loadData();
          } else {
            showToast(resp.msg ?? '删除失败');
          }
        } catch (e) {
          showToast('删除失败 $e');
        } finally {
          Loading.dismiss();
        }
      },
      onCancel: () {},
    );
  }

  Future<void> _removeAccountFromGroup(AccountGroupModel group, AccountModel account) async {
    Loading.show();
    try {
      var resp = await AccountGroupRepo.unbindAccountsFromGroup(
        groupId: group.id!,
        cardIds: [account.id!],
      );
      if (resp.code == 1) {
        showToast('移除成功');
        await _loadData();
      } else {
        showToast(resp.msg ?? '移除失败');
      }
    } catch (e) {
      showToast('移除失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  void _onReorder(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final group = _accountGroups.removeAt(oldIndex);
      _accountGroups.insert(newIndex, group);
    });

    // Save the new order to the server
    _saveGroupOrder();
  }

  Future<void> _saveGroupOrder() async {
    try {
      List<int> sortedIds = _accountGroups.map((group) => group.id!).toList();
      var resp = await AccountGroupRepo.sortAccountGroups(sortedIds: sortedIds);
      if (resp.code != 1) {
        showToast(resp.msg ?? '保存排序失败');
        // Reload data to restore original order
        await _loadData();
      }
    } catch (e) {
      showToast('保存排序失败 $e');
      // Reload data to restore original order
      await _loadData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
      title: '分组管理',
      actions: [
        TextButton(
          onPressed: () {
            setState(() {
              _isReorderMode = !_isReorderMode;
              if (_isReorderMode) {
                // Collapse all groups when entering reorder mode
                for (var groupId in _expandedGroups.keys) {
                  _expandedGroups[groupId] = false;
                }
              }
            });
          },
          child: Text(
            _isReorderMode ? '完成' : '排序',
            style: TextStyle(
              color: MColor.skin,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Expanded(
                  child: _isReorderMode
                      ? ReorderableListView.builder(
                          padding: const EdgeInsets.all(14),
                          itemCount: _accountGroups.length,
                          onReorder: _onReorder,
                          itemBuilder: (context, index) {
                            return Container(
                              key: ValueKey(_accountGroups[index].id),
                              margin: const EdgeInsets.only(bottom: 14),
                              child: _buildGroupItem(_accountGroups[index]),
                            );
                          },
                        )
                      : ListView.separated(
                          padding: const EdgeInsets.all(14),
                          itemCount: _accountGroups.length,
                          separatorBuilder: (context, index) => const SizedBox(height: 14),
                          itemBuilder: (context, index) {
                            return _buildGroupItem(_accountGroups[index]);
                          },
                        ),
                ),
              ],
            ),
      footerButtons: [
        SubmitButton(text: '添加分组', onTap: _addGroup),
      ],
    );
  }

  Widget _buildGroupItem(AccountGroupModel group) {
    final isExpanded = _expandedGroups[group.id] ?? false;
    final isCustomGroup = group.type == 0;

    return Container(
      decoration: BoxDecoration(
        color: MColor.xFFFFFFFF,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        children: [
          // Group header
          ListTile(
            onTap: () {
              setState(() {
                _expandedGroups[group.id!] = !isExpanded;
              });
            },
            leading: Icon(
              isExpanded ? Icons.expand_less : Icons.expand_more,
              color: MColor.xFF999999,
            ),
            title: Text(
              group.name ?? '',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: MColor.xFF1B1C1A,
              ),
            ),
            subtitle: Text(
              '${group.accountCount}个账户',
              style: TextStyle(
                fontSize: 12,
                color: MColor.xFF999999,
              ),
            ),
            trailing: isCustomGroup
                ? Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(Icons.edit, color: MColor.xFF999999, size: 20),
                        onPressed: () => _editGroupName(group),
                      ),
                      IconButton(
                        icon: Icon(Icons.delete, color: MColor.xFFFF7858, size: 20),
                        onPressed: () => _deleteGroup(group),
                      ),
                    ],
                  )
                : null,
          ),
          // Expanded content
          if (isExpanded) ...[
            const Divider(height: 1),
            ...group.accounts?.map((account) => _buildAccountItem(group, account)) ?? [],
            if (isCustomGroup) _buildAddAccountButton(group),
          ],
        ],
      ),
    );
  }

  Widget _buildAccountItem(AccountGroupModel group, AccountModel account) {
    final isCustomGroup = group.type == 0;

    return Slidable(
      key: ValueKey('${group.id}_${account.id}'),
      enabled: isCustomGroup,
      endActionPane: isCustomGroup
          ? ActionPane(
              motion: const ScrollMotion(),
              children: [
                SlidableAction(
                  onPressed: (context) => _removeAccountFromGroup(group, account),
                  backgroundColor: MColor.xFFFF7858,
                  foregroundColor: Colors.white,
                  icon: Icons.delete,
                  label: '移除',
                ),
              ],
            )
          : null,
      child: AccountItemView(account),
    );
  }

  Widget _buildAddAccountButton(AccountGroupModel group) {
    return ListTile(
      onTap: () {
        RouterHelper.router.pushNamed(Routes.accountSelectionPath, extra: {'groupId': group.id}).then((_) {
          _loadData();
        });
      },
      leading: Icon(Icons.add, color: MColor.xFF68C2BF),
      title: Text(
        '添加账户',
        style: TextStyle(
          fontSize: 14,
          color: MColor.xFF68C2BF,
        ),
      ),
    );
  }
}
