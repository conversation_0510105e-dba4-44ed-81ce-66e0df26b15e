import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_add_page.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class AccountAddBigAssetsSection extends StatefulWidget {
  final AccountAddController? addController;
  final AccountModel? accountModel;

  const AccountAddBigAssetsSection({super.key, this.addController, this.accountModel});
  @override
  State<StatefulWidget> createState() => _AccountAddBigAssetsState();
}

class _AccountAddBigAssetsState extends State<AccountAddBigAssetsSection> {
  bool _inAssets = true;
  bool _isProfit = false;
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _percentController = TextEditingController();

  @override
  void initState() {
    widget.addController?.onAction = _addAccount;
    if (widget.accountModel != null) {
      _nameController.text = widget.accountModel!.accountName ?? '';
      _priceController.text = widget.accountModel!.balance ?? '';
      _isProfit = widget.accountModel!.subType != '1';
      _percentController.text = widget.accountModel!.proportion ?? '';
      _inAssets = widget.accountModel!.isJoinTotal == 1;
    }
    super.initState();
  }

  Future<void> _addAccount() async {
    if (_nameController.text.isEmpty) {
      showToast('请填写账户名称');
      return;
    }
    if (_priceController.text.isEmpty) {
      showToast('请填写金额');
      return;
    }
    if (_percentController.text.isEmpty) {
      showToast('请填写百分比');
      return;
    }

    Loading.show();
    try {
      var resp = await AccountRepo.addSavingCard(
          id: widget.accountModel?.id,
          accountType: '8',
          propertyPrice: _priceController.text,
          accountName: _nameController.text,
          isJoinTotal: _inAssets ? '1' : '2',
          propertyProportion: _percentController.text,
          propertyType: _isProfit ? '2' : '1');
      if (resp.code == 1) {
        showToast('添加账号成功');
        RouterHelper.router.pop();
      } else {
        showToast(resp.msg ?? '添加账号失败');
      }
    } catch (e) {
      showToast('添加账号失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14, vertical: 0),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
      child: Column(children: [
        ListTile(
          dense: true,
          contentPadding: EdgeInsets.zero,
          leading: Text(
            '账户名称',
            style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
          ),
          trailing: Container(
            width: 200,
            // color: Colors.blue,
            child: TextField(
              controller: _nameController,
              keyboardType: TextInputType.text,
              textAlign: TextAlign.end,
              decoration: InputDecoration(
                hintText: '请输入',
                hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                border: OutlineInputBorder(borderSide: BorderSide.none),
                filled: true,
                fillColor: Colors.transparent,
                contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                isDense: true,
              ),
            ),
          ),
        ),
        Divider(
          height: 0.5,
          thickness: 0.5,
          color: MColor.xFFF5F5F5,
        ),
        ListTile(
          dense: true,
          contentPadding: EdgeInsets.zero,
          leading: Text(
            '金额',
            style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
          ),
          trailing: Container(
            width: 200,
            // color: Colors.blue,
            child: TextField(
              controller: _priceController,
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()],
              textAlign: TextAlign.end,
              decoration: InputDecoration(
                hintText: '请输入',
                hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                border: OutlineInputBorder(borderSide: BorderSide.none),
                filled: true,
                fillColor: Colors.transparent,
                contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                isDense: true,
              ),
            ),
          ),
        ),
        Divider(
          height: 0.5,
          thickness: 0.5,
          color: MColor.xFFF5F5F5,
        ),
        _profitTypeSection,
        Divider(
          height: 0.5,
          thickness: 0.5,
          color: MColor.xFFF5F5F5,
        ),
        ListTile(
          dense: true,
          contentPadding: EdgeInsets.zero,
          leading: Text(
            '0-100百分比/年',
            style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
          ),
          trailing: Container(
            width: 200,
            // color: Colors.blue,
            child: TextField(
              controller: _percentController,
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9]')), TwoDecimalInputFormatter()],
              textAlign: TextAlign.end,
              decoration: InputDecoration(
                hintText: '请输入',
                hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                border: OutlineInputBorder(borderSide: BorderSide.none),
                filled: true,
                fillColor: Colors.transparent,
                contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                isDense: true,
              ),
            ),
          ),
        ),
        Divider(
          height: 0.5,
          thickness: 0.5,
          color: MColor.xFFF5F5F5,
        ),
        ListTile(
          dense: true,
          contentPadding: EdgeInsets.zero,
          leading: Text(
            '是否计入资产',
            style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
          ),
          trailing: SizedBox(
            height: 24,
            child: Switch(
              value: _inAssets,
              activeTrackColor: MColor.skin,
              activeColor: MColor.xFFFFFFFF,
              onChanged: (value) {
                setState(() {
                  _inAssets = !_inAssets;
                });
              },
            ),
          ),
        ),
      ]),
    );
  }

  List<String> _profitTypeStr = ['折旧', '增值'];

  Widget get _profitTypeSection {
    List<Widget> items = List.generate(_profitTypeStr.length, (index) {
      bool isSelected = (index == 0 && !_isProfit || index == 1 && _isProfit);
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          setState(() {
            _isProfit = index == 1;
          });
        },
        child: Row(
          children: [
            Icon(isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked, size: 16, color: isSelected ? MColor.skin : MColor.xFF999999),
            const SizedBox(
              width: 6,
            ),
            Text(
              _profitTypeStr[index],
              style: TextStyle(height: 1.4, fontSize: 14, color: isSelected ? MColor.skin : MColor.xFF333333),
            ),
            if (index != _profitTypeStr.length - 1)
              const SizedBox(
                width: 16,
              ),
          ],
        ),
      );
    });
    return Container(
      // margin: EdgeInsets.symmetric(horizontal: 14),
      padding: EdgeInsets.symmetric(horizontal: 0, vertical: 12),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: items,
      ),
    );
  }
}
