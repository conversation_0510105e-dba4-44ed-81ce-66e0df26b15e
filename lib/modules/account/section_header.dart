import 'package:flutter/material.dart';
import 'package:qiazhun/constants/design.dart';

class SectionHeader extends StatelessWidget {
  final String headerStr;
  final String? trailingStr;
  final Color? trailingColor;
  final Function()? onTrailingTap;
  const SectionHeader(this.headerStr, {super.key, this.trailingStr, this.trailingColor, this.onTrailingTap});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.transparent, Colors.transparent, MColor.xFFFED58E, MColor.xFFFED58E],
              stops: [0.0, 0.6, 0.6, 1.0],
              begin: const FractionalOffset(0.0, 0.0),
              end: const FractionalOffset(0.0, 1.0),
            ),
          ),
          child: Text(headerStr, style: TextStyle(height: 1.4, fontSize: 16, color: MColor.xFF1B1C1A)),
        ),
        if (trailingStr?.isNotEmpty == true) ...{
          const Spacer(),
          GestureDetector(
            onTap: () {
              onTrailingTap?.call();
            },
            child: Text(trailingStr ?? '', style: TextStyle(height: 1.4, fontSize: 14, color: trailingColor)),
          )
        }
      ],
    );
  }
}
