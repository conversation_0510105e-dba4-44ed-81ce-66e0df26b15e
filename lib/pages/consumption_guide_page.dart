import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:intl/intl.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/mine_tab/user_repo.dart';
import 'package:qiazhun/modules/transaction_item_view.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/loading_view.dart';
import 'package:qiazhun/widgets/page_container_view.dart';
import 'package:qiazhun/widgets/round_image.dart';

class ConsumptionGuidePath extends StatefulWidget {
  const ConsumptionGuidePath({super.key});

  @override
  State<StatefulWidget> createState() => _ConsumptionGuideState();
}

class _ConsumptionGuideState extends State<ConsumptionGuidePath> {
  bool _isLoading = true;

  final List _dataList = [];
  final List<ConsumptionGuideModel> _usedList = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    _isLoading = true;
    Loading.show();

    try {
      var resp = await UserRepo.getConsumptionGuide();
      if (resp.code == 1) {
        _usedList.clear();
        var used = (resp.data['isRepayment'] as List<dynamic>).map((dynamic e) => ConsumptionGuideModel.fromJson(e)).toList();
        _usedList.addAll(used);
        _dataList.clear();
        var notUsed = (resp.data['noRepayment'] as List<dynamic>).map((dynamic e) => ConsumptionGuideModel.fromJson(e)).toList();
        _dataList.addAll(notUsed);
      } else {
        showToast(resp.msg ?? '数据获取失败');
      }
    } catch (e) {
      showToast('数据获取失败 $e');
    } finally {
      Loading.dismiss();
      _isLoading = false;
      setState(() {});
    }
    // _dataList.clear();
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
        title: '消费宝典',
        body: Builder(builder: (context) {
          if (_isLoading) {
            return LoadingView();
          }
          if (_dataList.isEmpty && _usedList.isEmpty) {
            return EmptyView();
          }
          return ListView.separated(
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                return Container(
                    color: MColor.xFFF5F5F5,
                    child: _itemView(index >= _dataList.length ? _usedList[index - _dataList.length] : _dataList[index], index, index >= _dataList.length));
              },
              separatorBuilder: (context, index) {
                if (_dataList.isEmpty || _usedList.isEmpty) {
                  return const SizedBox(
                    height: 18,
                  );
                } else if (index == _dataList.length - 1) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 18.0),
                    child: Image.asset(
                      'assets/images/bg_separator_repay_diary.png',
                      width: 343,
                      height: 31,
                    ),
                  );
                }
                return const SizedBox(
                  height: 18,
                );
              },
              itemCount: _dataList.length + _usedList.length);
        }));
  }

  Widget _itemView(ConsumptionGuideModel data, int index, bool isUsed) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 14),
      padding: EdgeInsets.all(18),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: Color(int.tryParse(data?.bankColour ?? '', radix: 16) ?? MColor.skin.value)),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              RoundImage(imageUrl: getImageUrl(data?.bankIcon), radius: 12, size: 24),
              const SizedBox(
                width: 10,
              ),
              Text(
                data?.bankName ?? '',
                style: TextStyle(height: 1.4, fontSize: 15, fontWeight: FontWeight.bold, color: MColor.xFFFFFFFF),
              ),
              const Spacer(),
              const SizedBox(
                height: 4,
              ),
              Text(
                '¥${data.availableAmount}',
                style: TextStyle(height: 1.4, fontSize: 20, color: MColor.xFFFFFFFF),
              ),
            ],
          ),
          const SizedBox(
            height: 14,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Text(
                    '免息期${data.freePeriod}天 账单日${data.billDate}日',
                    style: TextStyle(height: 1.4, fontSize: 12, fontWeight: FontWeight.bold, color: MColor.xFFFFFFFF),
                  ),
                  // const SizedBox(
                  //   width: 8,
                  // ),
                  // Builder(builder: (context) {
                  //   DateTime? dt = DateFormat('MM/dd').tryParse(data.repaymentDate ?? '');
                  //   return dt == null
                  //       ? const SizedBox()
                  //       : Text(
                  //           '${dt.month}月${dt.day}日 账单日',
                  //           style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFFFFFFFF),
                  //         );
                  // }),
                ],
              ),
              const Spacer(),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () async {
                  RouterHelper.router.pushNamed(Routes.transferPath, extra: {'fromAccountId': '${data.id}'});
                },
                child: Row(
                  children: [
                    Icon(
                      Icons.swap_horiz,
                      color: MColor.xFFFFFFFF,
                      size: 16,
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Text(
                      '转账',
                      style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFFFFFFFF),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                width: 14,
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () async {
                  Loading.show();
                  try {
                    var resp = await UserRepo.switchGuideStatus('${data.id}');
                    if (resp.code == 1) {
                      _loadData();
                      showToast('操作成功');
                    } else {
                      showToast(resp.msg ?? '操作失败');
                    }
                  } catch (e) {
                    showToast('操作失败 $e');
                  } finally {
                    Loading.dismiss();
                  }
                },
                child: Row(
                  children: [
                    Image.asset(
                      isUsed ? 'assets/images/ic_consumption_guide_used.png' : 'assets/images/ic_repayment_unpaid.png',
                      width: 20,
                      height: 20,
                      fit: BoxFit.scaleDown,
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Text(
                      isUsed ? '已使用' : '未使用',
                      style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFFFFFFFF),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                width: 14,
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  RouterHelper.router.pushNamed(Routes.editAccountPath, extra: {'accountId': data.id}).then((value) {
                    _loadData();
                  });
                },
                child: Row(
                  children: [
                    Image.asset(
                      'assets/images/ic_repayment_manage.png',
                      width: 18,
                      height: 18,
                      fit: BoxFit.scaleDown,
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Text(
                      '管理',
                      style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFFFFFFFF),
                    ),
                  ],
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}
