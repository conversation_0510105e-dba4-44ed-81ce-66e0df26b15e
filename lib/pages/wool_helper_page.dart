import 'package:flutter/material.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/mine_tab/user_repo.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/loading_view.dart';
import 'package:qiazhun/widgets/page_container_view.dart';

class WoolHelperPage extends StatefulWidget {
  const WoolHelperPage({super.key});

  @override
  State<StatefulWidget> createState() => _WoolHelperState();
}

class _WoolHelperState extends State<WoolHelperPage> {
  bool _isLoading = true;
  List<dynamic> _woolHelperData = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Call the wool helper API
      final response = await UserRepo.getWoolHelper();

      if (response.code == 1) {
        setState(() {
          _woolHelperData = response.data ?? [];
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
        showToast(response.msg ?? '获取数据失败');
      }
      // if (response['code'] == 1) {
      //   setState(() {
      //     _woolHelperData = response['data'] ?? [];
      //     _isLoading = false;
      //   });
      // } else {
      //   setState(() {
      //     _isLoading = false;
      //   });
      //   showToast(response['msg'] ?? '获取数据失败');
      // }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast('网络错误，请稍后重试');
      logger.e('Wool helper error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
        title: '羊毛助手',
        body: _isLoading
            ? const LoadingView()
            : _woolHelperData.isEmpty
                ? const EmptyView(
                    tips: '暂无羊毛信息',
                  )
                : RefreshIndicator(
                    onRefresh: _loadData,
                    child: _buildContent(),
                  ));
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeaderCard(),
          const SizedBox(height: 16),
          _buildWoolList(),
        ],
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: MColor.skin.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.savings_outlined,
                  color: MColor.skin,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '羊毛助手',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: MColor.xFF1B1C1A,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '发现优质羊毛活动，助您省钱理财',
                      style: TextStyle(
                        fontSize: 14,
                        color: MColor.xFF999999,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWoolList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '推荐活动',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: MColor.xFF1B1C1A,
          ),
        ),
        const SizedBox(height: 12),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _woolHelperData.length,
          itemBuilder: (context, index) {
            final item = _woolHelperData[index];
            return _buildWoolItem(item);
          },
        ),
      ],
    );
  }

  Widget _buildWoolItem(dynamic item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: MColor.skin.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  '推荐',
                  style: TextStyle(
                    fontSize: 12,
                    color: MColor.skin,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                '${item['date'] ?? ''}',
                style: TextStyle(
                  fontSize: 12,
                  color: MColor.xFF999999,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '${item['title'] ?? '暂无标题'}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: MColor.xFF1B1C1A,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${item['description'] ?? '暂无描述'}',
            style: TextStyle(
              fontSize: 14,
              color: MColor.xFF777777,
              height: 1.4,
            ),
          ),
          if (item['reward'] != null) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.monetization_on_outlined,
                  color: MColor.skin,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  '预期收益: ${item['reward']}',
                  style: TextStyle(
                    fontSize: 14,
                    color: MColor.skin,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
