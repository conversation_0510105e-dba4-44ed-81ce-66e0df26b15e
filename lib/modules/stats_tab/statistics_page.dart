// Statistics Page
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:qiazhun/models/finance.dart';
import 'package:qiazhun/models/transaction_model.dart';
import 'package:qiazhun/notifiers/finance_data_notifier.dart';

class StatisticsPage extends StatelessWidget {
  const StatisticsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // final financeData = ref.watch(financeDataProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('账本列表'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {},
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            TimeRangeSelector(),
            // YearlyChart(financeData: financeData),
            // IncomeDistributionChart(financeData: financeData),
            // ExpenseDistributionChart(financeData: financeData),
          ],
        ),
      ),
    );
  }
}

// Widget components for StatisticsPage
class TimeRangeSelector extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        ElevatedButton(
          onPressed: () {},
          child: const Text('日'),
          style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
        ),
        ElevatedButton(
          onPressed: () {},
          child: const Text('周'),
          style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
        ),
        ElevatedButton(
          onPressed: () {},
          child: const Text('月'),
          style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
        ),
        ElevatedButton(
          onPressed: () {},
          child: const Text('年'),
          style: ElevatedButton.styleFrom(backgroundColor: Colors.teal),
        ),
        ElevatedButton(
          onPressed: () {},
          child: const Text('自定义'),
          style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
        ),
      ],
    );
  }
}

class YearlyChart extends StatelessWidget {
  final FinanceData financeData;

  const YearlyChart({Key? key, required this.financeData}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Implement yearly chart here
    return Container(
      height: 200,
      color: Colors.white,
      margin: const EdgeInsets.all(16),
      child: const Center(child: Text('Yearly Chart Placeholder')),
    );
  }
}

class IncomeDistributionChart extends StatelessWidget {
  final FinanceData financeData;

  const IncomeDistributionChart({Key? key, required this.financeData}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('收入来源', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  flex: 85,
                  child: Container(height: 20, color: Colors.teal),
                ),
                const SizedBox(width: 4),
                Expanded(
                  flex: 15,
                  child: Container(height: 20, color: Colors.orange),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildIncomeItem('职业收入', 0.85, Colors.teal),
            _buildIncomeItem('其他收入', 0.15, Colors.orange),
            // Add more income types as needed
          ],
        ),
      ),
    );
  }

  Widget _buildIncomeItem(String label, double percentage, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            color: color,
            margin: const EdgeInsets.only(right: 8),
          ),
          Expanded(
            child: Text(label),
          ),
          Text('${(percentage * 100).toStringAsFixed(1)}%'),
          const SizedBox(width: 8),
          Text('¥${(financeData.income * percentage).toStringAsFixed(2)}'),
        ],
      ),
    );
  }
}

class ExpenseDistributionChart extends StatelessWidget {
  final FinanceData financeData;

  const ExpenseDistributionChart({Key? key, required this.financeData}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('支出分布', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            Container(
              height: 200,
              child: CustomPaint(
                size: Size.infinite,
                painter: PieChartPainter(),
              ),
            ),
            const SizedBox(height: 16),
            _buildExpenseItem('食品酒水', 0.20, Colors.red),
            _buildExpenseItem('交通出行', 0.24, Colors.blue),
            _buildExpenseItem('休闲娱乐', 0.16, Colors.green),
            _buildExpenseItem('购物消费', 0.19, Colors.orange),
            _buildExpenseItem('其他', 0.21, Colors.purple),
          ],
        ),
      ),
    );
  }

  Widget _buildExpenseItem(String label, double percentage, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            color: color,
            margin: const EdgeInsets.only(right: 8),
          ),
          Expanded(
            child: Text(label),
          ),
          Text('${(percentage * 100).toStringAsFixed(1)}%'),
          const SizedBox(width: 8),
          Text('¥${(financeData.expenses * percentage).toStringAsFixed(2)}'),
        ],
      ),
    );
  }
}

class PieChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;

    final paint = Paint()..style = PaintingStyle.fill;

    final List<MapEntry<Color, double>> data = [
      MapEntry(Colors.red, 0.20),
      MapEntry(Colors.blue, 0.24),
      MapEntry(Colors.green, 0.16),
      MapEntry(Colors.orange, 0.19),
      MapEntry(Colors.purple, 0.21),
    ];

    double startAngle = 0;
    for (final entry in data) {
      final sweepAngle = entry.value * 2 * 3.14159;
      paint.color = entry.key;
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );
      startAngle += sweepAngle;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

// Additional widgets and improvements

class AddTransactionButton extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FloatingActionButton(
      onPressed: () {
        showModalBottomSheet(
          context: context,
          builder: (context) => AddTransactionForm(),
        );
      },
      child: Icon(Icons.add),
    );
  }
}

class AddTransactionForm extends ConsumerStatefulWidget {
  @override
  _AddTransactionFormState createState() => _AddTransactionFormState();
}

class _AddTransactionFormState extends ConsumerState<AddTransactionForm> {
  final _formKey = GlobalKey<FormState>();
  String _title = '';
  double _amount = 0;
  String _category = 'expense';

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      final transaction = Transaction(
        title: _title,
        amount: _category == 'income' ? _amount : -_amount,
        date: DateTime.now(),
        category: _category,
      );
      ref.read(financeDataProvider.notifier).addTransaction(transaction);
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            TextFormField(
              decoration: InputDecoration(labelText: 'Title'),
              validator: (value) => value!.isEmpty ? 'Please enter a title' : null,
              onSaved: (value) => _title = value!,
            ),
            TextFormField(
              decoration: InputDecoration(labelText: 'Amount'),
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              validator: (value) => value!.isEmpty ? 'Please enter an amount' : null,
              onSaved: (value) => _amount = double.parse(value!),
            ),
            DropdownButtonFormField<String>(
              value: _category,
              items: [
                DropdownMenuItem(child: Text('Expense'), value: 'expense'),
                DropdownMenuItem(child: Text('Income'), value: 'income'),
              ],
              onChanged: (value) => setState(() => _category = value!),
            ),
            ElevatedButton(
              onPressed: _submitForm,
              child: Text('Add Transaction'),
            ),
          ],
        ),
      ),
    );
  }
}
