import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/providers/bookkeeping_list_provider.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/loading_view.dart';
import 'package:qiazhun/widgets/round_image.dart';

class ChooseLedgarPanel extends StatefulWidget {
  final List<String>? selected;
  final bool? multiSelect;
  final bool? showCreateNew;
  const ChooseLedgarPanel(this.selected, this.multiSelect, this.showCreateNew, {super.key});

  @override
  State<ChooseLedgarPanel> createState() => _ChooseLedgarState();
}

class _ChooseLedgarState extends State<ChooseLedgarPanel> {
  List<String> _tmpSelected = [];
  bool _isLoading = true;

  @override
  void initState() {
    _tmpSelected.addAll(widget.selected ?? []);

    BookkeepingStore.to.getBookkeepingList().then((_) {
      _isLoading = false;
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        dividerTheme: DividerThemeData(
          color: Colors.transparent,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.vertical(top: Radius.circular(28)),
        child: Scaffold(
          backgroundColor: MColor.xFFF5F5F5,
          appBar: AppBar(
            backgroundColor: MColor.xFFF5F5F5,
            title: Text('选择账本', style: TextStyle(height: 1.4, fontSize: 16, color: MColor.xFF1B1C1A)),
            scrolledUnderElevation: 0,
            centerTitle: true,
            automaticallyImplyLeading: false,
            actions: [
              IconButton(
                  onPressed: () {
                    RouterHelper.router.pop();
                  },
                  icon: Icon(Icons.close, size: 22, color: MColor.xFF999999))
            ],
          ),
          persistentFooterButtons: widget.multiSelect == true
              ? [
                  GestureDetector(
                    onTap: () {
                      RouterHelper.router.pop({'selected': _tmpSelected});
                    },
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(25),
                          gradient: LinearGradient(
                              begin: const FractionalOffset(0.0, 0.0), end: const FractionalOffset(0.0, 1.0), colors: [Color(0xFF68C2BF), Color(0xFF4C9B93)])),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '确认',
                            style: TextStyle(color: MColor.xFFFFFFFF, fontWeight: FontWeight.w500, fontSize: 16, height: 1),
                          ),
                        ],
                      ),
                    ),
                  )
                ]
              : null,
          body: SafeArea(child: GetBuilder<BookkeepingStore>(builder: (bkStore) {
            if (_isLoading) {
              return const LoadingView();
            }
            if (bkStore.bookkeepingList.isEmpty) {
              return const EmptyView();
            }
            return ListView.separated(
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  if (index < bkStore.bookkeepingList.length) {
                    return _itemView(bkStore.bookkeepingList[index]);
                  } else {
                    return _createNewView;
                  }
                },
                separatorBuilder: (context, index) {
                  return SizedBox(
                    height: 14,
                  );
                },
                itemCount: bkStore.bookkeepingList.length + (1));
          })),
        ),
      ),
    );
  }

  Widget get _createNewView {
    return GestureDetector(
      onTap: () {
        RouterHelper.router.pushNamed(Routes.addLedgerPath).then((_) {
          BookkeepingStore.to.getBookkeepingList();
        });
      },
      child: Container(
          height: 69,
          margin: EdgeInsets.symmetric(horizontal: 14),
          padding: EdgeInsets.all(14),
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.add, color: MColor.skin),
              Text(
                '新增账本',
                style: TextStyle(fontSize: 14, height: 1.4, color: MColor.skin),
              ),
            ],
          )),
    );
  }

  Widget _itemView(BookkeepingInfo info) {
    bool isSelected = _tmpSelected.firstWhereOrNull(
          (element) => info.bookkeepingNumber == element,
        ) !=
        null;
    return GestureDetector(
      onTap: () {
        if (widget.multiSelect == true) {
          if (isSelected) {
            _tmpSelected.removeWhere((element) => info.bookkeepingNumber == element);
          } else {
            _tmpSelected.add(info.bookkeepingNumber ?? '');
          }
        } else {
          _tmpSelected.clear();
          _tmpSelected.add(info.bookkeepingNumber ?? '');
          RouterHelper.router.pop({'selected': _tmpSelected});
        }
        setState(() {});
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 14),
        padding: EdgeInsets.all(14),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
        child: Row(
          children: [
            RoundImage(imageUrl: info.bookkeepingIcon ?? '', radius: 18, size: 36),
            const SizedBox(
              width: 12,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    info.accountBookName ?? '',
                    style: TextStyle(height: 1.4, fontSize: 14, color: MColor.xFF000000),
                  ),
                  Text(
                    '共${info.billCount ?? 0}条账单',
                    style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF999999),
                  )
                ],
              ),
            ),
            if (isSelected) Image.asset('assets/images/ic_checked.png', width: 24, height: 24, fit: BoxFit.fill),
          ],
        ),
      ),
    );
  }
}
