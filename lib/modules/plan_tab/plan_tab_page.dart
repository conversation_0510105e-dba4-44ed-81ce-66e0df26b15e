import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/plan_tab/plan_model.dart';
import 'package:qiazhun/repo/plan_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/loading_view.dart';
import 'package:qiazhun/widgets/round_image.dart';

class PlanTabPage extends StatefulWidget {
  const PlanTabPage({super.key});

  @override
  State<StatefulWidget> createState() => _PlanTabState();
}

class _PlanTabState extends State<PlanTabPage> {
  final List<PlanInfo> _planList = [];
  bool _isLoading = true;

  Future<void> _sortPlans(String sortedIds) async {
    debugPrint('Plan sorted: $sortedIds');
    // 空方法，用于后续实现排序后的逻辑
    try {
      var resp = await PlanRepo.sortPlan(sortedIds);
      if (resp.code == 1) {
        setState(() {
          _planList.clear();
          if (resp.data?.isNotEmpty == true) {
            _planList.addAll(resp.data!);
          }
        });
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void initState() {
    _getData();
    super.initState();
  }

  Future<void> _getData() async {
    try {
      var resp = await PlanRepo.getPlanList();
      if (resp.code == 1) {
        setState(() {
          _planList.clear();
          if (resp.data?.isNotEmpty == true) {
            _planList.addAll(resp.data!);
          }
        });
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      floatingActionButton: IconButton(
        padding: EdgeInsets.zero,
        constraints: const BoxConstraints(),
        style: const ButtonStyle(
          tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
        ),
        icon: Image.asset(
          'assets/images/ic_add.png',
          height: 50,
          width: 50,
        ),
        onPressed: () {
          RouterHelper.router.pushNamed(Routes.planEditPath, pathParameters: {'planId': '0'}).then((_) {
            _getData();
          });
        },
      ),
      body: SafeArea(
        // maintainBottomViewPadding: true,
        top: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      centerTitle: true,
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        '计划清单',
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: RefreshIndicator(
                    triggerMode: RefreshIndicatorTriggerMode.anywhere,
                    onRefresh: () async {
                      await _getData();
                    },
                    child: Builder(builder: (context) {
                      if (_isLoading) {
                        return LoadingView();
                      }
                      if (_planList.isEmpty) {
                        return EmptyView(
                          tips: '梦还是要有的，去创建一个吧！',
                        );
                      }
                      return ReorderableListView.builder(
                          padding: EdgeInsets.symmetric(vertical: 14),
                          itemBuilder: (context, index) {
                            return Column(
                              key: ValueKey(_planList[index].id),
                              children: [
                                const SizedBox(
                                  height: 7,
                                ),
                                _planItemView(_planList[index]),
                                const SizedBox(
                                  height: 7,
                                ),
                              ],
                            );
                          },
                          itemCount: _planList.length,
                          onReorder: (oldIndex, newIndex) {
                            setState(() {
                              if (oldIndex < newIndex) {
                                newIndex -= 1;
                              }
                              final List<String> planIds = _planList.map((plan) => '${plan.id}').toList();
                              final item = planIds.removeAt(oldIndex);
                              planIds.insert(newIndex, item);

                              // 更新列表顺序
                              final movedItem = _planList.removeAt(oldIndex);
                              _planList.insert(newIndex, movedItem);

                              // 调用onSorted方法，传入排序后的planId数组（逗号分隔）
                              _sortPlans(planIds.join(','));
                            });
                          },
                          proxyDecorator: (child, index, animation) {
                            return Material(
                              elevation: 6,
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(28),
                              child: child,
                            );
                          });
                    }),
                  ))
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _deletePlan(PlanInfo data) async {
    Loading.show();
    try {
      var resp = await PlanRepo.delPlan(data.id ?? '');
      if (resp.code == 1) {
        _getData();
      }
    } catch (e) {
    } finally {
      Loading.dismiss();
    }
  }

  Widget _planItemView(PlanInfo data) {
    double barWidth = MediaQuery.of(context).size.width - 28 - 28;
    return Slidable(
      key: ValueKey(data.id),
      // The end action pane is the one at the right or the bottom side.
      endActionPane: ActionPane(
        motion: ScrollMotion(),
        children: [
          SlidableAction(
            onPressed: (context) {
              showCustomDialog(
                '确认删除',
                content: '删除后不可恢复，请确认是否删除',
                cancel: true,
                onConfirm: () async {
                  await _deletePlan(data);
                },
                onCancel: () {},
              );
            },
            backgroundColor: MColor.xFFFF7858,
            foregroundColor: MColor.xFFFFFFFF,
            icon: Icons.delete,
            label: '删除',
          ),
          SlidableAction(
            onPressed: (context) async {
              RouterHelper.router.pushNamed(Routes.planEditPath, pathParameters: {'planId': '${data.id}'}).then((value) {
                _getData();
              });
            },
            backgroundColor: MColor.xFFFFBE4A,
            foregroundColor: MColor.xFFFFFFFF,
            icon: Icons.edit,
            label: '编辑',
          )
        ],
      ),
      child: GestureDetector(
        onTap: () {
          RouterHelper.router.pushNamed(Routes.planDetailPath, pathParameters: {'planId': '${data.id}'}).then((_) {
            _getData();
          });
        },
        child: Container(
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(28), border: Border.all(color: MColor.skin, width: 1), color: MColor.xFFFFFFFF),
          padding: EdgeInsets.all(14),
          margin: EdgeInsets.symmetric(horizontal: 14),
          child: Column(
            children: [
              Builder(builder: (context) {
                return Container(
                  // color: Colors.yellow,
                  child: Row(
                    children: [
                      if (data.icon?.isNotEmpty == true) RoundImage(imageUrl: data.icon!, size: 32, radius: 16),
                      const SizedBox(
                        width: 12,
                      ),
                      Expanded(
                        child: Text(
                          data.name,
                          style: TextStyle(fontSize: 14, height: 1.4, color: MColor.xFF1B1C1A),
                        ),
                      ),
                      // IconButton(
                      //     padding: EdgeInsets.zero,
                      //     constraints: const BoxConstraints(),
                      //     style: const ButtonStyle(
                      //       tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                      //     ),
                      //     onPressed: () {},
                      //     icon: Image.asset(
                      //       'assets/images/ic_note.png',
                      //       width: 24,
                      //       height: 24,
                      //       fit: BoxFit.fill,
                      //     )),
                      // const SizedBox(
                      //   width: 14,
                      // ),
                      IconButton(
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                          style: const ButtonStyle(
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                          ),
                          onPressed: () {
                            RouterHelper.router.pushNamed(Routes.addSavingDialogPath, pathParameters: {'planId': '${data.id}'}).then((_) {
                              _getData();
                            });
                          },
                          icon: Image.asset(
                            'assets/images/ic_add_2.png',
                            width: 24,
                            height: 24,
                            fit: BoxFit.fill,
                          ))
                    ],
                  ),
                );
              }),
              const SizedBox(
                height: 14,
              ),
              SizedBox(
                  height: 19,
                  child: Stack(
                    children: [
                      Container(
                        width: barWidth,
                        decoration: BoxDecoration(
                            gradient: LinearGradient(
                              // begin: Alignment.topRight,
                              // end: Alignment.bottomLeft,
                              stops: [0.0, ((double.tryParse(data!.proportion ?? '') ?? 0) / 100), ((double.tryParse(data!.proportion ?? '') ?? 0) / 100), 1.0],
                              begin: const FractionalOffset(0.0, 0.0),
                              end: const FractionalOffset(1.0, 0.0),
                              colors: [
                                MColor.skin,
                                MColor.skin,
                                MColor.skin.withOpacity(0.2),
                                MColor.skin.withOpacity(0.2),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(10)),
                      ),
                    ],
                  )),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '第${data.nowDay}天，预计${data.residueDay}天后完成',
                    style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF999999),
                  ),
                  const Spacer(),
                  Text(
                    '${data.accumulatedAmount}/${data.requiredAmount}',
                    style: TextStyle(fontSize: 12, height: 1.4, color: MColor.skin),
                  ),
                  const SizedBox(
                    width: 14,
                  ),
                  Text(
                    '${data.proportion}%',
                    style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF999999),
                  )
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
