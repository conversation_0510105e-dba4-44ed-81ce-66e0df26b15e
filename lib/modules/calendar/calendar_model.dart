import 'package:json_annotation/json_annotation.dart';
import 'package:qiazhun/models/home_detail_model.dart';

part 'calendar_model.g.dart';

@JsonSerializable()
class CalendarResp extends Object {
  @Json<PERSON>ey(name: 'flowingWaterLog')
  List<TransactionItem> flowingWaterLog;

  @Json<PERSON>ey(name: 'calendarLog')
  List<CalendarLog> monthFlowingWaterLog;
  @Json<PERSON>ey(name: 'total')
  Map<String, String> total;

  CalendarResp(
    this.flowingWaterLog,
    this.monthFlowingWaterLog,
    this.total,
  );

  factory CalendarResp.fromJson(Map<String, dynamic> srcJson) => _$CalendarRespFromJson(srcJson);
}

@JsonSerializable()
class CalendarLog extends Object {
  @Json<PERSON>ey(name: 'date')
  String date;

  @JsonKey(name: 'week')
  String week;

  @JsonKey(name: 'weekChinese')
  String weekChinese;

  @JsonKey(name: 'item')
  List<dynamic> item;

  @J<PERSON><PERSON>ey(name: 'totalIncome')
  int totalIncome;

  @Json<PERSON>ey(name: 'totalPayment')
  int totalPayment;

  CalendarLog(
    this.date,
    this.week,
    this.weekChinese,
    this.item,
    this.totalIncome,
    this.totalPayment,
  );

  factory CalendarLog.fromJson(Map<String, dynamic> srcJson) => _$CalendarLogFromJson(srcJson);

  Map<String, dynamic> toJson() => _$CalendarLogToJson(this);
}
