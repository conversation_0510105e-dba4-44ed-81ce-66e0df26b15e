import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/user_model.dart';
import 'package:qiazhun/modules/auth/third_login_store.dart';
import 'package:qiazhun/modules/mine_tab/user_repo.dart';
import 'package:qiazhun/modules/mine_tab/user_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/round_image.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class AppSettingPage extends StatefulWidget {
  const AppSettingPage({super.key});

  @override
  State<StatefulWidget> createState() => _AppSettingState();
}

class _AppSettingState extends State<AppSettingPage> {
  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        dividerTheme: DividerThemeData(
          color: Colors.transparent,
        ),
      ),
      child: Scaffold(
        extendBody: true,
        resizeToAvoidBottomInset: false,
        persistentFooterButtons: [_logoutButton],
        body: SafeArea(
          // maintainBottomViewPadding: true,
          top: false,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        '设置',
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                // top: 0,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 14.0),
                  child: GetBuilder<UserStore>(builder: (_) {
                    return Column(
                      children: [
                        _sectionHeader,
                        const SizedBox(
                          height: 14,
                        ),
                        _section0View,
                        const SizedBox(
                          height: 14,
                        ),
                        _section1View,
                        const SizedBox(
                          height: 14,
                        ),
                        _section2View
                      ],
                    );
                  }),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget get _sectionHeader {
    var userInfoResp = UserStore.to.userInfoResp;
    return Container(
      padding: EdgeInsets.fromLTRB(14, 14, 14, 0),
      child: Column(
        children: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              showUploadDialog(context, (filePath) async {
                Loading.show();
                try {
                  var resp = await UserRepo.updateUserInfo(avatar: filePath);
                  if (resp.code == 1) {
                    UserStore.to.getUserInfo();
                  } else {
                    showToast(resp.msg ?? '更新头像失败');
                  }
                } catch (e) {
                  showToast('更新头像失败 $e');
                } finally {
                  Loading.dismiss();
                }
              }, (errorMsg) {
                showToast(errorMsg ?? '上传失败');
              });
            },
            child: Stack(
              children: [
                RoundImage(imageUrl: getImageUrl(userInfoResp?.userInfo?.avatar ?? ''), radius: 50, size: 100),
                Positioned(
                  bottom: 0,
                  right: 8,
                  child: Image.asset(
                    'assets/images/ic_camera.png',
                    width: 24,
                    height: 24,
                  ),
                )
              ],
            ),
          ),
          const SizedBox(
            height: 15,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                userInfoResp?.userInfo?.userName ?? '',
                style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16, fontWeight: FontWeight.w500),
              ),
              const SizedBox(
                width: 10,
              ),
              GestureDetector(
                onTap: () {
                  TextEditingController controller = TextEditingController(text: userInfoResp?.userInfo?.userName ?? '');
                  Widget widget = TextField(
                    controller: controller,
                    keyboardType: TextInputType.text,
                    decoration: InputDecoration(
                      hintText: '请输入',
                      hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
                      filled: true,
                      fillColor: Colors.white,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 15),
                      isDense: true,
                    ),
                  );
                  RouterHelper.router.pushNamed(Routes.customPopupPath, extra: {
                    'title': '设置昵称',
                    'widget': widget,
                    'onConfirm': () async {
                      String newVal = controller.text;
                      if (newVal.isNotEmpty && newVal != userInfoResp?.userInfo?.userName) {
                        Loading.show();
                        try {
                          var resp = await UserRepo.updateUserInfo(userName: newVal);
                          if (resp.code == 1) {
                            UserStore.to.getUserInfo();
                          } else {
                            showToast(resp.msg ?? '更新昵称失败');
                          }
                        } catch (e) {
                          showToast('更新昵称失败 $e');
                        } finally {
                          Loading.dismiss();
                        }
                        setState(() {});
                      }
                    }
                  }).then((_) {
                    controller.dispose();
                  });
                },
                child: Image.asset(
                  'assets/images/ic_feedback.png',
                  width: 20,
                  height: 20,
                ),
              )
            ],
          )
        ],
      ),
    );
  }

  Widget get _section0View {
    var userInfoResp = UserStore.to.userInfoResp;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14),
      decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(20)),
      child: Column(
        children: [
          ListTile(
            onTap: () {
              TextEditingController controller = TextEditingController(text: userInfoResp?.userInfo?.bio ?? '');
              Widget widget = TextField(
                controller: controller,
                keyboardType: TextInputType.text,
                decoration: InputDecoration(
                  hintText: '请输入',
                  hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
                  filled: true,
                  fillColor: Colors.white,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 15),
                  isDense: true,
                ),
              );
              RouterHelper.router.pushNamed(Routes.customPopupPath, extra: {
                'title': '设置签名',
                'widget': widget,
                'onConfirm': () async {
                  String newVal = controller.text;
                  if (newVal.isNotEmpty && newVal != userInfoResp?.userInfo?.bio) {
                    Loading.show();
                    try {
                      var resp = await UserRepo.updateUserInfo(bio: newVal);
                      if (resp.code == 1) {
                        UserStore.to.getUserInfo();
                      } else {
                        showToast(resp.msg ?? '更新签名失败');
                      }
                    } catch (e) {
                      showToast('更新签名失败 $e');
                    } finally {
                      Loading.dismiss();
                    }
                    setState(() {});
                  }
                }
              }).then((_) {
                controller.dispose();
              });
            },
            minLeadingWidth: 14,
            contentPadding: EdgeInsets.zero,
            leading: Text(
              '签名',
              style: TextStyle(height: 1.4, fontSize: 15, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A),
            ),
            trailing:
                Text(userInfoResp?.userInfo?.bio ?? '设置签名', style: TextStyle(height: 1.4, fontSize: 12, fontWeight: FontWeight.w500, color: MColor.xFF999999)),
          ),
        ],
      ),
    );
  }

  Widget get _section1View {
    var userInfoResp = UserStore.to.userInfoResp;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14),
      decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(20)),
      child: Column(
        children: [
          ListTile(
            onTap: () {
              showCustomDialog(
                '确认是否要重新绑定手机号？',
                onConfirm: () async {
                  RouterHelper.router.pushNamed(Routes.verificationPath, extra: {'bindType': 2}).then((_) {
                    UserStore.to.getUserInfo();
                  });
                },
                cancel: true,
                onCancel: () {},
              );
            },
            minLeadingWidth: 14,
            contentPadding: EdgeInsets.zero,
            leading: Text('手机号', style: TextStyle(height: 1.4, fontSize: 15, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A)),
            trailing:
                Text(userInfoResp?.userInfo?.mobile ?? '', style: TextStyle(height: 1.4, fontSize: 12, fontWeight: FontWeight.w500, color: MColor.xFF999999)),
          ),
          ListTile(
            onTap: () {
              if (userInfoResp?.userInfo?.bindWxStatus == 1) {
                showCustomDialog(
                  '确认是否要重新绑定微信？',
                  onConfirm: () async {
                    ThirdLoginStore.to.rebindWechat();
                  },
                  cancel: true,
                  onCancel: () {},
                );
              } else {
                ThirdLoginStore.to.rebindWechat();
              }
            },
            minLeadingWidth: 14,
            contentPadding: EdgeInsets.zero,
            leading: Text('微信', style: TextStyle(height: 1.4, fontSize: 15, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A)),
            trailing: Text(userInfoResp?.userInfo?.bindWxStatus == 1 ? '已绑定' : '未绑定',
                style: TextStyle(height: 1.4, fontSize: 12, fontWeight: FontWeight.w500, color: MColor.xFF999999)),
          ),
          ListTile(
            onTap: () async {
              if (userInfoResp?.userInfo?.bindAppleStatus == 1) {
                showCustomDialog(
                  '确认是否要解绑AppleId？',
                  onConfirm: () async {
                    Loading.show();
                    try {
                      var resp = await UserRepo.unbindApple();
                      if (resp.code == 1) {
                        showToast('解绑成功');
                        UserStore.to.getUserInfo();
                      } else {
                        showToast(resp.msg ?? '解绑失败');
                      }
                    } catch (e) {
                      showToast('解绑失败 ${e.toString}');
                    } finally {
                      Loading.dismiss();
                    }
                  },
                  cancel: true,
                  onCancel: () {},
                );
              } else {
                final credential = await SignInWithApple.getAppleIDCredential(
                  scopes: [
                    AppleIDAuthorizationScopes.email,
                    AppleIDAuthorizationScopes.fullName,
                  ],
                );

                logger.i(credential);
                Loading.show();
                try {
                  var resp = await UserRepo.rebindApple(credential.identityToken!);
                  if (resp.code == 1) {
                    showToast('绑定成功');
                    UserStore.to.getUserInfo();
                  } else {
                    showToast(resp.msg ?? '绑定失败');
                  }
                } catch (e) {
                  showToast('解绑失败 ${e.toString}');
                } finally {
                  Loading.dismiss();
                }
              }
            },
            minLeadingWidth: 14,
            contentPadding: EdgeInsets.zero,
            leading: Text('AppleId', style: TextStyle(height: 1.4, fontSize: 15, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A)),
            trailing: Text(userInfoResp?.userInfo?.bindAppleStatus == 1 ? '已绑定' : '未绑定',
                style: TextStyle(height: 1.4, fontSize: 12, fontWeight: FontWeight.w500, color: MColor.xFF999999)),
          ),
        ],
      ),
    );
  }

  Widget get _section2View {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14),
      decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(20)),
      child: Column(
        children: [
          ListTile(
            onTap: () {
              showCustomDialog(
                '确认是否要注销账号：您一旦选择确认注销，您的账号、数据、权益等将清空，且无法恢复。',
                onConfirm: () async {
                  Loading.show();
                  try {
                    var resp = await UserRepo.deleteAccount();
                    if (resp.code == 1) {
                      UserStore.to.logout();
                      RouterHelper.router.goNamed(Routes.loginPath);
                    } else {
                      showToast(resp.msg ?? '注销失败');
                    }
                  } catch (e) {
                    showToast('注销失败 $e');
                  } finally {
                    Loading.dismiss();
                  }
                },
                cancel: true,
                onCancel: () {},
              );
            },
            minLeadingWidth: 14,
            contentPadding: EdgeInsets.zero,
            leading: Text('账号注销', style: TextStyle(height: 1.4, fontSize: 15, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A)),
            trailing: Icon(
              Icons.arrow_forward_ios,
              size: 12,
              color: MColor.xFF999999,
            ),
          )
        ],
      ),
    );
  }

  Widget get _logoutButton {
    return GestureDetector(
      onTap: () {
        // _submit();
        showCustomDialog(
          '确认是否要退出',
          onConfirm: () {
            UserStore.to.logout();
            RouterHelper.router.goNamed(Routes.loginPath);
          },
          cancel: true,
          onCancel: () {},
        );
      },
      child: Container(
        height: 50,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            gradient:
                LinearGradient(begin: const FractionalOffset(0.0, 0.0), end: const FractionalOffset(0.0, 1.0), colors: [Color(0xFF68C2BF), Color(0xFF4C9B93)])),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '退出登录',
              style: TextStyle(color: MColor.xFFFFFFFF, fontWeight: FontWeight.w500, fontSize: 16, height: 1),
            ),
          ],
        ),
      ),
    );
  }
}
