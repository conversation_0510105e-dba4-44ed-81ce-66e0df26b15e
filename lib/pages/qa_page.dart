import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/mine_tab/user_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';

class QaPage extends StatefulWidget {
  const QaPage({super.key});

  @override
  State<StatefulWidget> createState() => _QaState();
}

class _QaState extends State<QaPage> {
  final List<dynamic> _questionList = [];

  @override
  void initState() {
    super.initState();
    _getData();
  }

  Future<void> _getData() async {
    Loading.show();
    try {
      var resp = await UserRepo.getQuestionList();
      if (resp.code == 1) {
        _questionList.clear();
        _questionList.addAll(resp.data ?? []);
        setState(() {});
      } else {
        showToast(resp.msg ?? '获取问题列表失败');
      }
    } catch (e) {
      showToast('获取问题列表失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: SafeArea(
        top: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        '帮助中心',
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: Builder(builder: (context) {
                    if (_questionList.isEmpty) {
                      return EmptyView();
                    }
                    return Column(
                      children: [
                        Container(
                          margin: EdgeInsets.all(14),
                          padding: EdgeInsets.symmetric(horizontal: 14),
                          decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(20)),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: List.generate(_questionList.length, (index) {
                              return _tileView(_questionList[index]);
                            }),
                          ),
                        ),
                      ],
                    );
                  }))
            ],
          ),
        ),
      ),
    );
  }

  Widget _tileView(dynamic tile) {
    return ListTile(
      onTap: () {
        RouterHelper.router.pushNamed(Routes.webPath, extra: {'url': tile['url'], 'title': tile['title']});
      },
      contentPadding: EdgeInsets.symmetric(horizontal: 0, vertical: 4),
      dense: true,
      // leading: Image.asset(
      //   tile['icon'],
      //   width: 20,
      //   height: 20,
      //   fit: BoxFit.scaleDown,
      // ),
      title: Text(
        tile['title'],
        style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A, fontWeight: FontWeight.w500),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 14,
        color: MColor.xFF999999,
      ),
    );
  }
}
