import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/toast_utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/repo/plan_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class AddSavingDialog extends StatefulWidget {
  final String planId;

  const AddSavingDialog(this.planId, {super.key});

  @override
  State<StatefulWidget> createState() => _AddSavingState();
}

class _AddSavingState extends State<AddSavingDialog> {
  final TextEditingController _textEditingController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(14),
      decoration: BoxDecoration(
          color: MColor.xFFFFFFFF,
          borderRadius: BorderRadius.circular(28),
          image: DecorationImage(
            image: AssetImage(
              "assets/images/mine_bg.png",
            ),
            fit: BoxFit.cover,
          )),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '新增一笔储蓄',
            style: TextStyle(color: MColor.xFF1B1C1A, fontSize: 16, height: 1.4),
          ),
          const SizedBox(
            height: 24,
          ),
          TextField(
            controller: _textEditingController,
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
            decoration: InputDecoration(
              hintText: '请输入储蓄金额',
              hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 15),
              isDense: true,
            ),
          ),
          const SizedBox(
            height: 24,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              GestureDetector(
                onTap: () {
                  RouterHelper.router.pop();
                },
                child: Container(
                    height: 36,
                    padding: EdgeInsets.symmetric(horizontal: 18),
                    decoration: BoxDecoration(color: MColor.xFFE8E8E8, borderRadius: BorderRadius.circular(10)),
                    child: Center(
                      child: Text(
                        '取消',
                        style: TextStyle(fontSize: 14, color: MColor.xFF999999),
                      ),
                    )),
              ),
              const SizedBox(
                width: 14,
              ),
              GestureDetector(
                onTap: () {
                  _addSaving(_textEditingController.text);
                },
                child: Container(
                    height: 36,
                    padding: EdgeInsets.symmetric(horizontal: 18),
                    decoration: BoxDecoration(color: MColor.skin, borderRadius: BorderRadius.circular(10)),
                    child: Center(
                      child: Text(
                        '保存',
                        style: TextStyle(fontSize: 14, color: MColor.xFFFFFFFF),
                      ),
                    )),
              )
            ],
          )
        ],
      ),
    );
  }

  Future<void> _addSaving(String price) async {
    if (price.isEmpty) {
      showToast('请填写有效的金额');
      return;
    }
    Loading.show();
    try {
      var resp = await PlanRepo.manualSave(widget.planId, price);
      if (resp.code == 1) {
        showToast('操作成功');
        RouterHelper.router.pop();
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      Loading.dismiss();
    }
  }
}
