import 'dart:convert';

import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/modules/plan_tab/plan_model.dart';

class PlanRepo {
  static Future<BaseModel<List<PlanInfo>>> getPlanList() async {
    var resp = await HttpUtil().post('api/plan/planlist');
    return BaseModel.fromJson(resp, (json) => (json as List<dynamic>).map((dynamic e) => PlanInfo.fromJson(e)).toList());
  }

  static Future<BaseModel<PlanInfoResp>> getPlanDetail(String planId) async {
    var resp = await HttpUtil().post('api/plan/planDetail', data: {'planId': planId});
    return BaseModel.fromJson(resp, (json) => PlanInfoResp.fromJson(json));
  }

  static Future<BaseModel<dynamic>> manualSave(String planId, String price) async {
    var resp = await HttpUtil().post('api/plan/manualSaveTask', data: {'planId': planId, 'price': price});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> sortPlan(String sortedId) async {
    var resp = await HttpUtil().post('api/plan/updatePlanSort', data: {'sortedId': sortedId});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> addPlan(
      {String? planId,
      required String name,
      required String planIcon,
      String? background,
      required String alreadyAmount,
      required String requiredAmount,
      String? remark,
      String? bookkeepingNumbers,
      PlanStoreRule? rule,
      required String status,
      required String storeType,
      required String startDate,
      required String endDate}) async {
    var resp = await HttpUtil().post('api/plan/addPlan', data: {
      'planId': planId,
      'name': name,
      'planBackgroundImage': background,
      'planIcon': planIcon,
      'planRequiredAmount': requiredAmount,
      'planAccumulatedAmount': alreadyAmount,
      'planStartDate': startDate,
      'planExpectedAchieveDate': endDate,
      'planStorageMethod': storeType,
      'bookkeepingNumbers': bookkeepingNumbers,
      'planStorageRules': rule != null ? jsonEncode(rule.toJson()) : null,
      'memo': remark,
      'status': status
    });
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> delPlan(dynamic planId) async {
    var resp = await HttpUtil().post('api/plan/deletePlan', data: {
      'planId': planId,
    });
    return BaseModel.fromJson(resp, (json) => json);
  }
}
