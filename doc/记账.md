# Global Parameters

**Global Param Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Global Param Query**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Global Param Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Global Auth**

> NO Auth

# Response Codes

| Response Codes | Description |
| -------------- | ----------- |
| No parameters |

# 基础接口

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-07-24 15:09:33

> Update Time: 2024-07-24 15:09:33

```text
No description
```

**Folder Param Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Query**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Auth**

> Inherit auth from parent

## 微信登录/注册

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-07-24 15:10:51

> Update Time: 2024-08-23 13:22:05

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/wxLogin

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| appCode | 011G0Fkl2IvjWd4Aagml2lTCm20G0FkT | String | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"登录成功","time":"1721806426","data":{"userInfo":{"userId":3,"mobile":"","userName":"怀羽宁安","avatar":"http:\/\/bookkeeping.sudada123.cn\/data:image\/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgaGVpZ2h0PSIxMDAiIHdpZHRoPSIxMDAiPjxyZWN0IGZpbGw9InJnYigxNjAsMTgwLDIyOSkiIHg9IjAiIHk9IjAiIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIj48L3JlY3Q+PHRleHQgeD0iNTAiIHk9IjUwIiBmb250LXNpemU9IjUwIiB0ZXh0LWNvcHk9ImZhc3QiIGZpbGw9IiNmZmZmZmYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIHRleHQtcmlnaHRzPSJhZG1pbiIgZG9taW5hbnQtYmFzZWxpbmU9ImNlbnRyYWwiPuaAgDwvdGV4dD48L3N2Zz4=","token":"bb11dfa9-2681-4569-9ded-9455378025bb"},"mobileBindStatus":0},"serial_number":"2024072497501015"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | 登录成功 | String | - |
| time | 1721806426 | String | - |
| data | - | Object | - |
| data.userInfo | - | Object | - |
| data.userInfo.userId | 3 | Number | 用户ID |
| data.userInfo.mobile | - | String | 用户手机号 |
| data.userInfo.userName | 怀羽宁安 | String | 用户昵称 |
| data.userInfo.avatar | http://bookkeeping.sudada123.cn/data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgaGVpZ2h0PSIxMDAiIHdpZHRoPSIxMDAiPjxyZWN0IGZpbGw9InJnYigxNjAsMTgwLDIyOSkiIHg9IjAiIHk9IjAiIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIj48L3JlY3Q+PHRleHQgeD0iNTAiIHk9IjUwIiBmb250LXNpemU9IjUwIiB0ZXh0LWNvcHk9ImZhc3QiIGZpbGw9IiNmZmZmZmYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIHRleHQtcmlnaHRzPSJhZG1pbiIgZG9taW5hbnQtYmFzZWxpbmU9ImNlbnRyYWwiPuaAgDwvdGV4dD48L3N2Zz4= | String | 头像 |
| data.userInfo.token | bb11dfa9-2681-4569-9ded-9455378025bb | String | token |
| data.mobileBindStatus | 0 | Number | 绑定手机号状态:1=已绑定,0=未绑定 |
| serial_number | 2024072497501015 | String | - |

* 失败(404)

```javascript
No data
```

## 绑定手机号

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-07-24 15:41:00

> Update Time: 2024-08-19 21:45:58

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/bindMobile

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | bb11dfa9-2681-4569-9ded-9455378025bb | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| mobile | *********** | String | Yes | 手机号 |
| code | 8888 | String | Yes | 验证码(万能验证8888) |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 上传

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-21 11:55:26

> Update Time: 2024-10-13 22:32:55

```text
No description
```

**API Status**

> Developing

**URL**

> api/common/upload

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| file | 甜甜圈.png | File | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 手机号登录/注册

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-23 13:23:42

> Update Time: 2024-09-23 16:08:42

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/mobilelogin

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| mobile | *********** | String | Yes | 手机号 |
| captcha | 8888 | String | Yes | 验证码 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 图标接口

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-28 09:21:18

> Update Time: 2024-09-24 00:03:53

```text
No description
```

**API Status**

> Developing

**URL**

> api/index/iconList

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 文本

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-23 22:19:58

> Update Time: 2024-10-23 22:26:56

```text
No description
```

**API Status**

> Developing

**URL**

> api/index/text

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"","time":"1729693475","data":{"aboutUs":"http:\/\/bookkeeping.sudada123.cn\/index\/index\/aboutUs","privacy":"http:\/\/bookkeeping.sudada123.cn\/index\/index\/privacy","userAgreement":"http:\/\/bookkeeping.sudada123.cn\/index\/index\/userAgreement"},"serial_number":"2024102351999948"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | - | String | - |
| time | 1729693475 | String | - |
| data | - | Object | - |
| data.aboutUs | http://bookkeeping.sudada123.cn/index/index/aboutUs | String | 关于 |
| data.privacy | http://bookkeeping.sudada123.cn/index/index/privacy | String | 隐私政策 |
| data.userAgreement | http://bookkeeping.sudada123.cn/index/index/userAgreement | String | 服务协议 |
| serial_number | 2024102351999948 | String | - |

* 失败(404)

```javascript
No data
```

# 明细

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-07-29 23:37:35

> Update Time: 2024-07-29 23:37:35

```text
No description
```

**Folder Param Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Query**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Auth**

> Inherit auth from parent

## 首页

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-07-29 23:37:41

> Update Time: 2024-10-20 21:07:34

```text
No description
```

**API Status**

> Developing

**URL**

> api/detail/homePage

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| budgetType | 1 | String | Yes | 预算类型:1=周预算,2=月预算,3=年预算 |
| bookkeepingNumber | thhhb01k1pp9h9tu28c | String | Yes | 账本编号(非必填) |
| indexModule | - | String | Yes | 首页展示模块(1-8对应账户类型,9=净资产,10=总资产，11=总负债) |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"","time":"**********","data":{"assets":{"incomeTotal":"16350","expenditureTotal":"2100","currentAssetsTotal":"14250.00"},"budgetArr":[{"budgetId":2,"budgetType":"2","budgetAmount":"1000.00","spendPrice":"0.00","accountBookIds":"1","startDate":"2024-08-01","endDate":"2024-08-31","expenditurePrice":"1000.00","budgetStatus":1,"proportion":"100.00","surplusStatus":1,"surplus":"0","dayPrice":"33.3"},{"budgetId":3,"budgetType":"3","budgetAmount":"1000.00","spendPrice":"0.00","accountBookIds":"1","startDate":"2024-01-01","endDate":"2024-12-31","expenditurePrice":"1000.00","budgetStatus":1,"proportion":"100.00","surplusStatus":1,"surplus":"0","dayPrice":"2.7"}],"planArr":[{"residuePrice":10000,"id":1,"name":"计划任务A","backgroundImage":"\/assets\/img\/avatar.png","icon":"\/assets\/img\/avatar.png","todayDay":30,"residueDay":22,"nowDay":8,"proportion":"0","accumulatedAmount":"0.00","requiredAmount":"10000.00","status":1},{"residuePrice":10000,"id":3,"name":"计划任务B","backgroundImage":"\/assets\/img\/avatar.png","icon":"\/assets\/img\/avatar.png","todayDay":30,"residueDay":22,"nowDay":8,"proportion":"0","accumulatedAmount":"0.00","requiredAmount":"10000.00","status":1}],"account":{"monthIncome":"655","monthIncomeProportion":"0.65","monthExpenditure":"400","monthExpenditureProportion":"0.40","monthBalance":"255.00"},"flowingWaterLog":[{"date":"2024-08-05","week":"monday","weekChinese":"星期一","item":[{"id":1,"icon":"","money":"25.00","after":"25.00","memo":"微信转入","type":"1","date":"2024-08-05","week":"monday","categoryName":"买手机","accountName":""}]},{"date":"2024-08-03","week":"monday","weekChinese":"星期一","item":[{"id":2,"icon":"","money":"30.00","after":"55.00","memo":"支付宝转入","type":"1","date":"2024-08-03","week":"monday","categoryName":"买手机","accountName":""}]},{"date":"2024-08-09","week":"Friday","weekChinese":"Friday","item":[{"id":3,"icon":"","money":"200.00","after":"200.00","memo":"测试","type":"1","date":"2024-08-09","week":"Friday","categoryName":"买手机","accountName":""},{"id":4,"icon":"","money":"200.00","after":"200.00","memo":"测试","type":"1","date":"2024-08-09","week":"Friday","categoryName":"买手机","accountName":""},{"id":5,"icon":"","money":"200.00","after":"400.00","memo":"测试","type":"1","date":"2024-08-09","week":"Friday","categoryName":"买手机","accountName":""}]}]},"serial_number":"****************"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | - | String | - |
| time | ********** | String | - |
| data | - | Object | - |
| data.assets | - | Object | 资产 |
| data.assets.incomeTotal | 16350 | String | 收入 |
| data.assets.expenditureTotal | 2100 | String | 支出 |
| data.assets.currentAssetsTotal | 14250.00 | String | 当前资产 |
| data.budgetArr | - | Array | 预算 |
| data.budgetArr.budgetId | 2 | Number | 预算ID |
| data.budgetArr.budgetType | 2 | String | 预算类型:1=周,2=月,3=年 |
| data.budgetArr.budgetAmount | 1000.00 | String | 预算额 |
| data.budgetArr.spendPrice | 0.00 | String | 已花费金额 |
| data.budgetArr.accountBookIds | 1 | String | - |
| data.budgetArr.startDate | 2024-08-01 | String | 开始 |
| data.budgetArr.endDate | 2024-08-31 | String | 结束日期 |
| data.budgetArr.expenditurePrice | 1000.00 | String | 剩余预算 |
| data.budgetArr.budgetStatus | 1 | Number | 状态:1=未满,2=已满 |
| data.budgetArr.proportion | 100.00 | String | 比例条 |
| data.budgetArr.surplusStatus | 1 | Number | 超支状态:=未超支 0=已超支 |
| data.budgetArr.surplus | 0 | String | 超支时间 |
| data.budgetArr.dayPrice | 33.3 | String | 剩余日均 |
| data.planArr | - | Array | - |
| data.planArr.residuePrice | 10000 | Number | 剩余金额 |
| data.planArr.id | 1 | Number | 计划ID |
| data.planArr.name | 计划任务A | String | 计划名称 |
| data.planArr.backgroundImage | /assets/img/avatar.png | String | 计划背景 |
| data.planArr.icon | /assets/img/avatar.png | String | 计划图标 |
| data.planArr.todayDay | 30 | Number | 总天数 |
| data.planArr.residueDay | 22 | Number | 剩余天数 |
| data.planArr.nowDay | 8 | Number | 当前天数 |
| data.planArr.proportion | 0 | String | 比例 |
| data.planArr.accumulatedAmount | 0.00 | String | 已攒金额 |
| data.planArr.requiredAmount | 10000.00 | String | 所需金额 |
| data.planArr.status | 1 | Number | 是否实现:1=未实现,2=已实现 |
| data.account | - | Object | 账目明细 |
| data.account.monthIncome | 655 | String | 本月收入 |
| data.account.monthIncomeProportion | 0.65 | String | - |
| data.account.monthExpenditure | 400 | String | 本月支出 |
| data.account.monthExpenditureProportion | 0.40 | String | - |
| data.account.monthBalance | 255.00 | String | 本月结余 |
| data.flowingWaterLog | - | Array | - |
| data.flowingWaterLog.date | 2024-08-05 | String | 流水日期 |
| data.flowingWaterLog.week | monday | String | 流水星期 |
| data.flowingWaterLog.weekChinese | 星期一 | String | 中文星期 |
| data.flowingWaterLog.item | - | Array | - |
| data.flowingWaterLog.item.id | 1 | Number | 流水记录ID |
| data.flowingWaterLog.item.icon | - | String | 图标 |
| data.flowingWaterLog.item.money | 25.00 | String | 金额 |
| data.flowingWaterLog.item.after | 25.00 | String | 变化后金额 |
| data.flowingWaterLog.item.memo | 微信转入 | String | 备注 |
| data.flowingWaterLog.item.type | 1 | String | 类型:1=收入,2=支出 |
| data.flowingWaterLog.item.date | 2024-08-05 | String | 日期 |
| data.flowingWaterLog.item.week | monday | String | - |
| data.flowingWaterLog.item.categoryName | 买手机 | String | - |
| data.flowingWaterLog.item.accountName | - | String | - |
| serial_number | **************** | String | - |

* 失败(404)

```javascript
No data
```

## 首页搜索获取流水/节省/非必要

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-18 23:34:24

> Update Time: 2024-10-20 00:25:31

```text
No description
```

**API Status**

> Developing

**URL**

> api/detail/searchFlowingWater

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| keyWords | 手机 | String | Yes | 关键字 |
| type | - | String | Yes | 1=节省，2=非必要存入 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 日历页面

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-05 22:39:32

> Update Time: 2024-10-19 23:45:07

```text
No description
```

**API Status**

> Developing

**URL**

> api/detail/getDayFlowingWaterLog

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| date | 2024-08-03 | String | Yes | 日期 |
| bookkeepingNumber | - | String | No | 账本 |
| month | 2024-10 | String | Yes | 月份 |
| pageCount | 10 | String | Yes | 页面数量 |
| pageSize | 1 | String | Yes | 页码 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"","time":"**********","data":{"calendarLog":[{"date":"2024-08-05","week":"monday","weekChinese":"星期一","item":[],"totalIncome":25,"totalPayment":0},{"date":"2024-08-03","week":"monday","weekChinese":"星期一","item":[],"totalIncome":30,"totalPayment":0},{"date":"2024-08-09","week":"Friday","weekChinese":"Friday","item":[],"totalIncome":600,"totalPayment":0},{"date":"2024-08-21","week":"Wednesday","weekChinese":"Wednesday","item":[],"totalIncome":0,"totalPayment":100},{"date":"2024-08-23","week":"Friday","weekChinese":"Friday","item":[],"totalIncome":0,"totalPayment":300}],"flowingWaterLog":[{"id":2,"icon":"","money":"30.00","after":"55.00","memo":"支付宝转入","type":"1","date":"2024-08-03","week":"monday","account_id":null,"cardName":null}],"total":{"totalIncome":30,"totalPayment":0}},"serial_number":"****************"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | 交易状态码 |
| msg | - | String | 交易信息 |
| time | ********** | String | - |
| data | - | Object | 交易数据 |
| data.calendarLog | - | Array | 日历 |
| data.calendarLog.date | 2024-08-05 | String | 日历日期 |
| data.calendarLog.week | monday | String | 日历周数 |
| data.calendarLog.weekChinese | 星期一 | String | 中文周名称 |
| data.calendarLog.item | - | Array | 日历项目 |
| data.calendarLog.totalIncome | 25 | Number | 总收入 |
| data.calendarLog.totalPayment | 0 | Number | 总支出 |
| data.flowingWaterLog | - | Array | 流水日志 |
| data.flowingWaterLog.id | 2 | Number | 流水日志ID |
| data.flowingWaterLog.icon | - | String | 流水日志图标 |
| data.flowingWaterLog.money | 30.00 | String | 流水金额 |
| data.flowingWaterLog.after | 55.00 | String | 后续操作 |
| data.flowingWaterLog.memo | 支付宝转入 | String | 备注 |
| data.flowingWaterLog.type | 1 | String | 交易类型1=收入,2=支出 |
| data.flowingWaterLog.date | 2024-08-03 | String | 交易日期 |
| data.flowingWaterLog.week | monday | String | 交易周数 |
| data.flowingWaterLog.account_id | - | Null | 账户ID |
| data.flowingWaterLog.cardName | - | Null | 账户名 |
| data.total | - | Object | 总计 |
| data.total.totalIncome | 30 | Number | 流水总收入 |
| data.total.totalPayment | 0 | Number | 流水总支出 |
| serial_number | **************** | String | 序列号 |

* 失败(404)

```javascript
No data
```

## 单日记录详情

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-05 23:00:31

> Update Time: 2024-10-08 21:29:59

```text
No description
```

**API Status**

> Developing

**URL**

> api/detail/getDayDetail

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| date | 2024-10-08 | String | Yes | 日期 |
| bookkeepingNumber | thhhb01k1pp9h9tu28c | String | No | 账本编号 |
| keyWord | - | String | No | 关键词搜索 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"","time":"1722870418","data":{"item":[{"id":1,"icon":"http:\/\/bookkeeping.sudada123.cn\/","money":"25.00","after":"25.00","memo":"微信转入","type":"1","date":"2024-08-05","week":"monday"}],"totalArr":{"payCount":1,"payTotal":"25.00"}},"serial_number":"2024080550511001"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | - | String | - |
| time | 1722870418 | String | - |
| data | - | Object | - |
| data.item | - | Array | - |
| data.item.id | 1 | Number | - |
| data.item.icon | http://bookkeeping.sudada123.cn/ | String | - |
| data.item.money | 25.00 | String | - |
| data.item.after | 25.00 | String | - |
| data.item.memo | 微信转入 | String | - |
| data.item.type | 1 | String | - |
| data.item.date | 2024-08-05 | String | 日期 |
| data.item.week | monday | String | - |
| data.totalArr | - | Object | - |
| data.totalArr.payCount | 1 | Number | 消费笔数 |
| data.totalArr.payTotal | 25.00 | String | 消费总额 |
| serial_number | 2024080550511001 | String | - |

* 失败(404)

```javascript
No data
```

## 预算页面

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-29 14:27:14

> Update Time: 2024-10-20 21:51:18

```text
No description
```

**API Status**

> Developing

**URL**

> api/detail/budgetindex

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"","time":"**********","data":{"budgetList":[{"budgetId":1,"budgetType":"1","budgetAmount":"1000.00","spendPrice":"0.00","accountBookIds":"1","startDate":"","endDate":"","expenditurePrice":"1000.00","budgetStatus":1,"proportion":"100.00","surplusStatus":1,"surplus":"0","dayPrice":"142.8","bookkeepingArrInfo":[{"bookkeepingId":1,"bookkeepingName":"测试账本"}]},{"budgetId":2,"budgetType":"2","budgetAmount":"1000.00","spendPrice":"0.00","accountBookIds":"1","startDate":"","endDate":"","expenditurePrice":"1000.00","budgetStatus":1,"proportion":"100.00","surplusStatus":1,"surplus":"0","dayPrice":"33.3","bookkeepingArrInfo":[{"bookkeepingId":1,"bookkeepingName":"测试账本"}]},{"budgetId":3,"budgetType":"3","budgetAmount":"1000.00","spendPrice":"0.00","accountBookIds":"1","startDate":"","endDate":"","expenditurePrice":"1000.00","budgetStatus":1,"proportion":"100.00","surplusStatus":1,"surplus":"0","dayPrice":"2.7","bookkeepingArrInfo":[{"bookkeepingId":1,"bookkeepingName":"测试账本"}]}],"accountList":[{"id":5,"isJoinTotal":1,"accountIcon":"","cardType":"8","price":"100","accountTypeName":"大笔资产","selectedStatus":0},{"id":4,"isJoinTotal":1,"accountIcon":"","cardType":"4","price":"100.00","accountTypeName":"现金账户","selectedStatus":0},{"id":3,"isJoinTotal":1,"accountIcon":"\/assets\/img\/avatar.png","cardType":"3","price":"100.00","accountTypeName":"支付宝","selectedStatus":0},{"id":2,"isJoinTotal":1,"accountIcon":"","cardType":"2","price":"10000","accountTypeName":"信用卡","accountNumber":"中信银行(4545)","selectedStatus":0},{"id":1,"isJoinTotal":1,"accountIcon":"","cardType":"1","price":"9900.00","accountTypeName":"储蓄卡","accountNumber":"建设银行(6565)","selectedStatus":0},{"isJoinTotal":0,"accountIcon":"","price":"16350","accountTypeName":"总资产","cardType":9,"selectedStatus":0},{"isJoinTotal":0,"accountIcon":"","price":"16350","accountTypeName":"净资产","cardType":10,"selectedStatus":0},{"isJoinTotal":0,"accountIcon":"","price":"0","accountTypeName":"总负债","cardType":11,"selectedStatus":0}]},"serial_number":"****************"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | 返回状态码 |
| msg | - | String | 返回信息 |
| time | ********** | String | 时间戳 |
| data | - | Object | - |
| data.budgetList | - | Array | - |
| data.budgetList.budgetId | 1 | Number | 预算ID |
| data.budgetList.budgetType | 1 | String | 预算类型1=周预算,2=月预算,3=年预算 |
| data.budgetList.budgetAmount | 1000.00 | String | 预算金额 |
| data.budgetList.spendPrice | 0.00 | String | 已消费金额 |
| data.budgetList.accountBookIds | 1 | String | 预算绑定账本ID |
| data.budgetList.startDate | - | String | 预算开始日期 |
| data.budgetList.endDate | - | String | 预算结束日期 |
| data.budgetList.expenditurePrice | 1000.00 | String | 预算剩余金额 |
| data.budgetList.budgetStatus | 1 | Number | 状态:1=未满,2=已满 |
| data.budgetList.proportion | 100.00 | String | 比例条 |
| data.budgetList.surplusStatus | 1 | Number | 超支状态:=未超支 0=已超支 |
| data.budgetList.surplus | 0 | String | 超支时间 |
| data.budgetList.dayPrice | 142.8 | String | 剩余日均 |
| data.budgetList.bookkeepingArrInfo | - | Array | - |
| data.budgetList.bookkeepingArrInfo.bookkeepingId | 1 | Number | 账本ID |
| data.budgetList.bookkeepingArrInfo.bookkeepingName | 测试账本 | String | 账本名称 |
| data.accountList | - | Array | - |
| data.accountList.id | 5 | Number | 账户ID |
| data.accountList.isJoinTotal | 1 | Number | 计入总资产:1=是,2=否 |
| data.accountList.accountIcon | - | String | 账户图标 |
| data.accountList.cardType | 8 | String | - |
| data.accountList.price | 100 | String | 账户金额 |
| data.accountList.accountTypeName | 大笔资产 | String | 账户类型名 |
| data.accountList.selectedStatus | 0 | Number | 选中状态:1=是,0=否 |
| data.accountList.accountNumber | 中信银行(4545) | String | 卡号 |
| serial_number | **************** | String | - |

* 失败(404)

```javascript
No data
```

## 预算新增

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-07-30 21:47:06

> Update Time: 2024-09-19 20:24:21

```text
No description
```

**API Status**

> Developing

**URL**

> api/detail/budgetSet

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | be57da43-6e5f-44c7-a5f4-a4ebc95a351b | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| budgetType | 1 | String | Yes | 预算类型:1=周预算,2=月预算,3=年预算 |
| budgetAmount | 1000 | String | Yes | 预算金额 |
| accountBookNumbers | thhhb01k5pp9h9tu28c | String | Yes | 账本编号集合(逗号拼接) |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 预算修改账本

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-29 15:40:03

> Update Time: 2024-09-19 21:13:05

```text
No description
```

**API Status**

> Developing

**URL**

> api/detail/budgetEdit

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | {{LoginToken}} | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| budgetId | 1 | String | Yes | 预算ID |
| accountBookNumbers | thhhb01k1pp9h9tu28c,ryx1n0156fxhd0j5zjh | String | Yes | 账本编号合集(逗号拼接) |
| budgetAmount | - | String | No | 预算金额 |
| spendPrice | - | String | No | 花费金额 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 预算修改金额

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-09-01 09:22:49

> Update Time: 2024-09-01 09:25:38

```text
No description
```

**API Status**

> Developing

**URL**

> api/detail/budgetEditAmount

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | {{LoginToken}} | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| budgetId | 1 | String | Yes | 预算ID |
| budgetAmount | 1500 | String | Yes | 预算金额 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 切换资产显示模块

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-29 15:38:10

> Update Time: 2024-09-01 09:23:40

```text
No description
```

**API Status**

> Developing

**URL**

> api/detail/changeAccountModule

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | {{LoginToken}} | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| cardType | 1 | String | Yes | 资产显示模块类型 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

# 账本

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-07-30 14:28:56

> Update Time: 2024-07-30 14:28:56

```text
No description
```

**Folder Param Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Query**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Auth**

> Inherit auth from parent

## 账本列表

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-07-30 15:06:10

> Update Time: 2024-10-22 10:46:37

```text
No description
```

**API Status**

> Developing

**URL**

> api/bookkeeping/getBookkeepingList

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | {{LoginToken}} | String | Yes | - |
| token | 3080e327-3cdf-4bbc-bfa3-b46eae7a1940 | String | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"","time":"**********","data":[{"bookkeepingId":5,"bookkeepingNumber":"thhhb01k5pp9h9tu28c","accountBookName":"测试账本","bookkeepingIcon":"http:\/\/bookkeeping.sudada123.cn\/assets\/img\/avatar.png","memo":"测试"}],"serial_number":"****************"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | - | String | - |
| time | ********** | String | - |
| data | - | Array | - |
| data.bookkeepingId | 5 | Number | 账本ID |
| data.bookkeepingNumber | thhhb01k5pp9h9tu28c | String | 账本编号 |
| data.accountBookName | 测试账本 | String | 账本名称 |
| data.bookkeepingIcon | http://bookkeeping.sudada123.cn/assets/img/avatar.png | String | 账本图标 |
| data.memo | 测试 | String | 备注 |
| serial_number | **************** | String | - |

* 失败(404)

```javascript
No data
```

## 账本图标(已废弃)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-20 10:04:57

> Update Time: 2024-09-02 13:10:39

```text
No description
```

**API Status**

> Developing

**URL**

> api/bookkeeping/getBookkeepingIcon

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 新建账本

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-07-30 15:06:34

> Update Time: 2024-08-29 09:21:42

```text
No description
```

**API Status**

> Developing

**URL**

> api/bookkeeping/addBookkeeping

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | {{LoginToken}} | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| accountBookName | 测试账本1111222 | String | Yes | 账本名称 |
| icon | /assets/img/avatar.png | String | Yes | 图标 |
| isJoinTotal | 1 | String | Yes | 计入总资产:1=是,2=否 |
| memo | 测试 | String | No | 备注 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"","time":"**********","data":{"accountBookName":"测试账本","bookkeepingIcon":"http:\/\/bookkeeping.sudada123.cn\/assets\/img\/avatar.png","bookkeepingId":"5","bookkeepingNumber":"thhhb01k5pp9h9tu28c","isJoinTotal":"1","memo":"测试"},"serial_number":"****************"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | - | String | - |
| time | ********** | String | - |
| data | - | Object | - |
| data.accountBookName | 测试账本 | String | 账本名称 |
| data.bookkeepingIcon | http://bookkeeping.sudada123.cn/assets/img/avatar.png | String | 账本图标 |
| data.bookkeepingId | 5 | String | 账本ID |
| data.bookkeepingNumber | thhhb01k5pp9h9tu28c | String | 账本编号 |
| data.isJoinTotal | 1 | String | 计入总资产:1=是,2=否 |
| data.memo | 测试 | String | 备注 |
| serial_number | **************** | String | - |

* 失败(404)

```javascript
No data
```

## 修改账本

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-07-30 17:31:04

> Update Time: 2024-07-30 17:32:51

```text
No description
```

**API Status**

> Developing

**URL**

> api/bookkeeping/updBookkeeping

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | bb11dfa9-2681-4569-9ded-9455378025bb | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| accountBookName | 测试账本 | String | Yes | 账本名称 |
| icon | /assets/img/avatar.png | String | Yes | 图标 |
| isJoinTotal | 1 | String | Yes | 计入总资产:1=是,2=否 |
| memo | 测试111 | String | No | 备注 |
| bookkeepingNumber | thhhb01k5pp9h9tu28c | String | Yes | 账本编号 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"","time":"**********","data":{"accountBookName":"测试账本","bookkeepingIcon":"http:\/\/bookkeeping.sudada123.cn\/assets\/img\/avatar.png","bookkeepingId":"5","bookkeepingNumber":"thhhb01k5pp9h9tu28c","isJoinTotal":"1","memo":"测试"},"serial_number":"****************"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | - | String | - |
| time | ********** | String | - |
| data | - | Object | - |
| data.accountBookName | 测试账本 | String | 账本名称 |
| data.bookkeepingIcon | http://bookkeeping.sudada123.cn/assets/img/avatar.png | String | 账本图标 |
| data.bookkeepingId | 5 | String | 账本ID |
| data.bookkeepingNumber | thhhb01k5pp9h9tu28c | String | 账本编号 |
| data.isJoinTotal | 1 | String | 计入总资产:1=是,2=否 |
| data.memo | 测试 | String | 备注 |
| serial_number | **************** | String | - |

* 失败(404)

```javascript
No data
```

## 删除账本

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-07-30 17:37:40

> Update Time: 2024-07-30 17:38:20

```text
No description
```

**API Status**

> Developing

**URL**

> api/bookkeeping/delBookkeeping

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | bb11dfa9-2681-4569-9ded-9455378025bb | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| bookkeepingNumber | thhhb01k5pp9h9tu28c | String | Yes | 账本编号 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 流水分类(添加流水页面专用)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-07-31 15:24:09

> Update Time: 2024-10-11 21:15:17

```text
No description
```

**API Status**

> Developing

**URL**

> api/bookkeeping/getBookkeepingCategory

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 流水分类(添加三级分类页面专用)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-09 23:27:16

> Update Time: 2024-10-13 12:32:56

```text
No description
```

**API Status**

> Developing

**URL**

> api/bookkeeping/getBookkeepingCategoryPage

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| categoryType | 1 | String | Yes | 分类类型:1=支出,2=收入 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 流水分类调整排序

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-11 21:28:37

> Update Time: 2024-10-13 20:42:27

```text
No description
```

**API Status**

> Developing

**URL**

> api/bookkeeping/updateBookkeepingCategorySort

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| sortedId | 38,33,35,34,36 | String | Yes | - |
| categoryType | 1 | String | Yes | 流水分类类型：1=支出，2=收入 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 新增流水分类

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-07-31 15:50:45

> Update Time: 2024-10-19 23:15:59

```text
No description
```

**API Status**

> Developing

**URL**

> api/bookkeeping/addBookkeepingCategory

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> json

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

```javascript
{
    "pid": "11",
    "icon": "/uploads/20240924/b3988be368b82e1990e6a28c73fd529a.png",
    "bookkeepingCategoryName": "哈哈哈哈哈1",
    "categoryType": "1",
    "token": "4a0d7191-be7e-4357-818a-14c4f9c96803"
}
```

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"添加成功","time":"1722475483","data":{"user_id":3,"type":"1","level":2,"pid":"1","icon":"http:\/\/bookkeeping.sudada123.cn\/uploads\/dd7093143fe4fa50355e772c252d85fd.jpg","name":"买手机","text_as_icon_status":"1","id":"22"},"serial_number":"2024080198575248"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | 添加成功 | String | - |
| time | 1722475483 | String | - |
| data | - | Object | - |
| data.user_id | 3 | Number | - |
| data.type | 1 | String | 分类类型:1=支出分类,2=收入分类 |
| data.level | 2 | Number | 等级:1=一级分类,2=二级分类 |
| data.pid | 1 | String | 一级分类ID |
| data.icon | http://bookkeeping.sudada123.cn/uploads/dd7093143fe4fa50355e772c252d85fd.jpg | String | 分类图标 |
| data.name | 买手机 | String | 分类名 |
| data.text_as_icon_status | 1 | String | - |
| data.id | 22 | String | 分类ID |
| serial_number | 2024080198575248 | String | - |

* 失败(404)

```javascript
No data
```

## 删除流水分类

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-11 21:07:51

> Update Time: 2024-10-11 21:10:32

```text
No description
```

**API Status**

> Developing

**URL**

> api/bookkeeping/deleteBookkeepingCategory

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| bookkeepingCategoryId | 22 | String | Yes | 流水分类ID |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 新增流水

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-08 16:37:09

> Update Time: 2024-10-20 00:00:27

```text
No description
```

**API Status**

> Developing

**URL**

> api/bookkeeping/addBill

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> json

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

```javascript
{
    "bookkeepingNumber": "thhhb01k1pp9h9tu28c",
    "categoryId": "40",
    "memo": "",
    "action": "2",
    "money": "23.00",
    "isSave": "1",
    "isNecessaryStatus": "1",
    "nowTime": "2024-10-19",
    "accountId": 21,
    "token": "4a0d7191-be7e-4357-818a-14c4f9c96803"
}
```

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 新增借贷往来

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-20 13:58:08

> Update Time: 2024-10-18 17:35:59

```text
No description
```

**API Status**

> Developing

**URL**

> api/bookkeeping/addDebt

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| actionType | 2 | String | Yes | 操作类型:1=借入,2=借出 |
| money | 1000 | String | Yes | 金额 |
| otherSideAccountId | 3 | String | Yes | 对方(账户ID) |
| accountId | 1 | String | Yes | 账户ID |
| memo | 借给张三 存入 储蓄卡账户 | String | Yes | 备注 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 添加借支(暂不使用)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-08 23:23:15

> Update Time: 2024-10-08 23:27:43

```text
No description
```

**API Status**

> Developing

**URL**

> api/bookkeeping/addBorrow

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | - | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| borrowType | - | String | Yes | 借支类型:1=收支,2=借出 |
| otherSide | - | String | Yes | 对方名称 |
| accountId | - | String | Yes | 账户ID |
| money | - | String | Yes | 金额 |
| memo | - | String | Yes | 备注 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 流水分类图标(已废弃)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-23 11:55:07

> Update Time: 2024-09-02 13:10:52

```text
No description
```

**API Status**

> Developing

**URL**

> api/index/getMoneyLogCategoryicon

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| categoryId | 11 | String | Yes | 分类ID(二级分类ID) |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

# VIP

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-01 13:18:43

> Update Time: 2024-10-08 21:40:13

```text
No description
```

**Folder Param Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Query**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Auth**

> Inherit auth from parent

## 会员价格

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-01 13:19:17

> Update Time: 2024-10-21 11:22:04

```text
No description
```

**API Status**

> Developing

**URL**

> api/vip/index

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"","time":"1725343434","data":[{"id":1,"type":"1","month_count":"1","price":"5.00","createtime":"2024-08-01 13:17:30"},{"id":2,"type":"2","month_count":"12","price":"38.00","createtime":"2024-08-01 13:18:02"},{"id":3,"type":"3","month_count":"-1","price":"98.00","createtime":"2024-08-01 13:18:07"}],"serial_number":"2024090397564949"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | - | String | - |
| time | 1725343434 | String | - |
| data | - | Array | - |
| data.id | 1 | Number | 价格id |
| data.type | 1 | String | 类型:1=月,2=年,3=永久 |
| data.month_count | 1 | String | 时长:月=1,年=12,永久=-1 |
| data.price | 5.00 | String | 价格 |
| data.createtime | 2024-08-01 13:17:30 | String | - |
| serial_number | 2024090397564949 | String | - |

* 失败(404)

```javascript
No data
```

## 开通会员

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-01 13:20:19

> Update Time: 2024-10-21 11:28:05

```text
No description
```

**API Status**

> Developing

**URL**

> api/vip/openVip

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| vipConfigId | 1 | String | Yes | 会员配置ID |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 支付订单

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-02 11:35:26

> Update Time: 2024-10-19 23:23:20

```text
No description
```

**API Status**

> Developing

**URL**

> api/vip/payOrder

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| orderNumber | 24101923163038366 | String | Yes | 订单编号 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

# 账户

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-09 15:37:11

> Update Time: 2024-08-09 15:37:11

```text
No description
```

**Folder Param Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Query**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Auth**

> Inherit auth from parent

## 新增账户(储蓄卡)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-09 15:38:15

> Update Time: 2024-10-24 15:20:38

```text
No description
```

**API Status**

> Developing

**URL**

> api/card/addAccount

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| accountType | 1 | String | Yes | 账户类型 |
| balance | 10000 | String | Yes | 金额 |
| isJoinTotal | 1 | String | Yes | 是否计入总资产:1=是,2=否 |
| bankId | 1 | String | Yes | 银行Id |
| cardNo | *****************333.. | String | Yes | 银行卡号 |
| accountName | 建设储蓄卡账 | String | No | 账户名称(非必填) |
| accountId | 32 | String | Yes | 账户ID(编辑需传) |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 新增账户(信用卡)) 

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-09 15:48:59

> Update Time: 2024-10-23 13:37:21

```text
No description
```

**API Status**

> Developing

**URL**

> api/card/addAccount

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> json

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

```javascript
{
    "accountType": "2",
    "exemptionConditions": {
        "type": "2",
        "count": null,
        "time": null,
        "price": "3000"
    },
    "isJoinTotal": "1",
    "quota": "50000",
    "annualFee": "10/17",
    "billDate": "10/16",
    "repaymentDate": "10/18",
    "issuedBill": "1000",
    "noissuedBill": "20000",
    "consumptionAmount": "100",
    "bankId": "6",
    "cardNo": "999988",
    "token": "90d53789-640c-45f4-88c4-fa92c2f6e469"
}
```

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 新增账户(网络账户) 

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-09 15:49:05

> Update Time: 2024-10-17 11:32:45

```text
No description
```

**API Status**

> Developing

**URL**

> api/card/addAccount

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| accountType | 3 | String | Yes | 账户类型 |
| balance | 100 | String | Yes | 金额 |
| isJoinTotal | 1 | String | Yes | 是否计入总资产:1=是,2=否 |
| accountName | *********** | String | Yes | 账户名称(networkType为3时必填) |
| networkType | 1 | String | Yes | 类型:1=微信,2=支付宝,3=其它 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 新增账户(现金账户) 

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-09 15:49:12

> Update Time: 2024-10-16 22:34:02

```text
No description
```

**API Status**

> Developing

**URL**

> api/card/addAccount

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| accountType | 4 | String | Yes | 账户类型 |
| balance | 1000 | String | Yes | 金额 |
| isJoinTotal | 1 | String | Yes | 是否计入总资产:1=是,2=否 |
| accountName | 现金账户名称 | String | Yes | 账户(必填) |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 新增账户(投资账户) 

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-09 15:49:16

> Update Time: 2024-10-16 22:35:54

```text
No description
```

**API Status**

> Developing

**URL**

> api/card/addAccount

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| accountType | 5 | String | Yes | 账户类型 |
| balance | 100 | String | Yes | 金额 |
| isJoinTotal | 1 | String | Yes | 是否计入总资产:1=是,2=否 |
| accountName | 股票2 | String | Yes | 账户名称 |
| accountIncome | 20 | String | No | 收益 |
| memo | 涨了涨了 | String | No | 备注 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 新增账户(信贷往来) 

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-09 15:49:19

> Update Time: 2024-10-18 17:34:55

```text
No description
```

**API Status**

> Developing

**URL**

> api/card/addAccount

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| accountType | 6 | String | Yes | 账户类型 |
| isJoinTotal | 2 | String | Yes | 是否计入总资产:1=是,2=否 |
| price | 100 | String | Yes | 金额 |
| date | 2024-09-09 | String | Yes | 日期 |
| balance | 30 | String | Yes | 余额 |
| memo | 我借给张三100 | String | No | 备注 |
| accountName | 张三 | String | No | 账户名称 |
| loanType | 2 | String | Yes | 1=向谁借2=借给谁 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 新增账户(捐赠账户) 

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-09 15:49:24

> Update Time: 2024-10-16 23:09:35

```text
No description
```

**API Status**

> Developing

**URL**

> api/card/addAccount

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| accountType | 7 | String | Yes | 账户类型 |
| name | 李四 | String | Yes | 被捐赠人姓名 |
| price | 50 | String | Yes | 捐赠金额 |
| memo | - | String | Yes | 备注 |
| donateType | 2 | String | Yes | 捐赠类型1=捐赠给他人,2=被他人捐赠 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 新增账户(大笔资产)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-09 15:49:27

> Update Time: 2024-10-16 22:24:15

```text
No description
```

**API Status**

> Developing

**URL**

> api/card/addAccount

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | bb11dfa9-2681-4569-9ded-9455378025bb | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| accountType | 8 | String | Yes | 账户类型 |
| isJoinTotal | 1 | String | Yes | 是否计入总资产:1=是,2=否 |
| propertyPrice | 100 | String | Yes | 大笔资产金额 |
| propertyType | 2 | String | Yes | 大笔资产变更类型:1=折旧,2=增值 |
| propertyProportion | 1 | String | Yes | 变更比例(%) |
| accountName | - | String | Yes | 账户名称 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 账户列表

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-12 14:06:33

> Update Time: 2024-10-19 22:53:49

```text
No description
```

**API Status**

> Developing

**URL**

> api/card/list

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> json

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

```javascript
No data
```

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"","time":"**********","data":[{"id":50,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"6","network_type":null,"account_name":"借给王五","account_icon":"\/uploads\/********\/968f307420e41fb9cf5a5cbb48123254.png","account_income":null,"loan_type":2,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":"5.00","amount_income_date":"2024-10-17","amount_income_balance":"5","donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"5.00","is_join_total":1,"createtime":"2024-10-18 03:03:54","is_default":"2","is_del":1,"memo":"","accountType":"6","isJoinTotal":1,"accountIcon":"\/uploads\/********\/968f307420e41fb9cf5a5cbb48123254.png","accountName":"借给王五","date":"2024-10-17","balance":"5"},{"id":49,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"6","network_type":null,"account_name":"向李四借款","account_icon":"\/uploads\/********\/968f307420e41fb9cf5a5cbb48123254.png","account_income":null,"loan_type":1,"amount_due_name":null,"amount_due_price":"4","amount_due_date":"2024-10-13","amount_due_balance":"4","amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"4","is_join_total":1,"createtime":"2024-10-18 03:03:31","is_default":"2","is_del":1,"memo":"","accountType":"6","isJoinTotal":1,"accountIcon":"\/uploads\/********\/968f307420e41fb9cf5a5cbb48123254.png","accountName":"向李四借款","date":"2024-10-13","balance":"4"},{"id":48,"user_id":3,"bank_id":2,"bank_name":"华夏银行","card_no":"6666","card_type":"2","network_type":null,"account_name":"","account_icon":"\/uploads\/********\/0a1e2f5835636bbec6cef2a5053bf7d0.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":"11","bill_date":"04\/10","repayment_date":"12\/10","issued_bill":"55","noissued_bill":"55","available_amount":"11","consumption_amount":"22","exemption_conditions":{"type":"1","count":"5","time":"1","price":""},"promotion_name":"","promotion_conditions":[{"name":"11@@","type":"1","price":"","count":"3","time":"2"},{"name":"22##","type":"2","price":"25","count":"","time":"1"},{"name":"33$$","type":"3","price":"","count":"50","time":"1"}],"annual_status":null,"annual_fee":"06\/10","annual_price":"0.00","price":"11","is_join_total":1,"createtime":"2024-10-17 21:07:22","is_default":"2","is_del":1,"memo":null,"accountType":"2","isJoinTotal":1,"accountIcon":"\/uploads\/********\/0a1e2f5835636bbec6cef2a5053bf7d0.png","accountName":"华夏银行(6666)","bankId":2,"cardNo":"6666"},{"id":47,"user_id":3,"bank_id":3,"bank_name":"中信银行","card_no":"1234","card_type":"2","network_type":null,"account_name":"","account_icon":"\/uploads\/********\/0a1e2f5835636bbec6cef2a5053bf7d0.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":"100","bill_date":"16\/10","repayment_date":"12\/10","issued_bill":"200","noissued_bill":"300","available_amount":"400","consumption_amount":"500","exemption_conditions":{"type":"1","count":"6","time":"3","price":""},"promotion_name":"","promotion_conditions":[{"name":"优惠1","type":"1","price":"","count":"3","time":"3"}],"annual_status":null,"annual_fee":"","annual_price":"0.00","price":"100","is_join_total":1,"createtime":"2024-10-17 20:12:45","is_default":"2","is_del":1,"memo":null,"accountType":"2","isJoinTotal":1,"accountIcon":"\/uploads\/********\/0a1e2f5835636bbec6cef2a5053bf7d0.png","accountName":"中信银行(1234)","bankId":3,"cardNo":"1234"},{"id":43,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"3","network_type":"1","account_name":"测试微信","account_icon":"\/uploads\/********\/54d90ddadf6bf3ddc3e66bcb5299ad25.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"100.00","is_join_total":1,"createtime":"2024-10-17 19:48:45","is_default":"2","is_del":1,"memo":null,"accountType":"3","isJoinTotal":1,"accountIcon":"\/uploads\/********\/54d90ddadf6bf3ddc3e66bcb5299ad25.png","accountName":"测试微信","balance":"100.00","networkType":"1"},{"id":42,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"3","network_type":"1","account_name":"1234","account_icon":"\/uploads\/********\/54d90ddadf6bf3ddc3e66bcb5299ad25.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"11.00","is_join_total":1,"createtime":"2024-10-17 19:45:27","is_default":"2","is_del":1,"memo":null,"accountType":"3","isJoinTotal":1,"accountIcon":"\/uploads\/********\/54d90ddadf6bf3ddc3e66bcb5299ad25.png","accountName":"1234","balance":"11.00","networkType":"1"},{"id":41,"user_id":3,"bank_id":1,"bank_name":"平安银行","card_no":"5555","card_type":"1","network_type":null,"account_name":"","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"32.00","is_join_total":1,"createtime":"2024-10-17 19:26:07","is_default":"2","is_del":1,"memo":null,"accountType":"1","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"平安银行(5555)","bankId":1,"cardNo":"5555","balance":"32.00"},{"id":40,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"7","network_type":null,"account_name":"捐给张三","account_icon":"\/uploads\/********\/dd0619e9d98cc31f22a509fd6590ac96.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":1,"donated_name":null,"be_donated_name":"捐给张三","donated_price":"100.00","property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"100.00","is_join_total":1,"createtime":"2024-10-17 01:23:05","is_default":"2","is_del":1,"memo":"日主","accountType":"7","isJoinTotal":1,"accountIcon":"\/uploads\/********\/dd0619e9d98cc31f22a509fd6590ac96.png","donateType":1,"name":"捐给张三","accountName":"捐给张三"},{"id":39,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"6","network_type":null,"account_name":"张三","account_icon":"\/uploads\/********\/968f307420e41fb9cf5a5cbb48123254.png","account_income":null,"loan_type":2,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":"100.00","amount_income_date":"2024-10-16","amount_income_balance":"58","donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"100.00","is_join_total":1,"createtime":"2024-10-17 01:10:43","is_default":"2","is_del":1,"memo":"","accountType":"6","isJoinTotal":1,"accountIcon":"\/uploads\/********\/968f307420e41fb9cf5a5cbb48123254.png","accountName":"张三","date":"2024-10-16","balance":"58"},{"id":38,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"5","network_type":null,"account_name":"投资投资","account_icon":"\/uploads\/********\/9afc691d63340e3347717ce120acaffd.png","account_income":"200","loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"100.00","is_join_total":1,"createtime":"2024-10-17 00:55:47","is_default":"2","is_del":1,"memo":"备注测试","accountType":"5","isJoinTotal":1,"accountIcon":"\/uploads\/********\/9afc691d63340e3347717ce120acaffd.png","accountName":"投资投资","balance":"100.00","accountIncome":"200"},{"id":37,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"4","network_type":null,"account_name":"现金现金","account_icon":"\/uploads\/********\/32f9a96779c80c496b2ba7b39edb37b4.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"11.00","is_join_total":1,"createtime":"2024-10-17 00:54:26","is_default":"2","is_del":1,"memo":null,"accountType":"4","isJoinTotal":1,"accountIcon":"\/uploads\/********\/32f9a96779c80c496b2ba7b39edb37b4.png","accountName":"现金现金","balance":"11.00"},{"id":36,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"3","network_type":"3","account_name":"京东账号","account_icon":"\/uploads\/********\/56855c6b5f0442b8be0bf98fc4db57d1.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"55.00","is_join_total":1,"createtime":"2024-10-17 00:51:49","is_default":"2","is_del":1,"memo":null,"accountType":"3","isJoinTotal":1,"accountIcon":"\/uploads\/********\/56855c6b5f0442b8be0bf98fc4db57d1.png","accountName":"京东账号","balance":"55.00","networkType":"3"},{"id":35,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"3","network_type":"2","account_name":"支付宝","account_icon":"\/uploads\/********\/f3a02679e6586c6f5233e902ee5db6d2.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"11.00","is_join_total":1,"createtime":"2024-10-17 00:51:29","is_default":"2","is_del":1,"memo":null,"accountType":"3","isJoinTotal":1,"accountIcon":"\/uploads\/********\/f3a02679e6586c6f5233e902ee5db6d2.png","accountName":"支付宝","balance":"11.00","networkType":"2"},{"id":34,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"3","network_type":"1","account_name":"微信","account_icon":"\/uploads\/********\/54d90ddadf6bf3ddc3e66bcb5299ad25.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"23.00","is_join_total":1,"createtime":"2024-10-17 00:51:11","is_default":"2","is_del":1,"memo":null,"accountType":"3","isJoinTotal":1,"accountIcon":"\/uploads\/********\/54d90ddadf6bf3ddc3e66bcb5299ad25.png","accountName":"微信","balance":"23.00","networkType":"1"},{"id":33,"user_id":3,"bank_id":2,"bank_name":"华夏银行","card_no":"2222","card_type":"1","network_type":null,"account_name":"建设储蓄卡账户","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"3333.00","is_join_total":1,"createtime":"2024-10-17 00:50:25","is_default":"2","is_del":1,"memo":null,"accountType":"1","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"华夏银行(2222)","bankId":2,"cardNo":"2222","balance":"3333.00"},{"id":32,"user_id":3,"bank_id":1,"bank_name":"平安银行","card_no":"*****************333..","card_type":"1","network_type":null,"account_name":"","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"10000.00","is_join_total":1,"createtime":"2024-10-16 23:13:25","is_default":"2","is_del":1,"memo":null,"accountType":"1","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"平安银行(33..)","bankId":1,"cardNo":"*****************333..","balance":"10000.00"},{"id":31,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"7","network_type":null,"account_name":"李四","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":2,"donated_name":"李四","be_donated_name":null,"donated_price":"50.00","property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"50.00","is_join_total":1,"createtime":"2024-10-16 22:57:38","is_default":"2","is_del":1,"memo":"","accountType":"7","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","donateType":2,"name":"李四","accountName":"李四"},{"id":30,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"7","network_type":null,"account_name":"李四","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":1,"donated_name":null,"be_donated_name":"李四","donated_price":"50.00","property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"50.00","is_join_total":1,"createtime":"2024-10-16 22:57:33","is_default":"2","is_del":1,"memo":"","accountType":"7","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","donateType":1,"name":"李四","accountName":"李四"},{"id":29,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"7","network_type":null,"account_name":"李四","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":0,"donated_name":"李四","be_donated_name":null,"donated_price":"50.00","property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"50.00","is_join_total":1,"createtime":"2024-10-16 22:56:36","is_default":"2","is_del":1,"memo":"","accountType":"7","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","donateType":0,"name":"李四","accountName":"李四"},{"id":28,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"6","network_type":null,"account_name":"张三","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":2,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":"10000.00","amount_income_date":"2024-09-09","amount_income_balance":"30","donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"10000.00","is_join_total":1,"createtime":"2024-10-16 22:47:56","is_default":"2","is_del":1,"memo":"我借张三10000","accountType":"6","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"张三","date":"2024-09-09","balance":"30"},{"id":27,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"6","network_type":null,"account_name":"","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":2,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":"10000.00","amount_income_date":"2024-09-09","amount_income_balance":"30","donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"10000.00","is_join_total":1,"createtime":"2024-10-16 22:46:22","is_default":"2","is_del":1,"memo":"我借张三10000","accountType":"6","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"","date":"2024-09-09","balance":"30"},{"id":26,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"6","network_type":null,"account_name":"","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":1,"amount_due_name":null,"amount_due_price":"10000","amount_due_date":"2024-09-09","amount_due_balance":"30","amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"10000","is_join_total":1,"createtime":"2024-10-16 22:45:24","is_default":"2","is_del":1,"memo":"张三借我10000","accountType":"6","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"","date":"2024-09-09","balance":"30"},{"id":25,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"5","network_type":null,"account_name":"股票2","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":"20","loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"100.00","is_join_total":1,"createtime":"2024-10-16 22:35:51","is_default":"2","is_del":1,"memo":"涨了涨了","accountType":"5","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"股票2","balance":"100.00","accountIncome":"20"},{"id":24,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"4","network_type":null,"account_name":"现金账户名称","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"1000.00","is_join_total":1,"createtime":"2024-10-16 22:33:59","is_default":"2","is_del":1,"memo":null,"accountType":"4","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"现金账户名称","balance":"1000.00"},{"id":23,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"3","network_type":"3","account_name":"京东账户","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"100.00","is_join_total":1,"createtime":"2024-10-16 22:33:07","is_default":"2","is_del":1,"memo":null,"accountType":"3","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"京东账户","balance":"100.00","networkType":"3"},{"id":22,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"3","network_type":"2","account_name":"支付宝","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"100.00","is_join_total":1,"createtime":"2024-10-16 22:32:46","is_default":"2","is_del":1,"memo":null,"accountType":"3","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"支付宝","balance":"100.00","networkType":"2"},{"id":21,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"3","network_type":"1","account_name":"微信","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"100.00","is_join_total":1,"createtime":"2024-10-16 22:32:39","is_default":"2","is_del":1,"memo":null,"accountType":"3","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"微信","balance":"100.00","networkType":"1"},{"id":20,"user_id":3,"bank_id":1,"bank_name":"平安银行","card_no":"*****************","card_type":"1","network_type":null,"account_name":"","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"10000.00","is_join_total":1,"createtime":"2024-10-16 22:28:04","is_default":"2","is_del":1,"memo":null,"accountType":"1","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"平安银行(6562)","bankId":1,"cardNo":"*****************","balance":"10000.00"},{"id":14,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"5","network_type":null,"account_name":"的地方","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":"222","loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"12.00","is_join_total":1,"createtime":"2024-10-15 04:57:09","is_default":"2","is_del":1,"memo":"aaa","accountType":"5","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"的地方","balance":"12.00","accountIncome":"222"},{"id":13,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"4","network_type":null,"account_name":"用英语","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"333.00","is_join_total":1,"createtime":"2024-10-15 04:39:45","is_default":"2","is_del":1,"memo":null,"accountType":"4","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"用英语","balance":"333.00"},{"id":12,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"3","network_type":"3","account_name":"bbb","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"22.00","is_join_total":1,"createtime":"2024-10-15 04:34:05","is_default":"2","is_del":1,"memo":null,"accountType":"3","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"bbb","balance":"22.00","networkType":"3"},{"id":11,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"3","network_type":"1","account_name":"微信","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"1.00","is_join_total":1,"createtime":"2024-10-15 04:32:58","is_default":"2","is_del":1,"memo":null,"accountType":"3","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"微信","balance":"1.00","networkType":"1"},{"id":10,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"6","network_type":null,"account_name":"","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":"100","amount_due_date":"2024-09-09","amount_due_balance":"30","amount_income_name":null,"amount_income_price":"0.00","amount_income_date":"","amount_income_balance":"","donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"0.00","is_join_total":1,"createtime":"2024-10-14 23:00:32","is_default":"2","is_del":1,"memo":"张三借我100","accountType":"6","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"","date":"","balance":""},{"id":9,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"5","network_type":null,"account_name":"股票","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":"20","loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"100.00","is_join_total":1,"createtime":"2024-10-14 22:59:17","is_default":"2","is_del":1,"memo":"涨了涨了","accountType":"5","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"股票","balance":"100.00","accountIncome":"20"},{"id":8,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"4","network_type":null,"account_name":"现金账户名称","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"100.00","is_join_total":1,"createtime":"2024-10-14 22:58:19","is_default":"2","is_del":1,"memo":null,"accountType":"4","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"现金账户名称","balance":"100.00"},{"id":7,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"3","network_type":"1","account_name":"微信1111账户","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"100.00","is_join_total":1,"createtime":"2024-10-14 22:56:37","is_default":"2","is_del":1,"memo":null,"accountType":"3","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"微信1111账户","balance":"100.00","networkType":"1"},{"id":6,"user_id":3,"bank_id":null,"bank_name":"建设银行","card_no":"*****************","card_type":"1","network_type":null,"account_name":"建设银行","account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"10000.00","is_join_total":1,"createtime":"2024-10-14 22:35:29","is_default":"2","is_del":1,"memo":null,"accountType":"1","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"建设银行(6566)","bankId":null,"cardNo":"*****************","balance":"10000.00"},{"id":5,"user_id":3,"bank_id":null,"bank_name":"","card_no":"","card_type":"8","network_type":null,"account_name":null,"account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":"100","property_price":"79","property_type":"2","property_proportion":"1%","quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":null,"exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":null,"is_join_total":1,"createtime":"2024-08-12 14:00:01","is_default":"2","is_del":1,"memo":null,"accountType":"8","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":null,"propertyPrice":"79","propertyType":"2","propertyProportion":"1%"},{"id":1,"user_id":3,"bank_id":null,"bank_name":"建设银行","card_no":"*****************","card_type":"1","network_type":null,"account_name":null,"account_icon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","account_income":null,"loan_type":0,"amount_due_name":null,"amount_due_price":null,"amount_due_date":null,"amount_due_balance":null,"amount_income_name":null,"amount_income_price":null,"amount_income_date":null,"amount_income_balance":null,"donate_type":null,"donated_name":null,"be_donated_name":null,"donated_price":null,"property_name":null,"property_price":null,"property_type":null,"property_proportion":null,"quota":null,"bill_date":null,"repayment_date":null,"issued_bill":null,"noissued_bill":null,"available_amount":null,"consumption_amount":"100","exemption_conditions":null,"promotion_name":null,"promotion_conditions":null,"annual_status":null,"annual_fee":null,"annual_price":null,"price":"11679.00","is_join_total":1,"createtime":"2024-08-09 15:56:02","is_default":"1","is_del":1,"memo":null,"accountType":"1","isJoinTotal":1,"accountIcon":"\/uploads\/********\/55680714acdfe66a89c4785f011c8c3b.png","accountName":"建设银行(6565)","bankId":null,"cardNo":"*****************","balance":"11679.00"}],"serial_number":"****************"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | - | String | - |
| time | ********** | String | 优惠活动获取区间 |
| data | - | Array | - |
| data.id | 50 | Number | - |
| data.user_id | 3 | Number | 用户ID |
| data.bank_id | - | Null | 银行ID |
| data.bank_name | - | String | 银行名称 |
| data.card_no | - | String | 卡号 |
| data.card_type | 6 | String | 卡类型:1=储蓄卡,2=信用卡,3=网络账户,4=现金账户,5=投资账户,6=信贷往来,7=捐赠账户,8=大笔资产 |
| data.network_type | - | Null | 网络账户类型:1=微信,2=支付宝,3=其它 |
| data.account_name | 借给王五 | String | 账户名称 |
| data.account_icon | /uploads/********/968f307420e41fb9cf5a5cbb48123254.png | String | 账户图标 |
| data.account_income | - | Null | 收益(只针对投资账户有值) |
| data.loan_type | 2 | Number | 类型:1=向谁借,2=借给谁(针对借贷账户) |
| data.amount_due_name | - | Null | 向谁借入 |
| data.amount_due_price | - | Null | 借入金额(只针对信贷账户有值) |
| data.amount_due_date | - | Null | 还款日(只针对信贷账户) |
| data.amount_due_balance | - | Null | 还款余额(只针对信贷账户) |
| data.amount_income_name | - | Null | 借给谁 |
| data.amount_income_price | 5.00 | String | 借出金额(只针对信贷账户) |
| data.amount_income_date | 2024-10-17 | String | 收回日(只针对信贷账户) |
| data.amount_income_balance | 5 | String | 收回余额(只针对信贷账户) |
| data.donate_type | - | Null | 捐赠类型:1=捐赠给他人,2=被捐赠 |
| data.donated_name | - | Null | 捐赠人姓名(只针对捐赠账户) |
| data.be_donated_name | - | Null | 被捐赠人姓名(只针对捐赠账户) |
| data.donated_price | - | Null | 捐赠金额(只针对捐赠账户) |
| data.property_name | - | Null | 大笔资产名称(只针对大笔资产账户) |
| data.property_price | - | Null | 大笔资产金额(只针对大笔资产账户) |
| data.property_type | - | Null | 大笔资产变更类型:1=折旧,2=增值(只针对大笔资产 |
| data.property_proportion | - | Null | 变更比例(%)(只针对大笔资产) |
| data.quota | - | Null | 信用卡额度 |
| data.bill_date | - | Null | 账单日(只针对信用卡有值) |
| data.repayment_date | - | Null | 还款日(只针对信用卡有值) |
| data.issued_bill | - | Null | 已出账单(只针对信用卡有值) |
| data.noissued_bill | - | Null | 未出账单(只针对信用卡有值) |
| data.available_amount | - | Null | 可用金额(只针对信用卡有值) |
| data.consumption_amount | - | Null | 消费金额(只针对信用卡有值) |
| data.exemption_conditions | - | Null | time:1=日,2=周,3=月,4=年type为1必填) |
| data.promotion_name | - | Null | 优惠名称(只针对信用卡有值) |
| data.promotion_conditions | - | Null | 优惠活动: (只针对信用卡有值)
name:优惠名称
type:1=刷卡次数,2=消费金额,3=消费金额达标次数
price:金额 (type为2或3时必填)
count:次数(type为1,3时必填)
time:1=日,2=周,3=月,4=年(type为1时必填) |
| data.annual_status | - | Null | 年费状态:1=免年费,2=不免年费(只针对信用卡有值) |
| data.annual_fee | - | Null | 年费日(只针对信用卡有值) |
| data.annual_price | - | Null | 年费金额(只针对信用卡有值) |
| data.price | 5.00 | String | 资产额度 |
| data.is_join_total | 1 | Number | 是否计入总资产:1=是,2=否 |
| data.createtime | 2024-10-18 03:03:54 | String | - |
| data.is_default | 2 | String | - |
| data.is_del | 1 | Number | - |
| data.memo | - | String | - |
| data.accountType | 6 | String | 账户类型 |
| data.isJoinTotal | 1 | Number | - |
| data.accountIcon | /uploads/********/968f307420e41fb9cf5a5cbb48123254.png | String | 账户 |
| data.accountName | 借给王五 | String | 账户 |
| data.date | 2024-10-17 | String | 日期 |
| data.balance | 5 | String | 余额 |
| data.exemption_conditions.type | 1 | String | 年费条件类型 |
| data.exemption_conditions.count | 5 | String | 年费条件次数 |
| data.exemption_conditions.time | 1 | String | 年费条件 年月日 |
| data.exemption_conditions.price | - | String | 年费条件金额 |
| data.promotion_conditions.name | 11@@ | String | 优惠活动名称 |
| data.promotion_conditions.type | 1 | String | 优惠 |
| data.promotion_conditions.price | - | String | 优惠 |
| data.promotion_conditions.count | 3 | String | 优惠活动获取次数 |
| data.promotion_conditions.time | 2 | String | 优惠活动获取区间 |
| data.bankId | 2 | Number | - |
| data.cardNo | 6666 | String | - |
| data.networkType | 1 | String | - |
| data.donateType | 1 | Number | - |
| data.name | 捐给张三 | String | - |
| data.accountIncome | 200 | String | - |
| data.propertyPrice | 79 | String | - |
| data.propertyType | 2 | String | - |
| data.propertyProportion | 1% | String | - |
| serial_number | **************** | String | - |

* 失败(404)

```javascript
No data
```

## 银行列表

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-14 21:14:18

> Update Time: 2024-10-14 21:22:08

```text
No description
```

**API Status**

> Developing

**URL**

> api/card/bankList

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 账户详情

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-16 21:28:27

> Update Time: 2024-10-18 23:34:22

```text
No description
```

**API Status**

> Developing

**URL**

> api/card/accountDetail

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> json

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

```javascript
No data
```

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 删除账户

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-17 21:19:31

> Update Time: 2024-10-17 21:30:10

```text
No description
```

**API Status**

> Developing

**URL**

> api/card/delCard

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| accountId | 48 | String | Yes | 账户ID |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 修改账户计入总资产

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-21 11:29:23

> Update Time: 2024-10-21 11:32:31

```text
No description
```

**API Status**

> Developing

**URL**

> api/card/updAccountIsJoinTotal

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| accountId | 1 | String | Yes | 账户ID |
| action | 2 | String | Yes | 1=计入,2=不计入 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 修改账户金额

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-21 11:29:31

> Update Time: 2024-10-21 15:36:46

```text
No description
```

**API Status**

> Developing

**URL**

> api/card/updAccountPrice

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| amount | 100 | String | Yes | 金额 |
| accountId | 4 | String | Yes | 账户ID |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

# 图表页面

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-19 14:04:39

> Update Time: 2024-08-19 14:04:39

```text
No description
```

**Folder Param Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Query**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Auth**

> Inherit auth from parent

## 首页

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-19 14:05:12

> Update Time: 2024-10-22 20:26:48

```text
No description
```

**API Status**

> Developing

**URL**

> api/chart/index

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> json

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

```javascript
{
    "bookkeepingNumber": "1yjke01i71zksm91vqh,2h2sl02p13mzpvn5hb6,ryx1n0156fxhd0j5zjh,thhhb01k1pp9h9tu28c,tjb6002y11m9ym4sydy,uthy002q12fy31do4xe",
    "type": "1",
    "timeInterval": "2024-10-22,2024-10-22",
    "token": "4a0d7191-be7e-4357-818a-14c4f9c96803"
}
```

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 收入来源&&支出分布

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-19 15:24:34

> Update Time: 2024-10-22 21:46:59

```text
No description
```

**API Status**

> Developing

**URL**

> api/chart/incomeAndPayment

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> json

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

```javascript
{
    "bookkeepingNumber": "1yjke01i71zksm91vqh,2h2sl02p13mzpvn5hb6,ryx1n0156fxhd0j5zjh,thhhb01k1pp9h9tu28c,tjb6002y11m9ym4sydy,uthy002q12fy31do4xe",
    "type": "3",
    "timeInterval": "2024-10-01,2024-10-31",
    "token": "4a0d7191-be7e-4357-818a-14c4f9c96803"
}
```

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"","time":"1724053397","data":{"incomeItems":{"total":600,"items":[{"categoryName":"买手机","money":600,"icon":"","categoryIcon":"http:\/\/bookkeeping.sudada123.cn\/uploads\/dd7093143fe4fa50355e772c252d85fd.jpg","proportion":"1.00"}]},"expenditureItems":{"total":0,"items":0}},"serial_number":"2024081953100491"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | - | String | - |
| time | 1724053397 | String | - |
| data | - | Object | - |
| data.incomeItems | - | Object | - |
| data.incomeItems.total | 600 | Number | 收入总金额 |
| data.incomeItems.items | - | Array | - |
| data.incomeItems.items.categoryName | 买手机 | String | 收入分类名 |
| data.incomeItems.items.money | 600 | Number | 收入金额 |
| data.incomeItems.items.icon | - | String | - |
| data.incomeItems.items.categoryIcon | http://bookkeeping.sudada123.cn/uploads/dd7093143fe4fa50355e772c252d85fd.jpg | String | 分类图标 |
| data.incomeItems.items.proportion | 1.00 | String | 所占比例 |
| data.expenditureItems | - | Object | - |
| data.expenditureItems.total | 0 | Number | 支出总金额 |
| data.expenditureItems.items | 0 | Number | - |
| serial_number | 2024081953100491 | String | - |

* 失败(404)

```javascript
No data
```

## 资产走势

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-09-20 21:27:54

> Update Time: 2024-10-22 20:32:41

```text
No description
```

**API Status**

> Developing

**URL**

> api/chart/propertyWalk

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> json

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

```javascript
{
    "type": "1",
    "timeInterval": "2024-10-22,2024-10-22",
    "token": "4a0d7191-be7e-4357-818a-14c4f9c96803"
}
```

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 资产汇总

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-19 16:09:41

> Update Time: 2024-10-22 22:00:20

```text
No description
```

**API Status**

> Developing

**URL**

> api/chart/assetSummary

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> json

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

```javascript
{
    "type": null,
    "timeInterval": null,
    "token": "4a0d7191-be7e-4357-818a-14c4f9c96803"
}
```

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 负债汇总

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-19 16:23:36

> Update Time: 2024-10-21 09:25:46

```text
No description
```

**API Status**

> Developing

**URL**

> api/chart/liabilitiesSummary

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"","time":"1724074076","data":{"total":2100,"consumptionAmount":2100,"amountDueBalance":0},"serial_number":"2024081999495799"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | - | String | - |
| time | 1724074076 | String | - |
| data | - | Object | - |
| data.total | 2100 | Number | 总额 |
| data.consumptionAmount | 2100 | Number | 信用卡欠额 |
| data.amountDueBalance | 0 | Number | 借款金额 |
| serial_number | 2024081999495799 | String | - |

* 失败(404)

```javascript
No data
```

## 节省金额&&非必要金额

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-09-20 23:04:22

> Update Time: 2024-10-20 23:42:17

```text
No description
```

**API Status**

> Developing

**URL**

> api/chart/saveMoney

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | {{LoginToken}} | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| bookkeepingNumber | ryx1n0156fxhd0j5zjh,thhhb01k1pp9h9tu28c | String | Yes | - |
| type | 2 | String | Yes | 1周 2月 3年 4日 |
| timeInterval | 2024-08-19,2024-08-25 | String | Yes | 时间区间（与上方type不可同时存在） |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

# 计划

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-19 21:30:31

> Update Time: 2024-08-19 21:30:31

```text
No description
```

**Folder Param Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Query**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Auth**

> Inherit auth from parent

## 新增计划

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-20 13:59:03

> Update Time: 2024-10-14 17:16:22

```text
No description
```

**API Status**

> Developing

**URL**

> api/plan/addPlan

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> json

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

```javascript
{
    
    "name": "计划任务A1",
    "planBackgroundImage": "/uploads/********/67ee6902e31ef86747d0a138b72e417d.jpg",
    "planIcon": "/uploads/20240903/d11cb2755cc37270667c7726884e4fe8.png",
    "planRequiredAmount": "10000.00",
    "planAccumulatedAmount": "255555.00",
    "planStartDate": "2024-08-20",
    "planExpectedAchieveDate": "2024-09-20",
    "planStorageMethod": "2",
    "bookkeepingNumbers": "1yjke01i71zksm91vqh,ryx1n0156fxhd0j5zjh,thhhb01k1pp9h9tu28c",
    "planStorageRules": "{\"cycle\":\"1\",\"times\":\"111\",\"price\":\"100\",\"proportion\":\"0\"}",
    "memo": "5",
    "status": "2",
    "token": "4a0d7191-be7e-4357-818a-14c4f9c96803"
}
```

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 计划手动存储

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-21 11:56:31

> Update Time: 2024-08-21 13:24:54

```text
No description
```

**API Status**

> Developing

**URL**

> api/plan/manualSaveTask

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | {{LoginToken}} | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| planId | 1 | String | Yes | 计划ID |
| price | 222 | String | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 计划列表

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-21 13:24:14

> Update Time: 2024-10-13 22:56:00

```text
No description
```

**API Status**

> Developing

**URL**

> api/plan/planlist

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"获取成功","time":"1725153683","data":[{"id":1,"name":"计划任务A","backgroundImage":"\/assets\/img\/avatar.png","icon":"\/assets\/img\/avatar.png","todayDay":30,"residueDay":19,"nowDay":11,"proportion":"0","accumulatedAmount":"0.00","requiredAmount":"10000.00","status":1,"storageMethod":"1"},{"id":3,"name":"计划任务B","backgroundImage":"\/assets\/img\/avatar.png","icon":"\/assets\/img\/avatar.png","todayDay":30,"residueDay":19,"nowDay":11,"proportion":"0","accumulatedAmount":"0.00","requiredAmount":"10000.00","status":1,"storageMethod":"2"}],"serial_number":"2024090151979856"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | 获取成功 | String | - |
| time | 1725153683 | String | - |
| data | - | Array | - |
| data.id | 1 | Number | 计划ID |
| data.name | 计划任务A | String | 计划名称 |
| data.backgroundImage | /assets/img/avatar.png | String | 计划背景图 |
| data.icon | /assets/img/avatar.png | String | 计划图标 |
| data.todayDay | 30 | Number | 总天数 |
| data.residueDay | 19 | Number | 剩余天数 |
| data.nowDay | 11 | Number | 当前天数 |
| data.proportion | 0 | String | 比例 |
| data.accumulatedAmount | 0.00 | String | 已攒金额 |
| data.requiredAmount | 10000.00 | String | 需满金额 |
| data.status | 1 | Number | 是否实现:1=未实现,2=已实现 |
| data.storageMethod | 1 | String | 存储方式:1=手动存入,2=自动存入,3=收入存入,4=节省存入 |
| serial_number | 2024090151979856 | String | - |

* 失败(404)

```javascript
No data
```

## 计划详情

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-21 14:33:47

> Update Time: 2024-10-14 17:16:57

```text
No description
```

**API Status**

> Developing

**URL**

> api/plan/planDetail

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| planId | 4 | String | Yes | 计划ID |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"","time":"**********","data":{"id":3,"name":"计划任务B","backgroundImage":"\/assets\/img\/avatar.png","icon":"\/assets\/img\/avatar.png","requiredAmount":"10000.00","accumulatedAmount":"0.00","startDate":"2024-08-20","expectedAchieveDate":"2024-09-20","storageMethod":"2","storageRules":{"cycle":"day","times":"1","price":"100","proportion":"0"},"planPrice":"100","memo":"","accountBookkeepingNumberArr":[{"bookkeepingNumber":"thhhb01k1pp9h9tu28c","accountBookName":"测试账本"}]},"serial_number":"****************"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | - | String | - |
| time | ********** | String | - |
| data | - | Object | - |
| data.id | 3 | Number | - |
| data.name | 计划任务B | String | 计划名称 |
| data.backgroundImage | /assets/img/avatar.png | String | 计划背景图 |
| data.icon | /assets/img/avatar.png | String | 计划图标 |
| data.requiredAmount | 10000.00 | String | 需满金额 |
| data.accumulatedAmount | 0.00 | String | 已攒金额 |
| data.startDate | 2024-08-20 | String | 开始日期 |
| data.expectedAchieveDate | 2024-09-20 | String | 预计达成日期 |
| data.storageMethod | 2 | String | 1=手动存入,2=自动存入,3=收入存入,4=节省存入 |
| data.storageRules | - | Object | - |
| data.storageRules.cycle | day | String | 周期(day,week,month,year), |
| data.storageRules.times | 1 | String | 次数 |
| data.storageRules.price | 100 | String | 金额 |
| data.storageRules.proportion | 0 | String | 比例 |
| data.planPrice | 100 | String | 计划金额 |
| data.memo | - | String | 计划说明 |
| data.accountBookkeepingNumberArr | - | Array | - |
| data.accountBookkeepingNumberArr.bookkeepingNumber | thhhb01k1pp9h9tu28c | String | 账本编号 |
| data.accountBookkeepingNumberArr.accountBookName | 测试账本 | String | 账本名称 |
| serial_number | **************** | String | - |

* 失败(404)

```javascript
No data
```

## 修改计划回显(已废弃)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-11 20:29:44

> Update Time: 2024-10-11 22:31:49

```text
No description
```

**API Status**

> Developing

**URL**

> api/plan/editPlanPage

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| planId | 3 | String | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"","time":"**********","data":{"id":3,"name":"计划任务B","backgroundImage":"\/assets\/img\/avatar.png","icon":"\/assets\/img\/avatar.png","requiredAmount":"10000.00","accumulatedAmount":"0.00","startDate":"2024-08-20","expectedAchieveDate":"2024-09-20","storageMethod":"2","storageRules":{"cycle":"day","times":"1","price":"100"},"planPrice":"100","memo":"","accountBookkeepingNumberArr":[{"bookkeepingNumber":"thhhb01k1pp9h9tu28c","accountBookName":"测试账本"}]},"serial_number":"****************"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | - | String | - |
| time | ********** | String | - |
| data | - | Object | - |
| data.id | 3 | Number | - |
| data.name | 计划任务B | String | 计划编号 |
| data.backgroundImage | /assets/img/avatar.png | String | 计划背景图 |
| data.icon | /assets/img/avatar.png | String | 计划图标 |
| data.requiredAmount | 10000.00 | String | 计划所需金额 |
| data.accumulatedAmount | 0.00 | String | 计划已攒金额 |
| data.startDate | 2024-08-20 | String | 开始日期 |
| data.expectedAchieveDate | 2024-09-20 | String | 预计达成日期 |
| data.storageMethod | 2 | String | 存储方式:1=手动存入,2=自动存入,3=收入存入,4=节省存入 |
| data.storageRules | - | Object | - |
| data.storageRules.cycle | day | String | 周期 day=日,week=周，month=月，year=年 |
| data.storageRules.times | 1 | String | 次数 |
| data.storageRules.price | 100 | String | 存储金额 |
| data.planPrice | 100 | String | 存储金额(只有在自动存入时页面显示该行) |
| data.memo | - | String | 备注 |
| data.accountBookkeepingNumberArr | - | Array | - |
| data.accountBookkeepingNumberArr.bookkeepingNumber | thhhb01k1pp9h9tu28c | String | 账本编号 |
| data.accountBookkeepingNumberArr.accountBookName | 测试账本 | String | 账本名称 |
| serial_number | **************** | String | - |

* 失败(404)

```javascript
No data
```

# 个人中心

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-22 13:33:33

> Update Time: 2024-08-22 13:34:18

```text
No description
```

**Folder Param Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Query**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Auth**

> Inherit auth from parent

## 我的

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-22 13:34:28

> Update Time: 2024-10-25 16:39:22

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/info

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 21fd557a-0eff-42cf-ba92-c0e76f9706b5 | String | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"","time":"**********","data":{"vipMemo":"会员过期天数:17天","accountingDays":20,"saveMoney":0,"necessaryMoney":0,"accountingCount":7,"userInfo":{"id":3,"vip":"2","avatar":"","username":"怀羽宁安","bio":"","mobile":"***********","appOpenId":"1234567","lastIndexBookkeepingNumber":null,"lastStatisticsBookkeepingNumber":null},"lastIndexBookkeepingNumber":null,"lastStaticisticsBookkeepingNumberArr":[]},"serial_number":"****************"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | - | String | - |
| time | ********** | String | - |
| data | - | Object | - |
| data.vipMemo | 会员过期天数:17天 | String | 会员备注 |
| data.accountingDays | 20 | Number | 记账日 |
| data.saveMoney | 0 | Number | 节省金额 |
| data.necessaryMoney | 0 | Number | 非必要金额 |
| data.accountingCount | 7 | Number | 记账总笔数 |
| data.userInfo | - | Object | - |
| data.userInfo.id | 3 | Number | 用户ID |
| data.userInfo.vip | 2 | String | 会员等级:0=普通会员,1=月会员,2=年会员,3=永久会员 |
| data.userInfo.avatar | - | String | 头像 |
| data.userInfo.username | 怀羽宁安 | String | 昵称 |
| data.userInfo.bio | - | String | 简介 |
| data.userInfo.mobile | *********** | String | - |
| data.userInfo.appOpenId | 1234567 | String | - |
| data.userInfo.lastIndexBookkeepingNumber | - | Null | 上次首页所选账本 |
| data.userInfo.lastStatisticsBookkeepingNumber | - | Null | 上次统计所选账本 |
| data.lastIndexBookkeepingNumber | - | Null | - |
| data.lastStaticisticsBookkeepingNumberArr | - | Array | - |
| serial_number | **************** | String | - |

* 失败(404)

```javascript
No data
```

## 意见反馈

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-23 12:10:18

> Update Time: 2024-08-23 12:10:18

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/feedback

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | {{LoginToken}} | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| content | - | String | Yes | 反馈内容 |
| image | - | String | Yes | 图文地址(多文件以逗号拼接) |
| mobile | - | String | Yes | 非必填 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 修改个人信息

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-23 12:10:37

> Update Time: 2024-08-26 11:03:54

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/profile

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| userName | - | String | Yes | 用户名 |
| bio | - | String | Yes | 简介 |
| avatar | - | String | Yes | 头像 |
| accountMemo | - | String | Yes | 记账提醒语 |
| accountMemoTime | - | String | Yes | 记账提醒时间 |
| indexModule | - | String | Yes | 首页显示资产模块:1-8对应账户类型,9总资产,10净资产,11总负债 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 绑定微信

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-23 13:21:55

> Update Time: 2024-08-23 13:21:55

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/bindWechat

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | {{LoginToken}} | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| appCode | - | String | Yes | 微信code |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 系统信息

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-26 11:13:55

> Update Time: 2024-08-26 11:15:02

```text
No description
```

**API Status**

> Developing

**URL**

> api/index/getSystemInfo

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"请求成功","time":"1724642032","data":{"systemLogo":"","aboutUs":"","customerService":"","privacy":"","userAgreement":""},"serial_number":"2024082648489948"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | 请求成功 | String | - |
| time | 1724642032 | String | - |
| data | - | Object | - |
| data.systemLogo | - | String | 系统logo |
| data.aboutUs | - | String | 关于我们 |
| data.customerService | - | String | 电话客服 |
| data.privacy | - | String | 隐私政策 |
| data.userAgreement | - | String | 服务协议 |
| serial_number | 2024082648489948 | String | - |

* 失败(404)

```javascript
No data
```

## 资产

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-26 11:20:39

> Update Time: 2024-08-26 11:51:22

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/assets

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | {{LoginToken}} | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| type | 1 | String | Yes | 1总资产 2净资产 (默认为总资产) |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 还款日记

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-26 13:45:55

> Update Time: 2024-10-24 13:12:19

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/repaymentLog

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 21fd557a-0eff-42cf-ba92-c0e76f9706b5 | String | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{"code":1,"msg":"","time":"**********","data":[{"id":4,"bankName":"中信银行","cardNo":"********","issuedBill":"11","repaymentDate":"10-10","accountIcon":"\/uploads\/********\/0a1e2f5835636bbec6cef2a5053bf7d0.png","bankIcon":"\/uploads\/********\/275a1575622e7b48233b8954c93422db.png","bankColour":"AA902323","residue":11,"overdue":"1"},{"id":5,"bankName":"中信银行","cardNo":"2222","issuedBill":"11","repaymentDate":"10-10","accountIcon":"\/uploads\/********\/0a1e2f5835636bbec6cef2a5053bf7d0.png","bankIcon":"\/uploads\/********\/275a1575622e7b48233b8954c93422db.png","bankColour":"AA902323","residue":11,"overdue":"1"},{"id":6,"bankName":"中信银行","cardNo":"2222","issuedBill":"11","repaymentDate":"10-10","accountIcon":"\/uploads\/********\/0a1e2f5835636bbec6cef2a5053bf7d0.png","bankIcon":"\/uploads\/********\/275a1575622e7b48233b8954c93422db.png","bankColour":"AA902323","residue":11,"overdue":"1"},{"id":7,"bankName":"中信银行","cardNo":"2222","issuedBill":"11","repaymentDate":"10-10","accountIcon":"\/uploads\/********\/0a1e2f5835636bbec6cef2a5053bf7d0.png","bankIcon":"\/uploads\/********\/275a1575622e7b48233b8954c93422db.png","bankColour":"AA902323","residue":11,"overdue":"1"},{"id":8,"bankName":"华夏银行","cardNo":"2222","issuedBill":"11","repaymentDate":"10-16","accountIcon":"\/uploads\/********\/0a1e2f5835636bbec6cef2a5053bf7d0.png","bankIcon":"\/uploads\/********\/6a64beda2e826520214ed395dd34014c.png","bankColour":"AA902323","residue":5,"overdue":"1"},{"id":21,"bankName":"平安银行","cardNo":"1111","issuedBill":"3333","repaymentDate":"10-03","accountIcon":"\/uploads\/********\/0a1e2f5835636bbec6cef2a5053bf7d0.png","bankIcon":"\/uploads\/********\/269b6b6dba86f86a1fba33fb1af89a9b.png","bankColour":"AA902323","residue":18,"overdue":"1"}],"serial_number":"****************"}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | - | String | - |
| time | ********** | String | - |
| data | - | Array | - |
| data.id | 4 | Number | 账户ID |
| data.bankName | 中信银行 | String | 银行名称 |
| data.cardNo | ******** | String | 卡号 |
| data.issuedBill | 11 | String | 已出账单金额 |
| data.repaymentDate | 10-10 | String | 还款日 |
| data.accountIcon | /uploads/********/0a1e2f5835636bbec6cef2a5053bf7d0.png | String | 账户图标 |
| data.bankIcon | /uploads/********/275a1575622e7b48233b8954c93422db.png | String | 银行图标 |
| data.bankColour | AA902323 | String | 银行颜色 |
| data.residue | 11 | Number | 距还款日天数/已逾期天数 |
| data.overdue | 1 | String | 逾期状态：1=已逾期，2=未预期 |
| serial_number | **************** | String | - |

* 失败(404)

```javascript
No data
```

## 已还/未还 (快速切换)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-28 13:42:53

> Update Time: 2024-10-28 13:44:07

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/editChangeStatus

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| accountId | - | String | Yes | 账户ID |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 消费指南

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-21 15:33:52

> Update Time: 2024-10-23 14:20:24

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/consumerGuide

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 4a0d7191-be7e-4357-818a-14c4f9c96803 | String | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 已使用/未使用 (快速切换)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-28 13:43:14

> Update Time: 2024-10-28 13:44:12

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/editChangeStatus

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| accountId | - | String | Yes | 账户ID |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 羊毛助手

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-26 15:16:48

> Update Time: 2024-08-27 14:10:41

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/woolHelper

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | {{LoginToken}} | String | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 年费计划

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-26 15:17:09

> Update Time: 2024-10-25 14:47:02

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/annualPlan

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | 21fd557a-0eff-42cf-ba92-c0e76f9706b5 | String | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
{
	"code": 79,
	"msg": "",
	"time": "**********",
	"data": [
		{
			"id": 98,
			"accountName": null,
			"bankName": "中信银行",
			"annualFee": "08-23",
			"annualStatus": "2",
			"annualPrice": "300.00",
			"days": 21,
			"priceCount": "6",
			"priceTotal": "1000",
			"annualFeeType": 34,
			"spendPriceTotal": 87,
			"spendPriceCount": 31
		}
	],
	"serial_number": "****************"
}
```

| Key | Example Value | Type | Description |
| --- | ------------- | ---- | ----------- |
| code | 1 | Number | - |
| msg | - | String | - |
| time | ********** | String | - |
| data | - | Array | - |
| data.id | 98 | Number | 账户ID |
| data.accountName | - | Null | 账户名称 |
| data.bankName | 中信银行 | String | 银行名称 |
| data.annualFee | 08-23 | String | 年费日 |
| data.annualStatus | 2 | String | 年费状态 1=免年费,2=不免年费 |
| data.annualPrice | 300.00 | String | 年费价格 |
| data.days | 21 | Number | 距年费收款剩余天数 |
| data.priceCount | 6 | String | 免年费条件(消费总次数) |
| data.priceTotal | 1000 | String | 免年费条件(消费总金额) |
| data.annualFeeType | 1 | Number | 年费类型 1=刷卡次数,2=消费金额 |
| data.spendPriceTotal | 87 | Number | 已花费金额 |
| data.spendPriceCount | 31 | Number | 已花费次数 |
| serial_number | **************** | String | - |

* 失败(404)

```javascript
No data
```

## 手动设置免年费

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-27 14:09:55

> Update Time: 2024-08-27 14:10:37

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/setAnnualStatus

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | {{LoginToken}} | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| cardId | - | String | Yes | 账户ID |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 讨账指南

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-26 15:17:35

> Update Time: 2024-08-26 16:33:20

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/collectionGuide

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | {{LoginToken}} | String | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 添加讨账记录

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-27 10:42:43

> Update Time: 2024-08-27 11:43:08

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/collectionGuideUpdate

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| token | {{LoginToken}} | String | Yes | - |

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| cardId | - | String | Yes | 账户ID |
| price | - | String | Yes | 价格 |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 节省金额

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-19 22:13:23

> Update Time: 2024-10-19 22:17:55

```text
No description
```

**API Status**

> Developing

**URL**

> api/detail/searchFlowingWater

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> form-data

**Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| isSave | - | String | Yes | - |

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 非必要金额

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-19 22:13:36

> Update Time: 2024-10-19 22:13:36

```text
No description
```

**API Status**

> Developing

**URL**

> api/detail/searchFlowingWater

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 注销账号

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-21 20:12:21

> Update Time: 2024-10-21 20:12:21

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/delete

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 常见问题

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-23 16:10:21

> Update Time: 2024-10-23 16:10:21

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/commonQuestion

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> urlencoded

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 关于我们

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-23 16:10:43

> Update Time: 2024-10-23 16:11:09

```text
No description
```

**API Status**

> Developing

**URL**

> api/user/aboutUs

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

# 定时任务

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-23 17:24:05

> Update Time: 2024-08-23 17:24:05

```text
No description
```

**Folder Param Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Query**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Auth**

> Inherit auth from parent

## 信用卡账单/年费(后端自动)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-23 17:24:21

> Update Time: 2024-08-23 17:38:15

```text
No description
```

**API Status**

> Developing

**URL**

> api/index/creditCardTask

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 自动写入 类型计划 定时任务(后端自用)

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-08-20 17:14:47

> Update Time: 2024-08-23 17:38:03

```text
No description
```

**API Status**

> Developing

**URL**

> api/plan/autoSaveTask

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 会员自动到期

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-09-03 13:38:43

> Update Time: 2024-09-03 14:12:24

```text
No description
```

**API Status**

> Developing

**URL**

> api/index/vipTask

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

# 文本

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-23 22:09:32

> Update Time: 2024-10-23 22:09:32

```text
No description
```

**Folder Param Headers**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Query**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Param Body**

| Key | Example Value | Type | Required | Description |
| --- | ------------- | ---- | -------- | ----------- |
| No parameters |

**Folder Auth**

> Inherit auth from parent

## 隐私政策

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-23 22:15:20

> Update Time: 2024-10-23 22:15:20

```text
No description
```

**API Status**

> Developing

**URL**

> index/index/privacy

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 服务协议

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-23 22:19:03

> Update Time: 2024-10-23 22:19:03

```text
No description
```

**API Status**

> Developing

**URL**

> index/index/userAgreement

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```

## 关于我们

> Creator: 苏大大

> Updater: 苏大大

> Created Time: 2024-10-23 22:19:32

> Update Time: 2024-10-23 22:19:32

```text
No description
```

**API Status**

> Developing

**URL**

> index/index/aboutUs

| Environment | URL |
| ----------- | --- |


**Method**

> POST

**Content-Type**

> none

**Authentication**

> Inherit auth from parent

**Response**

* 成功(200)

```javascript
No data
```

* 失败(404)

```javascript
No data
```
