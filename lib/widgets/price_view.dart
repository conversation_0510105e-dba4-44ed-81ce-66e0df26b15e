import 'package:flutter/material.dart';
import 'package:qiazhun/models/price_info.dart';

class PriceView extends StatelessWidget {
  final PriceInfo price;

  final double? integerFontSize;
  final double? fractionalFontSize;

  final Color? textColor;
  final FontWeight? fontWeight;

  final String? prefix;
  final TextStyle? prefixStyle;
  final double? prefixPadding;

  final bool? showSymbol;

  const PriceView(
      {required this.price,
      this.integerFontSize,
      this.fractionalFontSize,
      this.textColor,
      this.fontWeight,
      this.prefix,
      this.prefixStyle,
      this.prefixPadding,
      this.showSymbol = true,
      super.key});

  @override
  Widget build(BuildContext context) {
    return RichText(
        maxLines: 1,
        text: TextSpan(children: [
          if (prefix?.isNotEmpty == true) ...[
            TextSpan(text: prefix, style: prefixStyle),
            WidgetSpan(
                child: SizedBox(
              width: prefixPadding ?? 2,
            )),
          ],
          if (showSymbol == true) ...[
            TextSpan(
                text: '¥',
                style: TextStyle(color: textColor ?? Colors.white, fontSize: fractionalFontSize ?? 14, fontWeight: fontWeight ?? FontWeight.w400, height: 1.4)),
            WidgetSpan(
                child: SizedBox(
              width: 2,
            ))
          ],
          TextSpan(
              text: price.integerPart,
              style: TextStyle(color: textColor ?? Colors.white, fontSize: integerFontSize ?? 17, fontWeight: fontWeight ?? FontWeight.w400, height: 1.4)),
          TextSpan(
              text: price.priceParts[2],
              style: TextStyle(color: textColor ?? Colors.white, fontSize: fractionalFontSize ?? 14, fontWeight: fontWeight ?? FontWeight.w400, height: 1.4)),
        ]));
  }
}
