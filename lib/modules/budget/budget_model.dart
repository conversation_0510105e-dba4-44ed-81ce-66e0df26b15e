import 'package:json_annotation/json_annotation.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';

part 'budget_model.g.dart';

@JsonSerializable()
class BudgetIndexResp extends Object {
  @JsonKey(name: 'budgetList')
  List<BudgetInfo>? budgetList;

  @Json<PERSON>ey(name: 'accountList')
  List<AccountList>? accountList;

  BudgetIndexResp(
    this.budgetList,
    this.accountList,
  );

  factory BudgetIndexResp.fromJson(Map<String, dynamic> srcJson) => _$BudgetIndexRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$BudgetIndexRespToJson(this);
}

@JsonSerializable()
class AccountList extends Object {
  @JsonKey(name: 'id')
  int? id;

  @JsonKey(name: 'isJoinTotal')
  int? isJoinTotal;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'accountIcon')
  String? accountIcon;

  @Json<PERSON>ey(name: 'cardType')
  dynamic cardType;

  @JsonKey(name: 'balance')
  String? balance;

  @Json<PERSON>ey(name: 'accountName')
  String? accountName;

  @JsonKey(name: 'selectedStatus')
  int? selectedStatus;

  AccountList(
    this.id,
    this.isJoinTotal,
    this.accountIcon,
    this.cardType,
    this.balance,
    this.accountName,
    this.selectedStatus,
  );

  factory AccountList.fromJson(Map<String, dynamic> srcJson) => _$AccountListFromJson(srcJson);

  Map<String, dynamic> toJson() => _$AccountListToJson(this);
}

@JsonSerializable()
class BudgetInfo extends Object {
  @JsonKey(name: 'budgetId')
  int? budgetId;

  @JsonKey(name: 'budgetType')
  String? budgetType;

  @JsonKey(name: 'budgetAmount')
  String? budgetAmount;

  @JsonKey(name: 'spendPrice')
  String? spendPrice;

  @JsonKey(name: 'accountBookIds')
  String? accountBookIds;

  @JsonKey(name: 'startDate')
  String? startDate;

  @JsonKey(name: 'endDate')
  String? endDate;

  @JsonKey(name: 'expenditurePrice')
  String? expenditurePrice;

  @JsonKey(name: 'budgetStatus')
  int? budgetStatus;

  @JsonKey(name: 'proportion')
  String? proportion;

  @JsonKey(name: 'surplusStatus')
  int? surplusStatus;

  @JsonKey(name: 'surplus')
  String? surplus;

  @JsonKey(name: 'dayPrice')
  String? dayPrice;

  @JsonKey(name: 'bookkeepingArrInfo')
  List<BookkeepingInfo>? bookkeepingArrInfo;

  BudgetInfo(this.budgetId, this.budgetType, this.budgetAmount, this.spendPrice, this.accountBookIds, this.startDate, this.endDate, this.expenditurePrice,
      this.budgetStatus, this.proportion, this.surplusStatus, this.surplus, this.dayPrice, this.bookkeepingArrInfo);

  factory BudgetInfo.fromJson(Map<String, dynamic> srcJson) => _$BudgetInfoFromJson(srcJson);

  Map<String, dynamic> toJson() => _$BudgetInfoToJson(this);
}
