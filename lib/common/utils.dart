import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:qiazhun/common/file_repo.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/store/privacy_store.dart';
import 'package:qiazhun/tools/tools.dart';

String getImageUrl(String? path) {
  if (path?.startsWith('http') == true) {
    return path!;
  }

  return '${HttpUtil.kBaseUrl}${path}';
}

class TwoDecimalInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    final RegExp regex = RegExp(r'^\d*\.?\d{0,2}');
    String newString = regex.stringMatch(newValue.text) ?? '';
    return newString == newValue.text ? newValue : oldValue;
  }
}

class CardEndInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    final RegExp regex = RegExp(r'^\d{0,4}');
    String newString = regex.stringMatch(newValue.text) ?? '';
    return newString == newValue.text ? newValue : oldValue;
  }
}

void showUploadDialog(BuildContext context, Function(String filePath)? onUploadSuccess, Function(String? errorMsg)? onUploadFailed) {
  if (PrivacyStore.to.hasShowCameraTips()) {
    _innerShowUploadDialog(context, onUploadSuccess, onUploadFailed);
  } else {
    PrivacyStore.to.showCameraTips(
      onConfirm: () {
        _innerShowUploadDialog(context, onUploadSuccess, onUploadFailed);
      },
    );
  }
}

void _innerShowUploadDialog(BuildContext context, Function(String filePath)? onUploadSuccess, Function(String? errorMsg)? onUploadFailed) {
  final ImagePicker _picker = ImagePicker();
  showCupertinoModalPopup(
      context: context,
      builder: (context) {
        return CupertinoActionSheet(
          cancelButton: CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text('取消', style: TextStyle(fontSize: 16.0, color: MColor.xFF333333, fontWeight: FontWeight.bold)),
          ),
          actions: <Widget>[
            CupertinoActionSheetAction(
              onPressed: () {
                Navigator.of(context).pop();
                _pickImage(_picker, ImageSource.camera, onUploadSuccess, onUploadFailed);
              },
              child: Text(
                '拍照上传',
                style: TextStyle(fontSize: 16.0, color: MColor.xFF333333),
              ),
            ),
            CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.of(context).pop();
                  _pickImage(_picker, ImageSource.gallery, onUploadSuccess, onUploadFailed);
                },
                child: Text(
                  '从相册选择',
                  style: TextStyle(fontSize: 16.0, color: MColor.xFF333333),
                )),
          ],
        );
      });
}

Future<void> _pickImage(ImagePicker _picker, ImageSource source, Function(String filePath)? onUploadSuccess, Function(String? errorMsg)? onUploadFailed) async {
  final pickedFile = await _picker.pickImage(source: source, requestFullMetadata: false, imageQuality: 20);

  if (pickedFile != null) {
    try {
      var resp = await FileRepo.fileUpload(pickedFile!.path);
      if (resp.code == 1) {
        onUploadSuccess?.call(resp.data['url']);
      } else {
        onUploadFailed?.call(resp.msg);
      }
    } catch (ex, stackTrace) {
      debugPrintStack(stackTrace: stackTrace, maxFrames: 3);
      logger.e('upload file error $ex');
      onUploadFailed?.call(ex.toString());
    }
  }
}
