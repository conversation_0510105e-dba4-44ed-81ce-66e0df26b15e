import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_add_page.dart';
import 'package:qiazhun/modules/account/account_common_widgets.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class AccountAddInternetAccountSection extends StatefulWidget {
  final AccountAddController? addController;
  final AccountModel? accountModel;
  const AccountAddInternetAccountSection({this.addController, this.accountModel, super.key});

  @override
  State<StatefulWidget> createState() => _AccountAddInternetAccountState();
}

class _AccountAddInternetAccountState extends State<AccountAddInternetAccountSection> with AutomaticKeepAliveClientMixin {
  final TextEditingController _balanceController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();

  final List<String> _networkTypeStr = ['微信', '支付宝', '其他'];
  bool _inAssets = true;
  int _networkType = 1; //类型:1=微信,2=支付宝,3=其它

  @override
  void initState() {
    widget.addController?.onAction = _addAccount;
    if (widget.accountModel != null) {
      _nameController.text = widget.accountModel!.accountName ?? '';
      _balanceController.text = widget.accountModel!.balance ?? '';
      _networkType = int.tryParse(widget.accountModel!.subType ?? '') ?? 1;
      _inAssets = widget.accountModel!.isJoinTotal == 1;
    }
    super.initState();
  }

  @override
  void didUpdateWidget(covariant AccountAddInternetAccountSection oldWidget) {
    widget.addController?.onAction = _addAccount;
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _bankSection,
          const SizedBox(
            height: 14,
          ),
          _detailSection,
          const SizedBox(
            height: 14,
          ),
        ],
      ),
    );
  }

  Future<void> _addAccount() async {
    if (_balanceController.text.isEmpty) {
      showToast('请填写余额');
      return;
    }
    if (_nameController.text.isEmpty) {
      showToast('请填写账号名称');
      return;
    }

    Loading.show();
    try {
      var resp = await AccountRepo.addSavingCard(
          id: widget.accountModel?.id,
          accountType: '3',
          balance: _balanceController.text,
          isJoinTotal: _inAssets ? '1' : '2',
          accountName: _nameController.text,
          networkType: '$_networkType');
      if (resp.code == 1) {
        showToast('添加账号成功');
        RouterHelper.router.pop();
      } else {
        showToast(resp.msg ?? '添加账号失败');
      }
    } catch (e) {
      showToast('添加账号失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  Widget get _detailSection {
    return Container(
      // margin: EdgeInsets.symmetric(horizontal: 14),
      padding: EdgeInsets.symmetric(horizontal: 14, vertical: 0),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
      child: Column(
        children: [
          AccountInputTile(
            leadingText: '账户名称',
            hint: '6个字符以内，如建白金',
            maxLength: 6,
            textController: _nameController,
          ),
          Divider(
            height: 0.5,
            thickness: 0.5,
            color: MColor.xFFF5F5F5,
          ),
          _itemCommonView(
            leadingText: '余额',
            rightView: Container(
              width: 200,
              // color: Colors.blue,
              child: TextField(
                controller: _balanceController,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()],
                textAlign: TextAlign.end,
                decoration: InputDecoration(
                  hintText: '请输入',
                  hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                  border: OutlineInputBorder(borderSide: BorderSide.none),
                  filled: true,
                  fillColor: Colors.transparent,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                  isDense: true,
                ),
              ),
            ),
          ),
          Divider(
            height: 0.5,
            thickness: 0.5,
            color: MColor.xFFF5F5F5,
          ),
          _itemCommonView(
            leadingText: '是否计入资产',
            rightView: SizedBox(
              height: 24,
              child: Switch(
                value: _inAssets,
                activeTrackColor: MColor.skin,
                activeColor: MColor.xFFFFFFFF,
                onChanged: (value) {
                  setState(() {
                    _inAssets = !_inAssets;
                  });
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget get _bankSection {
    List<Widget> items = List.generate(_networkTypeStr.length, (index) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          setState(() {
            _networkType = index + 1;
          });
        },
        child: Row(
          children: [
            Icon(index == _networkType - 1 ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                size: 16, color: index == _networkType - 1 ? MColor.skin : MColor.xFF999999),
            const SizedBox(
              width: 6,
            ),
            Text(
              _networkTypeStr[index],
              style: TextStyle(height: 1.4, fontSize: 14, color: index == _networkType - 1 ? MColor.skin : MColor.xFF333333),
            ),
            if (index != _networkTypeStr.length - 1)
              const SizedBox(
                width: 16,
              ),
          ],
        ),
      );
    });
    return Container(
      // margin: EdgeInsets.symmetric(horizontal: 14),
      padding: EdgeInsets.symmetric(horizontal: 14, vertical: 12),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '选择账号类型',
            style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
          ),
          const SizedBox(
            height: 14,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: items,
          ),
        ],
      ),
    );
  }

  Widget _itemCommonView({required String leadingText, required Widget rightView, double? height}) {
    return ListTile(
      dense: true,
      contentPadding: EdgeInsets.zero,
      leading: Text(
        leadingText,
        style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A),
      ),
      trailing: rightView,
    );
  }

  @override
  bool get wantKeepAlive => true;
}
