import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:month_year_picker/month_year_picker.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/storage_util.dart';
import 'package:qiazhun/modules/auth/third_login_store.dart';
import 'package:qiazhun/modules/mine_tab/user_store.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/modules/mine_tab/mine_tab_page.dart';
import 'package:qiazhun/modules/settings/setting_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/modules/detail_tab/detail_tab_page.dart';
import 'package:qiazhun/modules/plan_tab/plan_page.dart';
import 'package:qiazhun/modules/stats_tab/statistics_page.dart';
import 'package:qiazhun/store/ad_store.dart';
import 'package:qiazhun/store/privacy_store.dart';

// void main() {
//   runApp(const ProviderScope(child: MyApp()));
// }

// class MyApp extends StatelessWidget {
//   const MyApp({Key? key}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     ScreenUtil.init(context);
//     return MaterialApp.router(
//       routeInformationParser: router.routeInformationParser,
//       routeInformationProvider: router.routeInformationProvider,
//       routerDelegate: router.routerDelegate,
//     );
//   }
// }

Future<void> main() async {
  // WidgetsFlutterBinding.ensureInitialized();
  // ExampleCandlestickData.getCandlestickData();
  // // 设置应用程序的方向为竖屏（只允许竖屏显示）
  // SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp])
  //     .then((_) {
  //   runApp(const MyApp());
  // });

  // runApp(App());

  WidgetsFlutterBinding.ensureInitialized();
  await Future.wait([StorageUtil.init()]);
  Get.put(PrivacyStore(), permanent: true);

  // Get.put(SettingStore(), permanent: true).initSettings();

  // if (Platform.isAndroid) {
  //   SystemUiOverlayStyle systemUiOverlayStyle = const SystemUiOverlayStyle(statusBarColor: Colors.transparent);
  //   SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
  // }
  if (Platform.isAndroid) {
    SystemUiOverlayStyle style = SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,

        ///这是设置状态栏的图标和字体的颜色
        ///Brightness.light  一般都是显示为白色
        ///Brightness.dark 一般都是显示为黑色
        statusBarIconBrightness: Brightness.dark);
    SystemChrome.setSystemUIOverlayStyle(style);
  }

  // await GlobalConst.sharedPreferences?.setBool(Constant.kStart, true);
  // ThemeMode themeMode = ThemeMode.system;
  // changeTheme();
  RouterHelper.instance;
  runApp(
    GetMaterialApp.router(
      initialBinding: AppBinding(),
      routeInformationParser: RouterHelper.router.routeInformationParser,
      routeInformationProvider: RouterHelper.router.routeInformationProvider,
      routerDelegate: RouterHelper.router.routerDelegate,
      debugShowCheckedModeBanner: false,
      title: '掐准记账',
      // locale: TranslationService.locale,
      // fallbackLocale: TranslationService.fallbackLocale,
      // translations: TranslationService(),
      // theme: AppTheme.lightTheme,
      // darkTheme: AppTheme.darkTheme,
      // themeMode: SettingStore.to.getThemeMode(),
      // locale: SettingStore.to.getCurrentLocale(),
      navigatorObservers: [RouterHelper.routeObserver],
      // getPages: AppPages.routes,
      builder: EasyLoading.init(
        builder: (context, child) {
          return GestureDetector(
            child: child,
            onPanDown: (details) {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
                FocusManager.instance.primaryFocus?.unfocus();
              }
            },
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
                FocusManager.instance.primaryFocus?.unfocus();
              }
            },
          );
        },
      ),
      // builder: ,
      localizationsDelegates: const [GlobalMaterialLocalizations.delegate, MonthYearPickerLocalizations.delegate],
      // localizationsDelegates: const [
      //   S.delegate,
      //   GlobalMaterialLocalizations.delegate,
      //   GlobalCupertinoLocalizations.delegate,
      //   GlobalWidgetsLocalizations.delegate
      // ],
      // supportedLocales: S.delegate.supportedLocales,
      localeListResolutionCallback: (locales, supportedLocales) {
        print(locales);
        return;
      },
    ),
  );

  Loading();
}

class AppBinding extends Bindings {
  @override
  void dependencies() {
    // Get.put(AdStore(), permanent: true);
    Get.put(UserStore(), permanent: true);
    Get.put(SettingStore(), permanent: true);
    Get.put(ThirdLoginStore(), permanent: true);
    Get.put(BookkeepingStore(), permanent: true);
  }
}
