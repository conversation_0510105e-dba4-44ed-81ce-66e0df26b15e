import 'package:json_annotation/json_annotation.dart';
import 'package:qiazhun/models/home_detail_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';

part 'plan_model.g.dart';

@JsonSerializable()
class PlanStoreRule {
  @JsonKey(name: 'cycle', defaultValue: 1)
  int? cycle = 1;
  @JsonKey(name: 'times')
  String? times;
  @JsonKey(name: 'price')
  String? price;
  @JsonKey(name: 'proportion')
  String? proportion;
  PlanStoreRule({this.cycle = 1, this.times, this.price, this.proportion});

  factory PlanStoreRule.fromJson(Map<String, dynamic> srcJson) => _$PlanStoreRuleFromJson(srcJson);

  Map<String, dynamic> toJson() => _$PlanStoreRuleToJson(this);
}

@JsonSerializable()
class PlanInfoResp {
  @JsonKey(name: 'detail')
  PlanInfo detail;

  @Json<PERSON>ey(name: 'list')
  List<PlanLogInfo> logs;
  PlanInfoResp(
    this.detail,
    this.logs,
  );

  factory PlanInfoResp.fromJson(Map<String, dynamic> srcJson) => _$PlanInfoRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$PlanInfoRespToJson(this);
}

@JsonSerializable()
class PlanLogInfo extends Object {
  @JsonKey(name: 'planLogId')
  int planLogId;

  @JsonKey(name: 'price')
  String price;

  @JsonKey(name: 'memo')
  String memo;

  @JsonKey(name: 'createtime')
  String createtime;

  @JsonKey(name: 'incomePrice')
  String incomePrice;

  PlanLogInfo(
    this.planLogId,
    this.price,
    this.memo,
    this.createtime,
    this.incomePrice,
  );

  factory PlanLogInfo.fromJson(Map<String, dynamic> srcJson) => _$PlanLogInfoFromJson(srcJson);

  Map<String, dynamic> toJson() => _$PlanLogInfoToJson(this);
}

@JsonSerializable()
class PlanInfo extends Object {
  @JsonKey(name: 'id')
  int id;

  @JsonKey(name: 'name')
  String name;

  @JsonKey(name: 'backgroundImage')
  String? backgroundImage;

  @JsonKey(name: 'icon')
  String? icon;

  @JsonKey(name: 'todayDay')
  int? todayDay;

  @JsonKey(name: 'residueDay')
  int? residueDay;

  @JsonKey(name: 'nowDay')
  int? nowDay;

  @JsonKey(name: 'startDate')
  String? startDate;
  @JsonKey(name: 'expectedAchieveDate')
  String? expectedAchieveDate;

  @JsonKey(name: 'proportion')
  String? proportion;

  @JsonKey(name: 'accumulatedAmount')
  String? accumulatedAmount;

  @JsonKey(name: 'storageMethod')
  String? storageMethod;
  @JsonKey(name: 'requiredAmount')
  String? requiredAmount;
  @JsonKey(name: 'status')
  int? status; //是否实现:1=未实现,2=已实现

  @JsonKey(name: 'memo')
  String? memo;
  @JsonKey(name: 'residuePrice')
  String? residuePrice;

  @JsonKey(name: 'accountBookkeepingNumberArr')
  List<BookkeepingInfo>? accountBookkeepingNumberArr;

  @JsonKey(name: 'storageRules')
  PlanStoreRule? rule;

  PlanInfo(
      this.id,
      this.name,
      this.backgroundImage,
      this.icon,
      this.todayDay,
      this.residueDay,
      this.nowDay,
      this.proportion,
      this.accumulatedAmount,
      this.requiredAmount,
      this.status,
      this.memo,
      this.startDate,
      this.expectedAchieveDate,
      this.storageMethod,
      this.rule,
      this.accountBookkeepingNumberArr,
      this.residuePrice);

  factory PlanInfo.fromJson(Map<String, dynamic> srcJson) => _$PlanInfoFromJson(srcJson);

  Map<String, dynamic> toJson() => _$PlanInfoToJson(this);
}
