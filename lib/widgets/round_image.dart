import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';

class RoundImage extends StatelessWidget {
  final String imageUrl;
  final double radius;
  final double size;
  final Color? backgroundColor;
  const RoundImage({required this.imageUrl, required this.radius, required this.size, this.backgroundColor, super.key});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
        borderRadius: BorderRadius.circular(radius),
        child: CachedNetworkImage(
          imageUrl: getImageUrl(imageUrl),
          width: size,
          height: size,
          fit: BoxFit.cover,
          placeholder: (ctx, e) {
            return Container(
              decoration: BoxDecoration(
                color: MColor.xFFECECEC,
              ),
            );
          },
          errorWidget: (ctx, e, x) {
            return Container(
              decoration: BoxDecoration(
                color: MColor.skin,
              ),
            );
          },
        ));
  }
}
