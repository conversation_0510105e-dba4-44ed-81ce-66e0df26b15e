import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_add_page.dart';
import 'package:qiazhun/modules/account/account_common_widgets.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class AccountAddDonationSection extends StatefulWidget {
  final AccountAddController? addController;
  final AccountModel? accountModel;
  const AccountAddDonationSection({super.key, this.addController, this.accountModel});

  @override
  State<StatefulWidget> createState() => _AccountAddDonationState();
}

class _AccountAddDonationState extends State<AccountAddDonationSection> {
  bool _donationType = true;
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _remarkController = TextEditingController();

  AccountModel? _targetAccount;

  @override
  void initState() {
    widget.addController?.onAction = _addAccount;
    if (widget.accountModel != null) {
      _donationType = widget.accountModel!.subType == '1';
      _nameController.text = widget.accountModel!.accountName ?? '';
      _priceController.text = widget.accountModel!.balance ?? '';
      _remarkController.text = widget.accountModel!.memo ?? '';
      if (widget.accountModel?.otherAccountInfo is Map) {
        var accountId = widget.accountModel!.otherAccountInfo['otherAccountId'];
        if (accountId != null) {
          _targetAccount = BookkeepingStore.to.accountList.firstWhereOrNull((el) => el.id == accountId);
        }
      }
    }
    super.initState();
  }

  Future<void> _addAccount() async {
    if (_priceController.text.isEmpty) {
      showToast('请填写金额');
      return;
    }
    if (_nameController.text.isEmpty) {
      showToast('请填写名字');
      return;
    }
    if (_remarkController.text.isEmpty) {
      showToast('请填写备注');
      return;
    }
    if (_targetAccount == null) {
      showToast(_donationType ? '请选择付款账户' : '请选择存入账户');
      return;
    }

    Loading.show();
    try {
      var resp = await AccountRepo.addSavingCard(
          id: widget.accountModel?.id,
          accountType: '7',
          price: _priceController.text,
          name: _nameController.text,
          accountName: _nameController.text,
          memo: _remarkController.text,
          otherAccountId: '${_targetAccount!.id}',
          donationType: _donationType ? '1' : '2');
      if (resp.code == 1) {
        showToast('添加账号成功');
        RouterHelper.router.pop();
      } else {
        showToast(resp.msg ?? '添加账号失败');
      }
    } catch (e) {
      showToast('添加账号失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(20)),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    setState(() {
                      if (!_donationType) {
                        _donationType = true;
                        _targetAccount = null;
                      }
                    });
                  },
                  child: Container(
                    height: 40,
                    decoration: _donationType
                        ? BoxDecoration(
                            color: MColor.xFFFFD180, borderRadius: BorderRadius.only(topLeft: Radius.circular(20), bottomRight: Radius.circular(20)))
                        : null,
                    child: Center(
                        child: Text(
                      '捐款给他人',
                      style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 15),
                    )),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    setState(() {
                      if (_donationType) {
                        _donationType = false;
                        _targetAccount = null;
                      }
                    });
                  },
                  child: Container(
                    height: 40,
                    decoration: !_donationType
                        ? BoxDecoration(
                            color: MColor.xFFFFD180, borderRadius: BorderRadius.only(topLeft: Radius.circular(20), bottomRight: Radius.circular(20)))
                        : null,
                    child: Center(
                        child: Text(
                      '被他人捐款',
                      style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 15),
                    )),
                  ),
                ),
              )
            ],
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 14.0),
            child: Column(
              children: [
                // AccountInputTile(),
                AccountArrowTile(
                    leadingText: _donationType ? '支出账户' : '存入账户',
                    onTap: () {
                      RouterHelper.router
                          .pushNamed(Routes.accountListPath, extra: {'selectMode': true, 'limitAccount': _donationType ? '6' : '7'}).then((value) {
                        if (value != null && value is Map<String, dynamic> && value.containsKey('selected')) {
                          // _accountItem1 = value['selected'] as AccountModel;
                          _targetAccount = value['selected'] as AccountModel;
                          setState(() {});
                        }
                      });
                    },
                    trailingText: _targetAccount?.getShortDesc()),
                Divider(
                  height: 0.5,
                  thickness: 0.5,
                  color: MColor.xFFF5F5F5,
                ),
                AccountInputTile(
                  leadingText: _donationType ? '被捐人' : '捐助人',
                  hint: '请输入',
                  textController: _nameController,
                ),
                Divider(
                  height: 0.5,
                  thickness: 0.5,
                  color: MColor.xFFF5F5F5,
                ),
                AccountInputTile(
                  leadingText: _donationType ? '捐助金额' : '被捐助金额',
                  hint: '请输入',
                  textController: _priceController,
                ),
                Divider(
                  height: 0.5,
                  thickness: 0.5,
                  color: MColor.xFFF5F5F5,
                ),
                AccountInputTile(
                  leadingText: '备注',
                  hint: '请输入',
                  textController: _remarkController,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
