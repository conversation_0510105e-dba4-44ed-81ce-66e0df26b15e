
// Providers
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:qiazhun/models/finance.dart';
import 'package:qiazhun/models/transaction_model.dart';

final financeDataProvider = StateNotifierProvider<FinanceDataNotifier, FinanceData>((ref) {
  return FinanceDataNotifier();
});

class FinanceDataNotifier extends StateNotifier<FinanceData> {
  FinanceDataNotifier()
      : super(FinanceData(
          totalAssets: 6850.08,
          income: 5893.00,
          expenses: 8493.00,
          weeklyBudget: 6000,
          weeklySpent: 5999,
          transactions: [
            Transaction(
              title: '工资收入',
              amount: 6888.00,
              date: DateTime(2024, 7, 31),
              category: 'income',
            ),
            Transaction(
              title: '早午晚餐',
              amount: -24.15,
              date: DateTime(2024, 7, 31),
              category: 'food',
            ),
            Transaction(
              title: '私家车费用',
              amount: -680.00,
              date: DateTime(2024, 7, 31),
              category: 'transport',
            ),
            Transaction(
              title: '工资收入',
              amount: 3000.00,
              date: DateTime(2024, 8, 1),
              category: 'income',
            ),
          ],
        ));

  void addTransaction(Transaction transaction) {
    state = FinanceData(
      totalAssets: state.totalAssets + transaction.amount,
      income: transaction.amount > 0 ? state.income + transaction.amount : state.income,
      expenses: transaction.amount < 0 ? state.expenses - transaction.amount : state.expenses,
      weeklyBudget: state.weeklyBudget,
      weeklySpent: state.weeklySpent,
      transactions: [transaction, ...state.transactions],
    );
  }
}
