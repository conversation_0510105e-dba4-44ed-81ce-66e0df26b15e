import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/router/router.dart';

class AboutPage extends StatefulWidget {
  const AboutPage({super.key});

  @override
  State<StatefulWidget> createState() => _AboutState();
}

class _AboutState extends State<AboutPage> {
  final _tiles = [
    {'icon': 'assets/images/ic_about_about.png', 'title': '关于我们', 'url': '${HttpUtil.kBaseUrl}/index/index/aboutUs'},
    {'icon': 'assets/images/ic_about_service.png', 'title': '联系客服', 'url': '${HttpUtil.kBaseUrl}/index/index/contactUs'},
    {'icon': 'assets/images/ic_about_privacy.png', 'title': '隐私政策', 'url': '${HttpUtil.kBaseUrl}/index/index/privacy'},
    {'icon': 'assets/images/ic_about_agreement.png', 'title': '服务协议', 'url': '${HttpUtil.kBaseUrl}/index/index/userAgreement'},
  ];
  static String _versionName = '';

  @override
  void initState() {
    super.initState();
    PackageInfo.fromPlatform().then((v) {
      _versionName = v.version;
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: SafeArea(
        top: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        '关于我们',
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 32,
                      ),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.asset(
                          'assets/images/ic_launcher.png',
                          width: 100,
                          height: 100,
                          fit: BoxFit.scaleDown,
                        ),
                      ),
                      const SizedBox(
                        height: 6,
                      ),
                      Image.asset(
                        'assets/images/icon_title.png',
                        width: 100,
                        fit: BoxFit.scaleDown,
                      ),
                      const SizedBox(
                        height: 6,
                      ),
                      Text(
                        'v$_versionName',
                        style: TextStyle(height: 1.4, fontSize: 12, fontWeight: FontWeight.w500, color: MColor.xFF999999),
                      ),
                      const SizedBox(
                        height: 14,
                      ),
                      Container(
                        margin: EdgeInsets.all(14),
                        padding: EdgeInsets.symmetric(horizontal: 14),
                        decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(20)),
                        child: Column(
                          children: List.generate(_tiles.length, (index) {
                            return _tileView(_tiles[index]);
                          }),
                        ),
                      )
                    ],
                  ))
            ],
          ),
        ),
      ),
    );
  }

  Widget _tileView(dynamic tile) {
    return ListTile(
      onTap: () {
        if (tile['url'].isNotEmpty) {
          RouterHelper.router.pushNamed(Routes.webPath, extra: {'title': tile['title'], 'url': tile['url']});
        }
      },
      contentPadding: EdgeInsets.symmetric(horizontal: 0, vertical: 4),
      dense: true,
      leading: Image.asset(
        tile['icon'],
        width: 20,
        height: 20,
        fit: BoxFit.scaleDown,
      ),
      title: Text(
        tile['title'],
        style: TextStyle(height: 1.4, fontSize: 15, color: MColor.xFF1B1C1A, fontWeight: FontWeight.w500),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 14,
        color: MColor.xFF999999,
      ),
    );
  }
}
