import 'dart:math';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:month_year_picker/month_year_picker.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/home_detail_model.dart';
import 'package:qiazhun/models/price_info.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/stats_tab/stat_chart_view.dart';
import 'package:qiazhun/modules/transaction_item_view.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/page_container_view.dart';
import 'package:qiazhun/widgets/price_view.dart';

class BookkeepingDetailPage extends StatefulWidget {
  final String bookkeepingNumber;
  final String? bookkeepingName;

  const BookkeepingDetailPage({
    required this.bookkeepingNumber,
    this.bookkeepingName,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _BookkeepingDetailPageState();
}

class _BookkeepingDetailPageState extends State<BookkeepingDetailPage> {
  bool _isLoading = true;
  BookkeepingDetailResp? _detailData;
  String _selectedTimeType = '1'; // 1=明细 2=月报 3=年报 4=总报
  DateTime _selectedMonth = DateTime.now();
  DateTime _selectedYear = DateTime.now();

  final ScrollController _scrollController = ScrollController();
  bool _isTimeTypeSelectorPinned = false;

  final Map<String, String> _timeTypeNames = {
    '1': '明细',
    '2': '月报',
    '3': '年报',
    '4': '总报',
  };

  // 分组后的交易数据
  Map<String, List<TransactionItem>> _groupedTransactions = {};

  @override
  void initState() {
    super.initState();
    _isLoading = true;
    _scrollController.addListener(_onScroll);
    _loadData();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // 监听滚动，实现吸顶效果
    const double threshold = 200.0; // 滚动阈值
    if (_scrollController.offset > threshold && !_isTimeTypeSelectorPinned) {
      setState(() {
        _isTimeTypeSelectorPinned = true;
      });
    } else if (_scrollController.offset <= threshold && _isTimeTypeSelectorPinned) {
      setState(() {
        _isTimeTypeSelectorPinned = false;
      });
    }
  }

  Future<void> _loadData() async {
    try {
      String? timeInterval;

      // 根据选择的时间类型确定时间区间
      switch (_selectedTimeType) {
        case '2': // 月报
          timeInterval = monthDateRange(_selectedMonth);
          break;
        case '3': // 年报
          timeInterval = yearDateRange(_selectedYear);
          break;
        default: // 明细和总报
          timeInterval = null;
          break;
      }

      final response =
          await BookkeepingRepo.getBookkeepingDetailWithLogs(bookkeepingNumber: widget.bookkeepingNumber, timeInterval: timeInterval, type: _selectedTimeType);

      if (response.code == 1 && response.data != null) {
        _detailData = response.data;
        _groupTransactionsByDate();
      } else {
        showToast(response.msg ?? '获取数据失败');
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      _isLoading = false;
      setState(() {});
    }
  }

  void _groupTransactionsByDate() {
    _groupedTransactions.clear();

    if (_detailData?.flowingWater != null) {
      for (var transaction in _detailData!.flowingWater!) {
        String dateKey = _formatDateKey(transaction.date ?? '');
        if (!_groupedTransactions.containsKey(dateKey)) {
          _groupedTransactions[dateKey] = [];
        }
        _groupedTransactions[dateKey]!.add(transaction);
      }
    }
  }

  String _formatDateKey(String dateStr) {
    try {
      if (dateStr.isEmpty) return '';
      final date = DateTime.parse(dateStr);
      return DateFormat('yyyy-MM-dd').format(date);
    } catch (e) {
      return dateStr;
    }
  }

  String yearDateRange(DateTime year) {
    DateTime begin = DateTime(year.year, 1, 1);
    DateTime end = DateTime(year.year, 12, 31);
    DateFormat dt = DateFormat('yyyy-MM-dd');
    String timeInterval = '';
    timeInterval = '${dt.format(begin)},${dt.format(end)}';
    return timeInterval;
  }

  void _showMonthPicker() {
    showMonthYearPicker(
      context: context,
      initialDate: _selectedMonth,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      locale: const Locale('zh'), // 显示中文
    ).then((picked) {
      if (picked != null) {
        setState(() {
          _selectedMonth = picked;
        });
        _loadData();
      }
    });
  }

  void _showYearPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择年份'),
        content: SizedBox(
          width: 300,
          height: 300,
          child: YearPicker(
            firstDate: DateTime(2020),
            lastDate: DateTime.now(),
            selectedDate: _selectedYear,
            onChanged: (date) {
              Navigator.of(context).pop();
              setState(() {
                _selectedYear = date;
              });
              _loadData();
            },
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MColor.xFFF5F5F5,
      body: Stack(
        children: [
          // 主要内容
          PageContainerView(title: widget.bookkeepingName ?? '账本详情', body: _buildContent()),
          // 吸顶的时间类型选择器
          if (_isTimeTypeSelectorPinned)
            Positioned(
              top: MediaQuery.of(context).padding.top + 56, // AppBar高度
              left: 0,
              right: 0,
              child: Container(
                color: MColor.xFFF5F5F5,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: _buildTimeTypeSelector(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: MColor.skin,
        ),
      );
    }
    if (_detailData == null) {
      return const Center(
        child: EmptyView(),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCard(),
            const SizedBox(height: 16),
            _buildTimeTypeSelector(),
            const SizedBox(height: 16),
            _buildTimeSelector(), // 新增时间选择器
            _buildTransactionList(),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '总支出',
                style: TextStyle(
                  color: MColor.xFF1B1C1A,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Builder(builder: (context) {
                final priceInfo = PriceInfo.parsePrice(_detailData?.expenseTotal ?? '0.00');
                return PriceView(
                  price: priceInfo,
                  integerFontSize: 24,
                  fractionalFontSize: 18,
                  textColor: priceInfo.isNegative ? MColor.xFFCB322E : MColor.skin,
                  fontWeight: FontWeight.w600,
                );
              }),
              const SizedBox(
                height: 20,
              ),
              const Text(
                '总收入',
                style: TextStyle(
                  color: MColor.xFF1B1C1A,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Builder(builder: (context) {
                final priceInfo = PriceInfo.parsePrice(_detailData?.incomeTotal ?? '0.00');
                return PriceView(
                  price: priceInfo,
                  integerFontSize: 24,
                  fractionalFontSize: 18,
                  textColor: priceInfo.isNegative ? MColor.xFFCB322E : MColor.skin,
                  fontWeight: FontWeight.w600,
                );
              }),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimeTypeSelector() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: _timeTypeNames.entries.map((entry) {
          final isSelected = _selectedTimeType == entry.key;
          return Expanded(
            child: GestureDetector(
              onTap: () {
                if (_selectedTimeType != entry.key) {
                  setState(() {
                    _selectedTimeType = entry.key;
                  });
                  _loadData();
                }
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected ? MColor.skin : Colors.transparent,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  entry.value,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: isSelected ? Colors.white : MColor.xFF999999,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTimeSelector() {
    // 只在月报和年报时显示时间选择器
    if (_selectedTimeType != '2' && _selectedTimeType != '3') {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _selectedTimeType == '2' ? _buildMonthSelector() : _buildYearSelector(),
    );
  }

  Widget _buildMonthSelector() {
    return Row(
      children: [
        Icon(Icons.calendar_month, color: MColor.skin, size: 20),
        const SizedBox(width: 8),
        Text(
          '选择月份:',
          style: TextStyle(
            fontSize: 14,
            color: MColor.xFF1B1C1A,
            fontWeight: FontWeight.w500,
          ),
        ),
        const Spacer(),
        GestureDetector(
          onTap: _showMonthPicker,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: MColor.skin.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: MColor.skin, width: 1),
            ),
            child: Text(
              DateFormat('yyyy年MM月').format(_selectedMonth),
              style: TextStyle(
                fontSize: 14,
                color: MColor.skin,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildYearSelector() {
    return Row(
      children: [
        Icon(Icons.date_range, color: MColor.skin, size: 20),
        const SizedBox(width: 8),
        Text(
          '选择年份:',
          style: TextStyle(
            fontSize: 14,
            color: MColor.xFF1B1C1A,
            fontWeight: FontWeight.w500,
          ),
        ),
        const Spacer(),
        GestureDetector(
          onTap: _showYearPicker,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: MColor.skin.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: MColor.skin, width: 1),
            ),
            child: Text(
              DateFormat('yyyy年').format(_selectedYear),
              style: TextStyle(
                fontSize: 14,
                color: MColor.skin,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionList() {
    if (_groupedTransactions.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const Center(
          child: Text(
            '暂无流水记录',
            style: TextStyle(
              color: MColor.xFF999999,
              fontSize: 14,
            ),
          ),
        ),
      );
    }

    // 按日期排序
    final sortedDates = _groupedTransactions.keys.toList()..sort((a, b) => b.compareTo(a)); // 降序排列，最新的在前
    final children = <Widget>[];

    children.add(_buildPieChart());

    for (var date in sortedDates) {
      final transactions = _groupedTransactions[date]!;
      children.add(_buildDateSection(date, transactions));
    }

    return Column(
      children: children,
    );
  }

  Widget _buildPieChart() {
    // 只在月报和年报时显示时间选择器
    if (_selectedTimeType == '1') {
      return const SizedBox.shrink();
    }

    final _summaryColors = {
      '1': MColor.xFFF5D16D,
      '2': MColor.xFF577F8C,
      '4': MColor.xFFD05363,
      '3': MColor.xFF1C6974,
      '6': MColor.xFFFB9186,
      '5': MColor.xFF5CB188,
      '8': MColor.skin
    };
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: StatPieChartView(
        titles: _detailData?.pieChartData?.map((e) => e.percentage ?? '').toList() ?? [],
        percentages: _detailData?.pieChartData?.map((e) => double.tryParse(e.percentage ?? '0') ?? 0.0).toList() ?? [],
        colors: _detailData?.pieChartData?.map((e) => _summaryColors['${Random().nextInt(7)}'] ?? Colors.transparent).toList() ?? [],
        radius: 50,
        innerRaidus: 50,
        showIndicator: true,
        indicators: _detailData?.pieChartData?.map((e) => e.categoryName ?? '').toList(),
      ),
    );
  }

  Widget _buildDateSection(String date, List<TransactionItem> transactions) {
    // 计算当日收入和支出总额
    double dayIncome = 0.0;
    double dayExpense = 0.0;

    for (var transaction in transactions) {
      double amount = double.tryParse(transaction.money ?? '0') ?? 0.0;
      if (transaction.type == '1') {
        // 收入
        dayIncome += amount;
      } else {
        // 支出
        dayExpense += amount;
      }
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 日期头部
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Color(0xFFF5F5F5), width: 1),
              ),
            ),
            child: Row(
              children: [
                Text(
                  _formatDisplayDate(date),
                  style: const TextStyle(
                    color: MColor.xFF1B1C1A,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  _getWeekday(date),
                  style: const TextStyle(
                    color: MColor.xFF999999,
                    fontSize: 14,
                  ),
                ),
                const Spacer(),
                Text(
                  '收 ${dayIncome.toStringAsFixed(2)}',
                  style: const TextStyle(
                    color: MColor.xFFCB322E,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  '支 ${dayExpense.toStringAsFixed(2)}',
                  style: const TextStyle(
                    color: MColor.skin,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
          // 交易项目列表
          ...transactions.map((transaction) => TransactionItemView(transaction)),
        ],
      ),
    );
  }

  String _formatDisplayDate(String dateStr) {
    try {
      if (dateStr.isEmpty) return '';
      final date = DateTime.parse(dateStr);
      return '${date.month}月${date.day}日';
    } catch (e) {
      return dateStr;
    }
  }

  String _getWeekday(String dateStr) {
    try {
      if (dateStr.isEmpty) return '';
      final date = DateTime.parse(dateStr);
      const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      return weekdays[date.weekday - 1];
    } catch (e) {
      return '';
    }
  }
}
