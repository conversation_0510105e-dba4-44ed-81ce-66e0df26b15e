import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/price_info.dart';
import 'package:qiazhun/modules/account/account_item_view.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/modules/account/account_group_repo.dart';
import 'package:qiazhun/modules/mine_tab/user_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/page_container_view.dart';
import 'package:qiazhun/widgets/price_view.dart';

class MyAssetsPage extends StatefulWidget {
  const MyAssetsPage({super.key});

  @override
  State<StatefulWidget> createState() => _MyAssetsState();
}

class _MyAssetsState extends State<MyAssetsPage> {
  bool _isLoading = true;

  final List<AccountGroupModel> _accountGroups = [];
  dynamic _assets;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    _isLoading = true;
    Loading.show();
    try {
      // Load user assets
      var assetsResp = await UserRepo.getUserAssets();
      if (assetsResp.code == 1) {
        _assets = assetsResp.data?.assets;
      }

      // Load account groups
      var groupsResp = await AccountGroupRepo.getAccountGroupList();
      if (groupsResp.code == 1) {
        _accountGroups.clear();
        // Filter out empty groups (groups with no accounts)
        var allGroups = groupsResp.data ?? [];
        _accountGroups.addAll(allGroups.where((group) => group.hasAccounts));
      } else {
        showToast(groupsResp.msg ?? '获取分组数据失败');
      }
    } catch (e) {
      showToast('获取数据失败 $e');
    } finally {
      Loading.dismiss();
      _isLoading = false;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
        title: '我的资产',
        actions: [
          IconButton(
            icon: Icon(Icons.settings, color: MColor.xFF1B1C1A),
            onPressed: () {
              RouterHelper.router.pushNamed(Routes.accountGroupManagementPath).then((_) {
                _loadData(); // Refresh data when returning from group management
              });
            },
          ),
        ],
        body: Builder(builder: (context) {
          if (_isLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          return ListView.separated(
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                if (index == 0) {
                  return _totalAssetView;
                } else {
                  return _groupView(_accountGroups[index - 1]);
                }
              },
              separatorBuilder: (context, index) {
                return const SizedBox(
                  height: 14,
                );
              },
              itemCount: _accountGroups.length + 1);
        }),
        backgroundImage: 'assets/images/myassets_bg.png',
        footerButtons: [_createAccountButton]);
  }

  Future<void> _deleteAccount(AccountModel data) async {
    Loading.show();
    try {
      var resp = await AccountRepo.deleteAccount(data.id);
      if (resp.code == 1) {
        await _loadData();
        showToast('删除成功');
      } else {
        showToast(resp.msg ?? '删除失败');
      }
    } catch (e) {
      showToast('删除失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  Widget _groupView(AccountGroupModel group) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 14),
      decoration: BoxDecoration(
        color: MColor.xFFFFFFFF,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Group header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              group.name ?? '',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: MColor.xFF1B1C1A,
              ),
            ),
          ),
          // Group accounts
          ...group.accounts?.map((account) => _itemView(account)) ?? [],
        ],
      ),
    );
  }

  Widget _itemView(AccountModel data) {
    return Slidable(
      key: ValueKey(data.id),
      // The end action pane is the one at the right or the bottom side.
      endActionPane: ActionPane(
        motion: ScrollMotion(),
        children: [
          SlidableAction(
            onPressed: (context) {
              showCustomDialog(
                '确认删除',
                content: '删除后不可恢复，请确认是否删除',
                cancel: true,
                onConfirm: () async {
                  await _deleteAccount(data);
                },
                onCancel: () {},
              );
            },
            backgroundColor: MColor.xFFFF7858,
            foregroundColor: MColor.xFFFFFFFF,
            icon: Icons.delete,
            label: '删除',
          ),
          SlidableAction(
            onPressed: (context) async {
              RouterHelper.router.pushNamed(Routes.editAccountPath, extra: {'accountId': data.id}).then((value) {
                _loadData();
              });
            },
            backgroundColor: MColor.xFFFFBE4A,
            foregroundColor: MColor.xFFFFFFFF,
            icon: Icons.edit,
            label: '编辑',
          )
        ],
      ),
      child: AccountItemView(
        data,
        onDataChanged: () {
          _loadData();
        },
      ),
    );
  }

  Widget get _createAccountButton {
    return GestureDetector(
      onTap: () {
        RouterHelper.router.pushNamed(Routes.addAccountPath).then((_) {
          _loadData();
        });
      },
      child: Container(
        height: 50,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            gradient:
                LinearGradient(begin: const FractionalOffset(0.0, 0.0), end: const FractionalOffset(0.0, 1.0), colors: [Color(0xFF68C2BF), Color(0xFF4C9B93)])),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '创建账户',
              style: TextStyle(color: MColor.xFFFFFFFF, fontWeight: FontWeight.w500, fontSize: 16, height: 1),
            ),
          ],
        ),
      ),
    );
  }

  Widget get _totalAssetView {
    return Builder(builder: (context) {
      return Container(
        padding: const EdgeInsets.all(15),
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(color: Colors.transparent, borderRadius: BorderRadius.vertical(top: Radius.circular(15), bottom: Radius.circular(15))),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: 164,
              child: Stack(
                children: [
                  Positioned.fill(
                      top: 15,
                      bottom: 0,
                      child: Container(
                        width: MediaQuery.of(context).size.width,
                        padding: const EdgeInsets.fromLTRB(15, 15, 15, 0),
                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(15), color: MColor.skin),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Row(
                              children: [
                                Image.asset(
                                  'assets/images/ic_rings.png',
                                  width: 28,
                                  height: 17,
                                ),
                                const SizedBox(
                                  width: 6,
                                ),
                                const Text('净资产', style: TextStyle(height: 1, color: Color.fromARGB(0xFF, 168, 220, 217), fontSize: 14)),
                              ],
                            ),
                            const SizedBox(
                              height: 8,
                            ),
                            Row(
                              children: [
                                PriceView(
                                  price: PriceInfo.parsePrice(_assets?['currentAssetsTotal'] ?? '0.00'),
                                  integerFontSize: 38,
                                  fractionalFontSize: 20,
                                  textColor: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 22,
                            ),
                            Row(
                              children: [
                                Expanded(
                                    child: RichText(
                                  text: WidgetSpan(
                                      child: Row(
                                    children: [
                                      PriceView(
                                          price: PriceInfo.parsePrice(_assets?['incomeTotal'] ?? '0.00'),
                                          integerFontSize: 20,
                                          fractionalFontSize: 14,
                                          prefix: '资产',
                                          prefixStyle: TextStyle(height: 1, color: Color.fromARGB(255, 168, 220, 217), fontSize: 12),
                                          prefixPadding: 10),
                                    ],
                                  )),
                                )),
                                const SizedBox(
                                  width: 12,
                                ),
                                Expanded(
                                    child: RichText(
                                  textAlign: TextAlign.end,
                                  text: WidgetSpan(
                                      child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      PriceView(
                                          price: PriceInfo.parsePrice(_assets?['expenditureTotal'] ?? '0.00'),
                                          integerFontSize: 20,
                                          fractionalFontSize: 14,
                                          prefix: '负债',
                                          prefixStyle: TextStyle(height: 1, color: Color.fromARGB(255, 168, 220, 217), fontSize: 12),
                                          prefixPadding: 10),
                                    ],
                                  )),
                                ))
                              ],
                            )
                          ],
                        ),
                      )),
                  Positioned(
                      top: 0,
                      right: 20,
                      child: Image.asset(
                        'assets/images/cat.png',
                        width: 124,
                        height: 124,
                      ))
                ],
              ),
            ),
          ],
        ),
      );
    });
  }
}
