import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/auth/auth_repo.dart';
import 'package:qiazhun/modules/mine_tab/user_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class VerificationPage extends StatefulWidget {
  final int bindType; // 0-绑定手机号 1-手机号登录 2-重新绑定手机号
  const VerificationPage({super.key, required this.bindType});

  @override
  State<StatefulWidget> createState() => _VerificationState();
}

class _VerificationState extends State<VerificationPage> {
  bool hasSentCode = false;
  List<String> code = List.filled(4, '');
  List<FocusNode> focusNodes = List.generate(4, (index) => FocusNode());
  final _phoneController = TextEditingController();

  String _mobile = '';

  @override
  void dispose() {
    for (var node in focusNodes) {
      node.dispose;
    }
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _sendCode() async {
    String phoneNumber = _phoneController.text;
    if (phoneNumber.isPhoneNumber) {
      try {
        var resp = await AuthRepo.sendSms(phoneNumber);
        if (resp.code == 1) {
          setState(() {
            _mobile = phoneNumber;
            hasSentCode = true;
          });
        } else {
          showToast(resp.msg ?? '发送验证码失败');
        }
      } catch (e) {
        showToast('发送验证码失败 $e');
      }
    } else {
      showToast('请输入手机号');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        extendBodyBehindAppBar: true,
        backgroundColor: Color(0xFFF5F5F5),
        appBar: widget.bindType == 2
            ? AppBar(
                backgroundColor: Colors.transparent,
                scrolledUnderElevation: 0,
                centerTitle: true,
                leading: IconButton(
                  icon: Image.asset(
                    'assets/images/ic_back.png',
                    width: 24,
                    height: 24,
                  ),
                  onPressed: () {
                    RouterHelper.router.pop();
                  },
                ),
              )
            : null,
        body: SafeArea(
            top: false,
            child: SafeArea(
                top: false,
                child: Stack(children: [
                  Container(),
                  Container(
                    height: MediaQuery.of(context).size.height / 2,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                          colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                    ),
                  ),
                  Positioned(
                      top: 133,
                      child: Container(
                        width: MediaQuery.of(context).size.width,
                        child: Column(
                          children: [
                            Image.asset(
                              'assets/images/icon_title.png',
                              width: 150,
                              height: 35,
                            ),
                            hasSentCode ? _inputView : _sendView
                          ],
                        ),
                      ))
                ]))));
  }

  Widget get _sendView {
    return Container(
      padding: EdgeInsets.fromLTRB(24, 50, 24, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.bindType != 1 ? '绑定手机号' : '输入手机号',
            style: TextStyle(fontSize: 20, color: Color(0xFF1B1B1B), height: 1.4),
          ),
          const SizedBox(
            height: 12,
          ),
          Text(
            widget.bindType != 1 ? '请输入所绑定的手机号' : '请输入所登录的手机号',
            style: TextStyle(fontSize: 14, color: Color(0xFF979797), height: 1),
          ),
          const SizedBox(
            height: 24,
          ),
          TextField(
            controller: _phoneController,
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              hintText: '输入手机号',
              hintStyle: TextStyle(fontSize: 14, color: Color(0xFF979797), height: 1),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: BorderSide.none),
              filled: true,
              fillColor: Colors.white,
              // prefixIcon: SizedBox(
              //   width: 16,
              //   height: 16,
              //   child: Image.asset(
              //     'assets/images/ic_phone.png',
              //     width: 16,
              //     height: 16,
              //   ),
              // ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              prefixIcon: Icon(Icons.smartphone_outlined, size: 20, color: Color(0xFF979797)),
              isDense: true,
            ),
          ),
          const SizedBox(
            height: 48,
          ),
          Row(
            children: [
              Expanded(
                child: TextButton(
                    onPressed: () {
                      _sendCode();
                    },
                    style: TextButton.styleFrom(backgroundColor: MColor.skin, padding: EdgeInsets.fromLTRB(10, 10, 10, 10)),
                    child: Text('获取验证码', style: TextStyle(color: MColor.xFFFFFFFF))),
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget get _inputView {
    return Container(
      padding: EdgeInsets.fromLTRB(24, 50, 24, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '输入验证码',
            style: TextStyle(fontSize: 20, color: Color(0xFF1B1B1B), height: 1.4),
          ),
          const SizedBox(
            height: 12,
          ),
          Text(
            '已发送验证码',
            style: TextStyle(fontSize: 14, color: Color(0xFF979797), height: 1),
          ),
          const SizedBox(
            height: 24,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: List.generate(focusNodes.length, (index) {
              return Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: TextField(
                    focusNode: focusNodes[index],
                    textAlign: TextAlign.center,
                    maxLength: 1,
                    keyboardType: TextInputType.numberWithOptions(decimal: true),
                    decoration: InputDecoration(border: InputBorder.none, counterText: '', filled: true, fillColor: Colors.white),
                    onChanged: (value) => onCodeChanged(value, index),
                  ),
                ),
              );
            }),
          ),
          const SizedBox(
            height: 48,
          ),
          Row(
            children: [
              Expanded(
                child: TextButton(
                    onPressed: () {
                      _bindMobile();
                    },
                    style: TextButton.styleFrom(backgroundColor: MColor.skin, padding: EdgeInsets.fromLTRB(10, 10, 10, 10)),
                    child: Text(widget.bindType != 1 ? '绑定' : '登录', style: TextStyle(color: MColor.xFFFFFFFF))),
              ),
            ],
          )
        ],
      ),
    );
  }

  void onCodeChanged(String value, int index) {
    if (value.isNotEmpty && index < focusNodes.length - 1) {
      focusNodes[index + 1].requestFocus();
      // FocusScope.of(Get.context!).requestFocus(focusNodes[index + 1]);
    }
    if (value.isEmpty && index > 0) {
      focusNodes[index - 1].requestFocus();
      // FocusScope.of(Get.context!).requestFocus(focusNodes[index - 1]);
    }
    code[index] = value;

    if (code.every((el) => el.isNotEmpty) && index == focusNodes.length - 1 && _mobile.isPhoneNumber) {
      FocusManager.instance.primaryFocus?.unfocus();
      _bindMobile();
    }
  }

  Future<void> _bindMobile() async {
    Loading.show();
    try {
      if (code.every((element) => element.isNotEmpty) && _mobile.isPhoneNumber) {
        if (widget.bindType == 2) {
          var resp = await AuthRepo.reBindMobile(_mobile, code.join());
          if (resp.code == 1) {
            RouterHelper.router.pop();
          } else {
            showToast(resp.msg ?? '');
          }
        } else if (widget.bindType == 0) {
          var resp = await AuthRepo.bindMobile(_mobile, code.join());
          if (resp.code == 1) {
            RouterHelper.router.go(Routes.detailTabPath);
          } else {
            showToast(resp.msg ?? '');
          }
        } else if (widget.bindType == 1) {
          var resp = await AuthRepo.mobileLogin(_mobile, code.join());
          if (resp.code == 1) {
            if (resp.data?.userInfo.token?.isNotEmpty == true) {
              UserStore.to.saveUserInfo(resp.data!.userInfo);
            }
            RouterHelper.router.go(Routes.detailTabPath);
          } else {
            showToast(resp.msg ?? '');
          }
        }
      }
    } catch (e) {
      logger.e('Verification _bindMobile error $e');
    } finally {
      Loading.dismiss();
    }
  }
}
