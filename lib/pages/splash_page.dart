import 'package:flutter/material.dart';
// import 'package:flutter_unionad/flutter_unionad.dart';
import 'package:qiazhun/constants/design.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({
    super.key,
  });
  @override
  State createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  bool _offstage = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // FlutterUnionadSplashAdView(
        //   //android 开屏广告广告id 必填 889033013 102729400
        //   androidCodeId: "103190393",
        //   //ios 开屏广告广告id 必填
        //   iosCodeId: "103190393",
        //   //是否支持 DeepLink 选填
        //   supportDeepLink: true,
        //   // 期望view 宽度 dp 选填
        //   width: MediaQuery.of(context).size.width,
        //   //期望view高度 dp 选填
        //   height: MediaQuery.of(context).size.height - 100,
        //   //是否影藏跳过按钮(当影藏的时候显示自定义跳过按钮) 默认显示
        //   hideSkip: false,
        //   //超时时间
        //   timeout: 3000,
        //   callBack: FlutterUnionadSplashCallBack(
        //     onShow: () {
        //       print("AdStore 开屏广告显示");
        //       setState(() => _offstage = false);
        //     },
        //     onClick: () {
        //       print("AdStore 开屏广告点击");
        //     },
        //     onFail: (error) {
        //       print("AdStore 开屏广告失败 $error");
        //       Navigator.pop(context);
        //     },
        //     onFinish: () {
        //       print("AdStore 开屏广告倒计时结束");
        //       Navigator.pop(context);
        //     },
        //     onSkip: () {
        //       print("AdStore 开屏广告跳过");
        //       Navigator.pop(context);
        //     },
        //     onTimeOut: () {
        //       print("AdStore 开屏广告超时");
        //     },
        //   ),
        // ),
        SizedBox(
          height: 100,
          child: Container(
            color: Colors.white,
            alignment: Alignment.center,
            child: Text(
              "掐准记账",
              style: TextStyle(
                fontSize: 20,
                color: MColor.skin,
                decoration: TextDecoration.none,
              ),
            ),
          ),
        )
      ],
    );
  }
}
