// import 'dart:async';

// import 'package:flutter_unionad/flutter_unionad.dart';
// import 'package:get/get.dart';
// import 'package:qiazhun/store/privacy_store.dart';

// class AdStore extends GetxController {
//   static AdStore get to => Get.find();

//   bool? _init;
//   String? _version;
//   StreamSubscription? _adViewStream;

//   int _themeStatus = FlutterUnionAdTheme.NIGHT;

//   bool isInited = false;

//   @override
//   void onInit() async {
//     if (PrivacyStore.to.isPrivacyAgreed()) {
//       _initRegister();
//       // _register();
//       isInited = true;
//     }
//     super.onInit();
//   }

//   ///初始化
//   Future<void> _register() async {
//     _adViewStream = FlutterUnionadStream.initAdStream(
//       flutterUnionadFullVideoCallBack: FlutterUnionadFullVideoCallBack(
//         onShow: () {
//           print("AdStore 全屏广告显示");
//         },
//         onSkip: () {
//           print("AdStore 全屏广告跳过");
//         },
//         onClick: () {
//           print("AdStore 全屏广告点击");
//         },
//         onFinish: () {
//           print("AdStore 全屏广告结束");
//         },
//         onFail: (error) {
//           print("AdStore 全屏广告错误 $error");
//         },
//         onClose: () {
//           print("AdStore 全屏广告关闭");
//         },
//       ),
//     );
//   }

//   //注册
//   void _initRegister() async {
//     _init = await FlutterUnionad.register(
//         //穿山甲广告 Android appid 必填
//         androidAppId: "5619413",
//         //穿山甲广告 ios appid 必填
//         iosAppId: "5619413",
//         //appname 必填
//         appName: "掐准记账",
//         //使用聚合功能一定要打开此开关，否则不会请求聚合广告，默认这个值为false
//         //true使用GroMore下的广告位
//         //false使用广告变现下的广告位
//         useMediation: true,
//         //是否为计费用户 选填
//         paid: false,
//         //用户画像的关键词列表 选填
//         keywords: "",
//         //是否允许sdk展示通知栏提示 选填
//         allowShowNotify: true,
//         //是否显示debug日志
//         debug: true,
//         //是否支持多进程 选填
//         supportMultiProcess: false,
//         //主题模式 默认FlutterUnionAdTheme.DAY,修改后需重新调用初始化
//         themeStatus: _themeStatus,
//         //允许直接下载的网络状态集合 选填
//         directDownloadNetworkType: [
//           FlutterUnionadNetCode.NETWORK_STATE_2G,
//           FlutterUnionadNetCode.NETWORK_STATE_3G,
//           FlutterUnionadNetCode.NETWORK_STATE_4G,
//           FlutterUnionadNetCode.NETWORK_STATE_WIFI
//         ],
//         androidPrivacy: AndroidPrivacy(
//           //是否允许SDK主动使用地理位置信息 true可以获取，false禁止获取。默认为true
//           isCanUseLocation: false,
//           //当isCanUseLocation=false时，可传入地理位置信息，穿山甲sdk使用您传入的地理位置信息lat
//           lat: 0.0,
//           //当isCanUseLocation=false时，可传入地理位置信息，穿山甲sdk使用您传入的地理位置信息lon
//           lon: 0.0,
//           // 是否允许SDK主动使用手机硬件参数，如：imei
//           isCanUsePhoneState: false,
//           //当isCanUsePhoneState=false时，可传入imei信息，穿山甲sdk使用您传入的imei信息
//           imei: "",
//           // 是否允许SDK主动使用ACCESS_WIFI_STATE权限
//           isCanUseWifiState: false,
//           // 当isCanUseWifiState=false时，可传入Mac地址信息
//           macAddress: "",
//           // 是否允许SDK主动使用WRITE_EXTERNAL_STORAGE权限
//           isCanUseWriteExternal: false,
//           // 开发者可以传入oaid
//           oaid: "b69cd3cf68900323",
//           // 是否允许SDK主动获取设备上应用安装列表的采集权限
//           alist: false,
//           // 是否能获取android ID
//           isCanUseAndroidId: false,
//           // 开发者可以传入android ID
//           androidId: "",
//           // 是否允许SDK在申明和授权了的情况下使用录音权限
//           isCanUsePermissionRecordAudio: false,
//           // 是否限制个性化推荐接口
//           isLimitPersonalAds: false,
//           // 是否启用程序化广告推荐 true启用 false不启用
//           isProgrammaticRecommend: false,
//         ),
//         iosPrivacy: IOSPrivacy(
//           //允许个性化广告
//           limitPersonalAds: false,
//           //允许程序化广告
//           limitProgrammaticAds: false,
//           //允许CAID
//           forbiddenCAID: false,
//         ));
//     print("AdStore sdk初始化 $_init");
//     _version = await FlutterUnionad.getSDKVersion();
//     _themeStatus = await FlutterUnionad.getThemeStatus();
//   }
// }
