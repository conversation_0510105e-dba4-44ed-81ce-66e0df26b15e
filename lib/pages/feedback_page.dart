import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/settings/setting_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class FeedbackPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _FeedbackState();
}

class _FeedbackState extends State<FeedbackPage> {
  final TextEditingController _contentController = TextEditingController();
  final TextEditingController _contactController = TextEditingController();

  String? _imageUrl;

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        dividerTheme: DividerThemeData(
          color: Colors.transparent,
        ),
      ),
      child: Scaffold(
        extendBody: true,
        persistentFooterButtons: [_submitButton],
        body: SafeArea(
          top: false,
          child: Container(
            color: Colors.yellow,
            child: Stack(
              children: [
                Container(
                  color: const Color(0xFFF5F5F5),
                ),
                Positioned(
                  // top: 0,
                  child: Container(
                    height: 203,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                          colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                    ),
                  ),
                ),
                Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: AppBar(
                        backgroundColor: Colors.transparent,
                        scrolledUnderElevation: 0,
                        centerTitle: true,
                        leading: IconButton(
                          icon: Image.asset(
                            'assets/images/ic_back.png',
                            width: 24,
                            height: 24,
                          ),
                          onPressed: () {
                            RouterHelper.router.pop();
                          },
                        ),
                        titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                        title: Text(
                          '意见反馈',
                          style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                        ))),
                Positioned.fill(
                    top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                    // top: 0,
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(14.0),
                          child: TextField(
                            controller: _contentController,
                            keyboardType: TextInputType.text,
                            textAlign: TextAlign.start,
                            style: TextStyle(height: 1.4, fontSize: 14, color: MColor.xFF1B1C1A),
                            decoration: InputDecoration(
                              hintText: '请留下您的反馈意见',
                              hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                              border: OutlineInputBorder(borderSide: BorderSide.none, borderRadius: BorderRadius.circular(20)),
                              filled: true,
                              fillColor: MColor.xFFFFFFFF,
                              contentPadding: const EdgeInsets.all(14),
                              isDense: true,
                            ),
                            maxLines: 5,
                            // minLines: 5,
                          ),
                        ),
                        if (_imageUrl == null)
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 14, vertical: 14),
                            padding: EdgeInsets.symmetric(vertical: 17.5),
                            decoration: BoxDecoration(color: MColor.skin.withOpacity(0.1), borderRadius: BorderRadius.circular(10)),
                            child: GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                showUploadDialog(context, (filePath) {
                                  _imageUrl = filePath;
                                  setState(() {});
                                }, (errorMsg) {
                                  showToast(errorMsg ?? '上传失败');
                                });
                              },
                              child: Center(
                                child: Column(
                                  children: [
                                    Icon(
                                      Icons.add,
                                      color: MColor.xFF999999,
                                      size: 20,
                                    ),
                                    const SizedBox(
                                      height: 8,
                                    ),
                                    Text(
                                      '上传图片',
                                      style: TextStyle(height: 1.4, fontSize: 14, color: MColor.xFF999999),
                                    )
                                  ],
                                ),
                              ),
                            ),
                          )
                        else
                          SizedBox(
                            height: 80,
                            child: Stack(
                              children: [
                                CachedNetworkImage(
                                  imageUrl: getImageUrl(_imageUrl),
                                  height: 80,
                                  fit: BoxFit.fitHeight,
                                ),
                                Positioned(
                                    top: 0,
                                    right: 0,
                                    left: 0,
                                    bottom: 0,
                                    child: IconButton(
                                        onPressed: () {
                                          setState(() {
                                            _imageUrl = null;
                                          });
                                        },
                                        icon: Icon(Icons.do_not_disturb_on_outlined, color: MColor.xFFCB322E, size: 20)))
                              ],
                            ),
                          ),
                        Padding(
                          padding: const EdgeInsets.all(14.0),
                          child: TextField(
                            controller: _contactController,
                            keyboardType: TextInputType.phone,
                            textAlign: TextAlign.start,
                            decoration: InputDecoration(
                              hintText: '请输入手机号',
                              hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
                              border: OutlineInputBorder(borderSide: BorderSide.none, borderRadius: BorderRadius.circular(10)),
                              filled: true,
                              fillColor: MColor.xFFFFFFFF,
                              contentPadding: const EdgeInsets.all(14),
                              isDense: true,
                            ),
                          ),
                        )
                      ],
                    ))
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget get _submitButton {
    return GestureDetector(
      onTap: () async {
        if (_contentController.text.isEmpty) {
          showToast('请填写反馈意见');
          return;
        }
        if (_imageUrl?.isNotEmpty == false) {
          showToast('请上传图片');
          return;
        }
        if (_contactController.text.isEmpty) {
          showToast('请填写手机号');
          return;
        }
        Loading.show();
        try {
          var resp = await SettingRepo.feedback(_contentController.text, _imageUrl!, _contactController.text);
          if (resp.code == 1) {
            showToast('感谢您的反馈');
            RouterHelper.router.pop();
          }
        } catch (e) {
        } finally {
          Loading.dismiss();
        }
      },
      child: Container(
        height: 50,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            gradient:
                LinearGradient(begin: const FractionalOffset(0.0, 0.0), end: const FractionalOffset(0.0, 1.0), colors: [Color(0xFF68C2BF), Color(0xFF4C9B93)])),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '提交',
              style: TextStyle(color: MColor.xFFFFFFFF, fontWeight: FontWeight.w500, fontSize: 16, height: 1),
            ),
          ],
        ),
      ),
    );
  }
}
