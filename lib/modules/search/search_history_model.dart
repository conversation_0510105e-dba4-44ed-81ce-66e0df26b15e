import 'package:json_annotation/json_annotation.dart';

part 'search_history_model.g.dart';

@JsonSerializable()
class SearchHistoryModel extends Object {
  @Json<PERSON>ey(name: 'query')
  String query;

  @J<PERSON><PERSON>ey(name: 'timeStamp')
  num? timeStamp;

  SearchHistoryModel(this.query, this.timeStamp);

  factory SearchHistoryModel.fromJson(Map<String, dynamic> srcJson) => _$SearchHistoryModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$SearchHistoryModelToJson(this);
}
