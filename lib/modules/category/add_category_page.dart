import 'package:flutter/material.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/router/router.dart';

class AddCategoryPage extends StatefulWidget {
  const AddCategoryPage({super.key});

  @override
  State<StatefulWidget> createState() => _AddCategoryState();
}

class _AddCategoryState extends State<AddCategoryPage> {
  late TabController _tabController;

  List<Widget> _tabPages = [];

  @override
  void initState() {
    super.initState();
    _getData();
  }

  Future<void> _getData() async {
    try {
      // await
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: SafeArea(
        top: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      actions: [
                        IconButton(
                            onPressed: () {
                              RouterHelper.router.pushNamed(Routes.addLedgerPath);
                            },
                            icon: Image.asset(
                              'assets/images/ic_add_3.png',
                              width: 22,
                              height: 22,
                              fit: BoxFit.fill,
                            )),
                      ],
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        '账本列表',
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: Column(
                    children: [_categoryTabView],
                  ))
            ],
          ),
        ),
      ),
    );
  }

  Widget get _categoryTabView {
    return Column(
      children: [
        DefaultTabController(
          length: 5,
          child: Container(
            color: MColor.xFFFFFFFF,
            child: Row(
              children: [
                Expanded(
                  child: TabBar(
                      padding: EdgeInsets.zero,
                      indicatorPadding: EdgeInsets.zero,
                      labelPadding: EdgeInsets.symmetric(horizontal: 8.0),
                      controller: _tabController,
                      indicatorSize: TabBarIndicatorSize.label,
                      // indicatorWeight: 1,
                      onTap: (value) {
                        // controller.tabIndex.value = value;
                      },
                      isScrollable: true,
                      tabAlignment: TabAlignment.start,
                      dividerColor: Colors.transparent,
                      // indicatorSize: TabBarIndicatorSize.label,
                      // indicatorWeight: 2,
                      indicator: const BoxDecoration(
                          color: Colors.transparent,
                          // borderSide: BorderSide(width: 2.0),
                          // insets: EdgeInsets.symmetric(horizontal: 16.0),
                          borderRadius: BorderRadius.all(Radius.circular(2))),
                      // labelStyle: MFont.medium16.apply(color: MColor.xFF000000),
                      // unselectedLabelStyle: MFont.regular14.apply(color: MColor.xFF383838),
                      tabs: List<Container>.generate(
                          5,
                          (index) => Container(
                              padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 8),
                              decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.all(Radius.circular(6)),
                                  color: _tabController.index == index ? MColor.xFFFFFFFF : Colors.transparent),
                              child: Tab(
                                  iconMargin: EdgeInsets.zero,
                                  height: 28,
                                  child: Text(
                                    'tab${index}',
                                    style: TextStyle(color: _tabController.index == index ? MColor.xFFF0F0F0 : MColor.skin),
                                  )))).toList()),
                ),
              ],
            ),
          ),
        ),
        TabBarView(
          physics: const NeverScrollableScrollPhysics(),
          controller: _tabController,
          children: _tabPages,
        ),
      ],
    );
  }
}
