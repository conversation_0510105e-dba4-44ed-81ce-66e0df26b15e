import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/settings/setting_model.dart';
import 'package:qiazhun/modules/settings/setting_store.dart';
import 'package:qiazhun/router/router.dart';

class IconListPanel extends StatefulWidget {
  final List<IconInfo> iconList;
  const IconListPanel(this.iconList, {super.key});

  @override
  State<StatefulWidget> createState() => _IconListState();
}

class _IconListState extends State<IconListPanel> {
  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: MColor.xFFF5F5F5,
          title: Text('选择图标', style: TextStyle(height: 1.4, fontSize: 16, color: MColor.xFF1B1C1A)),
          scrolledUnderElevation: 0,
          centerTitle: true,
          automaticallyImplyLeading: false,
          actions: [
            IconButton(
                onPressed: () {
                  RouterHelper.router.pop();
                },
                icon: Icon(Icons.close, size: 22, color: MColor.xFF999999))
          ],
        ),
        backgroundColor: MColor.xFFF5F5F5,
        body: SafeArea(
          child: Container(
            // color: Colors.yellow,
            child: SingleChildScrollView(
              child: GridView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 5,
                  crossAxisSpacing: 0,
                  mainAxisSpacing: 14,
                ),
                itemCount: widget.iconList.length,
                itemBuilder: (context, index) {
                  IconInfo icon = widget.iconList[index];
                  return GestureDetector(
                    onTap: () {
                      // RouterHelper.router.pushNamed(items[index]['path']);
                      RouterHelper.router.pop({'result': icon});
                    },
                    child: Container(
                      // color: Colors.yellow,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.all(Radius.circular(20)),
                            child: CachedNetworkImage(width: 32, height: 32, imageUrl: getImageUrl(icon.icon ?? '')),
                          ),
                          SizedBox(height: 4),
                          Text(
                            icon.name ?? '',
                            style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF1B1C1A),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            // child: ListView.builder(itemBuilder: (context, index) {
            //   return GestureDetector(
            //     onTap: () {
            //       // RouterHelper.router.pushNamed(items[index]['path']);
            //     },
            //     child: Container(
            //       // color: Colors.yellow,
            //       child: Column(
            //         mainAxisAlignment: MainAxisAlignment.center,
            //         children: [
            //           Image.asset('assets/images/ic_default_ledger.png', width: 32, height: 32, fit: BoxFit.fill),
            //           SizedBox(height: 4),
            //           Text(
            //             'hhhh',
            //             style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF1B1C1A),
            //           ),
            //         ],
            //       ),
            //     ),
            //   );
            // })
          ),
        ),
      ),
    );
  }
}
