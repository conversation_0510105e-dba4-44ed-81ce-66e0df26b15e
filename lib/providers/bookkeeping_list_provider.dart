// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
// import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
// import 'package:qiazhun/tools/tools.dart';

// // BookkeepingList 状态类
// class BookkeepingListState {
//   final List<BookkeepingInfo> bookkeepingList;
//   final bool isLoading;
//   final String? error;

//   const BookkeepingListState({
//     this.bookkeepingList = const [],
//     this.isLoading = false,
//     this.error,
//   });

//   BookkeepingListState copyWith({
//     List<BookkeepingInfo>? bookkeepingList,
//     bool? isLoading,
//     String? error,
//   }) {
//     return BookkeepingListState(
//       bookkeepingList: bookkeepingList ?? this.bookkeepingList,
//       isLoading: isLoading ?? this.isLoading,
//       error: error,
//     );
//   }
// }

// // BookkeepingList StateNotifier
// class BookkeepingListNotifier extends StateNotifier<BookkeepingListState> {
//   BookkeepingListNotifier() : super(const BookkeepingListState());

//   // 获取记账本列表
//   Future<void> getBookkeepingList() async {
//     logger.i('getBookkeepingList');
    
//     state = state.copyWith(isLoading: true, error: null);
    
//     try {
//       var resp = await BookkeepingRepo.getBookkeepingList();
//       if (resp.code == 1) {
//         final newList = List<BookkeepingInfo>.from(resp.data ?? []);
        
//         List<String> debugIdList = List.generate(newList.length, (index) {
//           return '${newList[index].bookkeepingId}';
//         });
        
//         debugPrint('_sortBookkeepings after ${debugIdList.join(',')}');
        
//         state = state.copyWith(
//           bookkeepingList: newList,
//           isLoading: false,
//           error: null,
//         );
//       } else {
//         state = state.copyWith(
//           isLoading: false,
//           error: resp.msg ?? 'Unknown error',
//         );
//         showToast(resp.msg ?? '');
//       }
//     } catch (e) {
//       logger.e('getBookkeepingList error $e');
//       state = state.copyWith(
//         isLoading: false,
//         error: e.toString(),
//       );
//     }
//   }

//   // 刷新记账本列表
//   Future<void> refreshBookkeepingList() async {
//     await getBookkeepingList();
//   }

//   // 根据 bookkeepingNumber 查找记账本
//   BookkeepingInfo? findByBookkeepingNumber(String bookkeepingNumber) {
//     try {
//       return state.bookkeepingList.firstWhere(
//         (element) => element.bookkeepingNumber == bookkeepingNumber,
//       );
//     } catch (e) {
//       return null;
//     }
//   }

//   // 清空错误状态
//   void clearError() {
//     state = state.copyWith(error: null);
//   }
// }

// // BookkeepingList Provider
// final bookkeepingListProvider = StateNotifierProvider<BookkeepingListNotifier, BookkeepingListState>((ref) {
//   return BookkeepingListNotifier();
// });

// // 便捷的 Provider，只返回 bookkeepingList
// final bookkeepingListDataProvider = Provider<List<BookkeepingInfo>>((ref) {
//   return ref.watch(bookkeepingListProvider).bookkeepingList;
// });

// // 便捷的 Provider，返回加载状态
// final bookkeepingListLoadingProvider = Provider<bool>((ref) {
//   return ref.watch(bookkeepingListProvider).isLoading;
// });

// // 便捷的 Provider，返回错误状态
// final bookkeepingListErrorProvider = Provider<String?>((ref) {
//   return ref.watch(bookkeepingListProvider).error;
// });
