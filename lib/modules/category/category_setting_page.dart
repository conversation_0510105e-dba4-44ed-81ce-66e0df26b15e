import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_connect/http/src/utils/utils.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/round_image.dart';

class CategorySettingPage extends StatefulWidget {
  final int initialTab;
  const CategorySettingPage({this.initialTab = 0, super.key});

  @override
  State<StatefulWidget> createState() => _CategorySettingState();
}

class _CategorySettingState extends State<CategorySettingPage> with TickerProviderStateMixin {
  late TabController _tabController;
  late List<Widget> _tabPages = [_CategoryTab(items: []), _CategoryTab(items: [])];
  final List<String> _tabNames = [
    '支出',
    '收入',
  ];

  int _tabIndex = 0;

  @override
  void initState() {
    _tabController = TabController(initialIndex: widget.initialTab, length: _tabNames.length, vsync: this);
    _tabController.addListener(() {
      logger.i('_CategorySettingState change tab ${_tabController.index}');
      setState(() {
        _tabIndex = _tabController.index;
      });
    });
    _tabIndex = widget.initialTab;
    super.initState();
    _initData();
  }

  Future<void> _initData() async {
    Loading.show();
    await BookkeepingStore.to.getMyBookkeepingCategory();
    Loading.dismiss();
    _tabPages = [
      _CategoryTab(
        items: BookkeepingStore.to.outcomeCategories,
        moreItems: BookkeepingStore.to.officialOutcomes,
        onDelCategory: (categoryId) {
          showCustomDialog(
            '确认删除',
            content: '删除后不可恢复，请确认是否删除',
            cancel: true,
            onConfirm: () async {
              _delCategory(categoryId);
            },
            onCancel: () {},
          );
        },
        onAddCategory: (categoryId) {
          _addCategory(categoryId);
        },
        onSort: (sortedIdList) {
          _sortCategory('1', sortedIdList);
        },
      ),
      _CategoryTab(
        items: BookkeepingStore.to.incomeCategories,
        moreItems: BookkeepingStore.to.officialIncomes,
        onDelCategory: (categoryId) {
          showCustomDialog(
            '确认删除',
            content: '删除后不可恢复，请确认是否删除',
            cancel: true,
            onConfirm: () async {
              _delCategory(categoryId);
            },
            onCancel: () {},
          );
        },
        onAddCategory: (categoryId) {
          _addCategory(categoryId);
        },
        onSort: (sortedIdList) {
          _sortCategory('2', sortedIdList);
        },
      ),
    ];
    setState(() {});
  }

  Future<void> _delCategory(String cateId) async {
    Loading.show();
    try {
      var resp = await BookkeepingRepo.delBookkeepingCategory(cateId);
      if (resp.code == 1) {
        await _initData();
      } else {
        showToast(resp.msg ?? '删除失败');
      }
    } catch (e) {
      showToast('删除失败');
    } finally {
      Loading.dismiss();
    }
  }

  Future<void> _addCategory(String cateId) async {
    Loading.show();
    try {
      var resp = await BookkeepingRepo.addOfficialCategory(cateId);
      if (resp.code == 1) {
        await _initData();
      } else {
        showToast(resp.msg ?? '添加失败');
      }
    } catch (e) {
      showToast('添加失败');
    } finally {
      Loading.dismiss();
    }
  }

  Future<void> _sortCategory(String categoryType, String sortedIdList) async {
    Loading.show();
    try {
      var resp = await BookkeepingRepo.sortBookkeepingCategory(categoryType, sortedIdList);
      if (resp.code == 1) {
        await _initData();
      } else {
        showToast(resp.msg ?? '删除失败');
      }
    } catch (e) {
    } finally {
      Loading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        dividerTheme: DividerThemeData(
          color: MColor.xFFEEEEEE,
        ),
      ),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        extendBody: true,
        persistentFooterButtons: [_bottomView],
        body: SafeArea(
          top: false,
          bottom: false,
          child: Container(
            color: Colors.yellow,
            child: Stack(
              children: [
                Container(
                  color: const Color(0xFFF5F5F5),
                ),
                Positioned(
                  // top: 0,
                  child: Container(
                    height: 203,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                          colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                    ),
                  ),
                ),
                Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        AppBar(
                          backgroundColor: Colors.transparent,
                          scrolledUnderElevation: 0,
                          centerTitle: true,
                          leading: IconButton(
                            icon: Image.asset(
                              'assets/images/ic_back.png',
                              width: 24,
                              height: 24,
                            ),
                            onPressed: () {
                              RouterHelper.router.pop();
                            },
                          ),
                          titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                          title: Text(
                            '类别设置',
                            style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                          ),
                        ),
                        SizedBox(
                          height: 30,
                          child: Row(
                            children: [
                              const SizedBox(
                                width: 30,
                              ),
                              Expanded(
                                child: Container(
                                  child: TabBar(
                                      physics: const NeverScrollableScrollPhysics(),
                                      // padding: EdgeInsets.only(bottom: 20),
                                      // indicatorPadding: EdgeInsets.only(top: 20),
                                      labelPadding: EdgeInsets.symmetric(horizontal: 0.0),
                                      controller: _tabController,
                                      indicatorSize: TabBarIndicatorSize.tab,
                                      // indicatorWeight: 1,
                                      // isScrollable: true,
                                      // tabAlignment: TabAlignment.start,
                                      dividerColor: Colors.transparent,
                                      indicatorColor: Colors.transparent,
                                      labelStyle: MFont.medium14.apply(color: MColor.xFF000000),
                                      tabs: List<Tab>.generate(
                                          _tabPages.length,
                                          (index) => Tab(
                                              iconMargin: EdgeInsets.zero,
                                              child: Container(
                                                height: 40,
                                                decoration: BoxDecoration(
                                                    border: Border(
                                                        bottom: BorderSide(color: MColor.skin, width: 1),
                                                        top: BorderSide(color: MColor.skin, width: 1),
                                                        left: index == 1 ? BorderSide.none : BorderSide(color: MColor.skin, width: 1),
                                                        right: BorderSide(color: MColor.skin, width: 1)),
                                                    borderRadius: BorderRadius.only(
                                                      topLeft: index == 0 ? Radius.circular(4) : Radius.zero,
                                                      topRight: index == 1 ? Radius.circular(4) : Radius.zero,
                                                      bottomLeft: index == 0 ? Radius.circular(4) : Radius.zero,
                                                      bottomRight: index == 1 ? Radius.circular(4) : Radius.zero,
                                                    ),
                                                    color: _tabIndex == index ? MColor.skin : MColor.xFFFFFFFF),
                                                child: Row(
                                                  mainAxisAlignment: MainAxisAlignment.center,
                                                  children: [
                                                    Center(
                                                      child: Text(
                                                        _tabNames[index],
                                                        style: TextStyle(color: _tabIndex == index ? (MColor.xFFFFFFFF) : (MColor.skin)),
                                                        // TextStyle(color: _tabIndex == index ? (Colors.yellow) : (Colors.blue)),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ))).toList()),
                                ),
                              ),
                              const SizedBox(
                                width: 30,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 12,
                        )
                      ],
                    )),
                Positioned.fill(
                    top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top + 42,
                    // top: 0,
                    child: GetBuilder<BookkeepingStore>(builder: (store) {
                      return DefaultTabController(
                        length: _tabNames.length,
                        child: Container(
                          decoration: BoxDecoration(color: MColor.xFFFFFFFF, border: Border(bottom: BorderSide(color: MColor.xFFF0F0F0))),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: TabBarView(
                                  children: _tabPages,
                                  controller: _tabController,
                                ),
                              )
                            ],
                          ),
                        ),
                      );
                    }))
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget get _bottomView {
    return ElevatedButton(
        style: ElevatedButton.styleFrom(backgroundColor: MColor.skin, elevation: 10),
        onPressed: () {
          RouterHelper.router.pushNamed(Routes.categoryListPath, pathParameters: {'categoryType': _tabIndex == 0 ? '1' : '2'}).then((value) {
            if (value == 1) {
              _initData();
            }
          });
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_circle,
              size: 16,
              color: MColor.xFFFFFFFF,
            ),
            const SizedBox(
              width: 4,
            ),
            Text(
              '添加类别',
              style: TextStyle(height: 1.4, fontSize: 14, color: MColor.xFFFFFFFF),
            ),
          ],
        ));
  }
}

class _CategoryTab extends StatefulWidget {
  final List<CategoryItem> items;
  final List<CategoryItem>? moreItems;
  final Function(String categoryId)? onDelCategory;
  final Function(String categoryId)? onAddCategory;
  final Function(String sortedIdList)? onSort;

  const _CategoryTab({super.key, required this.items, this.moreItems, this.onDelCategory, this.onAddCategory, this.onSort});

  @override
  State<StatefulWidget> createState() => _CategoryTabState();
}

class _CategoryTabState extends State<_CategoryTab> with AutomaticKeepAliveClientMixin {
  List<CategoryItem> _tmpItems = [];

  @override
  void initState() {
    logger.i('_CategoryTabState initState');
    _tmpItems = widget.items;
    super.initState();
  }

  @override
  void didUpdateWidget(covariant _CategoryTab oldWidget) {
    logger.i('_CategoryTabState didUpdateWidget');
    _tmpItems = widget.items;
    super.didUpdateWidget(oldWidget);
  }

  @override
  void didChangeDependencies() {
    logger.i('_CategoryTabState didChangeDependencies');
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    if (widget.items.isEmpty) {
      return EmptyView();
    } else {
      List<Widget> widgets = List.generate(_tmpItems.length, (index) {
        return _itemView(index, _tmpItems[index]);
      });
      if (widget.moreItems?.isNotEmpty == true) {
        List<Widget> moreItems = List.generate(widget.moreItems!.length, (index) {
          return _moreItemView(index, widget.moreItems![index]);
        });
        widgets.add(Container(
          key: ValueKey('more-separator'),
          padding: EdgeInsets.only(top: 8, left: 14, right: 14, bottom: 4),
          color: MColor.xFFEEEEEE,
          child: Text(
            '更多分类',
            style: TextStyle(height: 1.4, fontSize: 13, color: MColor.xFF1B1C1A),
          ),
        ));
        widgets.addAll(moreItems);
      }
      // if (BookkeepingStore.to.allOutcomeCategories.isNotEmpty == true) {
      //   List<Widget> moreItems = List.generate(BookkeepingStore.to.allOutcomeCategories.length, (index) {
      //     return _itemView(index, BookkeepingStore.to.allOutcomeCategories[index]);
      //   });
      //   widgets.addAll(moreItems);
      // }
      return Column(
        children: [
          Expanded(
            child: ReorderableListView(
              buildDefaultDragHandles: false,
              onReorder: (oldIndex, newIndex) {
                logger.i('old $oldIndex new $newIndex');
                setState(() {
                  if (oldIndex < newIndex) {
                    newIndex -= 1;
                  }
                  final item = _tmpItems.removeAt(oldIndex);
                  _tmpItems.insert(newIndex, item);
                  List<String> idList = List.generate(_tmpItems.length, (index) {
                    return '${_tmpItems[index].bookkeepingCategoryId}';
                  });
                  widget.onSort?.call(idList.join(','));
                });
              },
              shrinkWrap: true,
              children: widgets,
            ),
          ),
          const SizedBox(
            height: 70,
          )
        ],
      );
    }
  }

  @override
  void dispose() {
    logger.i('_CategoryTabState dispose');
    super.dispose();
  }

  Widget _moreItemView(int index, CategoryItem item) {
    return ListTile(
      key: ValueKey('${item.bookkeepingCategoryId}-$index'),
      dense: true,
      horizontalTitleGap: 4,
      contentPadding: EdgeInsets.symmetric(horizontal: 2, vertical: 4),
      leading: IconButton(
          onPressed: () async {
            widget.onAddCategory?.call('${item.bookkeepingCategoryId}');
          },
          icon: Icon(Icons.add_circle_outline, color: MColor.skin, size: 20)),
      title: Row(
        children: [
          RoundImage(imageUrl: getImageUrl(item.bookkeepingCategoryIcon ?? ''), radius: 17, size: 34),
          const SizedBox(
            width: 8,
          ),
          Text(
            item.bookkeepingCategoryName ?? '',
            style: TextStyle(height: 1.4, color: MColor.xFF1B1C1A, fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _itemView(int index, CategoryItem item) {
    return ListTile(
      key: ValueKey('${item.bookkeepingCategoryId}-$index'),
      dense: true,
      horizontalTitleGap: 4,
      contentPadding: EdgeInsets.symmetric(horizontal: 2, vertical: 4),
      leading: IconButton(
          onPressed: () async {
            widget.onDelCategory?.call('${item.bookkeepingCategoryId}');
          },
          icon: Icon(Icons.do_not_disturb_on_outlined, color: MColor.xFFCB322E, size: 20)),
      title: Row(
        children: [
          RoundImage(imageUrl: getImageUrl(item.bookkeepingCategoryIcon ?? ''), radius: 17, size: 34),
          const SizedBox(
            width: 8,
          ),
          Text(
            item.bookkeepingCategoryName ?? '',
            style: TextStyle(height: 1.4, color: MColor.xFF1B1C1A, fontSize: 14),
          ),
        ],
      ),
      trailing: ReorderableDragStartListener(
        index: index,
        child: Container(
            decoration: BoxDecoration(border: Border.all(width: 1, color: Colors.transparent), borderRadius: BorderRadius.circular(12)),
            height: 32,
            width: 32,
            child: Icon(Icons.menu, size: 16, color: MColor.skin)),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
