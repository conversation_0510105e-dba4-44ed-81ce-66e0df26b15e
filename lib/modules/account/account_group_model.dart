// import 'package:json_annotation/json_annotation.dart';
// import 'package:qiazhun/modules/account/account_model.dart';

// part 'account_group_model.g.dart';

// @JsonSerializable()
// class AccountGroupModel extends Object {
//   @J<PERSON><PERSON><PERSON>(name: 'id')
//   int? id;

//   @J<PERSON><PERSON><PERSON>(name: 'name')
//   String? name;

//   @Json<PERSON>ey(name: 'sort')
//   int? sort;

//   @Json<PERSON>ey(name: 'isCustom')
//   bool? isCustom;

//   @Json<PERSON>ey(name: 'accounts')
//   List<AccountModel>? accounts;

//   AccountGroupModel({
//     this.id,
//     this.name,
//     this.sort,
//     this.isCustom,
//     this.accounts,
//   });

//   factory AccountGroupModel.fromJson(Map<String, dynamic> srcJson) => _$AccountGroupModelFromJson(srcJson);

//   Map<String, dynamic> toJson() => _$AccountGroupModelToJson(this);

//   // Helper method to check if group has accounts
//   bool get hasAccounts => accounts?.isNotEmpty == true;

//   // Helper method to get account count
//   int get accountCount => accounts?.length ?? 0;
// }

// @JsonSerializable()
// class AccountGroupListResponse extends Object {
//   @JsonKey(name: 'groups')
//   List<AccountGroupModel>? groups;

//   AccountGroupListResponse({this.groups});

//   factory AccountGroupListResponse.fromJson(Map<String, dynamic> srcJson) => _$AccountGroupListResponseFromJson(srcJson);

//   Map<String, dynamic> toJson() => _$AccountGroupListResponseToJson(this);
// }
