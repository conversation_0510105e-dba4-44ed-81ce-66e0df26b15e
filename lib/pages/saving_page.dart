import 'package:flutter/material.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/home_detail_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/detail_tab/detail_repo.dart';
import 'package:qiazhun/modules/transaction_item_view.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/loading_view.dart';

class SavingPage extends StatefulWidget {
  final int pageType; // 0 节省 1 非必要
  const SavingPage({required this.pageType, super.key});

  @override
  State<StatefulWidget> createState() => _SavingState();
}

class _SavingState extends State<SavingPage> {
  bool _isLoading = true;

  final List<TransactionItem> _dataList = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    _isLoading = true;
    Loading.show();
    try {
      var resp = await DetailRepo.search(action: widget.pageType == 0 ? '2' : '3');
      if (resp.code == 1) {
        _dataList.clear();
        _dataList.addAll(resp.data ?? []);
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      Loading.dismiss();
    }
    _isLoading = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: SafeArea(
        top: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        widget.pageType == 0 ? '节省' : '非必要',
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: Builder(builder: (context) {
                    if (_isLoading) {
                      return LoadingView();
                    }
                    if (_dataList.isEmpty) {
                      return EmptyView();
                    }
                    return Column(
                      children: [
                        _headerView,
                        Expanded(
                          child: ListView.separated(
                              padding: EdgeInsets.zero,
                              itemBuilder: (context, index) {
                                return Container(
                                    color: MColor.xFFF5F5F5,
                                    child: TransactionItemView(_dataList[index], actions: [
                                      TransactionItemAction(
                                        label: '删除',
                                        icon: Icons.delete,
                                        bgColor: MColor.xFFFF7858,
                                        fgColor: MColor.xFFFFFFFF,
                                        onPressed: () {
                                          showCustomDialog(
                                            '确认删除',
                                            content: '删除后不可恢复，请确认是否删除',
                                            cancel: true,
                                            onConfirm: () async {
                                              Loading.show();
                                              try {
                                                var resp = await BookkeepingRepo.deleteBill('${_dataList[index].id}');
                                                if (resp.code == 1) {
                                                  showToast('删除成功');
                                                  _loadData();
                                                } else {
                                                  showToast(resp.msg ?? '删除失败');
                                                }
                                              } catch (e) {
                                                showToast('删除失败 $e');
                                              } finally {
                                                Loading.dismiss();
                                              }
                                            },
                                            onCancel: () {},
                                          );
                                        },
                                      ),
                                      TransactionItemAction(
                                        bgColor: MColor.xFFFFBE4A,
                                        fgColor: MColor.xFFFFFFFF,
                                        icon: Icons.edit,
                                        label: '编辑',
                                        onPressed: () async {
                                          RouterHelper.router.pushNamed(Routes.bookkeepingPath, extra: {'logId': '${_dataList[index].id}'}).then((_) {
                                            _loadData();
                                          });
                                        },
                                      )
                                    ]));
                              },
                              separatorBuilder: (context, index) {
                                return Divider(
                                  height: 0.5,
                                  thickness: 0.5,
                                  color: MColor.xFFD9D9D9,
                                  indent: 15,
                                );
                              },
                              itemCount: _dataList.length),
                        ),
                      ],
                    );
                  }))
            ],
          ),
        ),
      ),
    );
  }

  Widget get _headerView {
    return Padding(
      padding: EdgeInsets.fromLTRB(14, 8, 14, 14),
      child: Row(
        children: [
          Text(
            '账目明细',
            style: TextStyle(fontSize: 17, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A),
          ),
          const SizedBox(
            width: 12,
          ),
          const Spacer(),
          Text('共${_dataList.length}笔', style: TextStyle(fontSize: 14, height: 1.4, fontWeight: FontWeight.w400, color: MColor.xFFCB322E)),
        ],
      ),
    );
  }
}
