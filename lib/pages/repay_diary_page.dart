import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/mine_tab/user_repo.dart';
import 'package:qiazhun/modules/transaction_item_view.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/loading_view.dart';
import 'package:qiazhun/widgets/round_image.dart';

class RepayDiaryPage extends StatefulWidget {
  const RepayDiaryPage({super.key});

  @override
  State<StatefulWidget> createState() => _RepayDiaryState();
}

class _RepayDiaryState extends State<RepayDiaryPage> {
  bool _isLoading = true;

  final List _dataList = <RepayDiaryModel>[];
  final List _paidList = <RepayDiaryModel>[];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    _isLoading = true;
    Loading.show();

    try {
      var resp = await UserRepo.getRepaymentDiary();
      if (resp.code == 1) {
        _dataList.clear();
        _paidList.clear();
        _dataList.addAll(resp.data?.noRepayment ?? []);
        _paidList.addAll(resp.data?.isRepayment ?? []);
      } else {
        showToast(resp.msg ?? '数据获取失败');
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace, maxFrames: 5);
      showToast('数据获取失败 $e');
    } finally {
      Loading.dismiss();
      _isLoading = false;
      setState(() {});
    }
    // _dataList.clear();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: SafeArea(
        top: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        '还款便签',
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: Builder(builder: (context) {
                    if (_isLoading) {
                      return LoadingView();
                    }
                    if (_dataList.isEmpty && _paidList.isEmpty) {
                      return EmptyView();
                    }
                    bool showEmptyNoPaid = _dataList.isEmpty;
                    int itemCount = (_dataList.isEmpty ? 1 : _dataList.length) + _paidList.length;
                    List<Widget> widgets = [];
                    if (showEmptyNoPaid) {
                      widgets.add(SizedBox(
                          height: 80,
                          child: Center(
                              child: Text(
                            '暂无待还卡片',
                            style: TextStyle(height: 1.4, fontSize: 16, fontWeight: FontWeight.w500, color: MColor.skin),
                          ))));
                    } else {
                      widgets.addAll(List.generate(_dataList.length, (index) {
                        return Container(color: MColor.xFFF5F5F5, child: _itemView(_dataList[index], false));
                      }));
                    }
                    widgets.addAll(List.generate(_paidList.length, (index) {
                      return Container(color: MColor.xFFF5F5F5, child: _itemView(_paidList[index], true));
                    }));
                    return ListView.separated(
                        padding: EdgeInsets.zero,
                        itemBuilder: (context, index) {
                          return widgets[index];
                        },
                        separatorBuilder: (context, index) {
                          if (index == _dataList.length - 1 || (index == 0 && showEmptyNoPaid)) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 8.0),
                              child: Image.asset(
                                'assets/images/bg_separator_repay_diary.png',
                                width: 343,
                                height: 31,
                              ),
                            );
                          }
                          return const SizedBox(
                            height: 13,
                          );
                        },
                        itemCount: itemCount);
                  }))
            ],
          ),
        ),
      ),
    );
  }

  Widget _itemView(RepayDiaryModel data, bool isPaid) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 14),
      padding: EdgeInsets.all(18),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: Color(int.tryParse(data?.bankColour ?? '', radix: 16) ?? MColor.skin.value)),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              RoundImage(imageUrl: getImageUrl(data?.bankIcon), radius: 12, size: 24),
              const SizedBox(
                width: 10,
              ),
              Text(
                '${data.bankName ?? ''}',
                style: TextStyle(height: 1.4, fontSize: 15, fontWeight: FontWeight.bold, color: MColor.xFFFFFFFF),
              ),
              const Spacer(),
              const SizedBox(
                height: 4,
              ),
              Text(
                '还款日 ${data?.repaymentDate ?? ''}',
                style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF99CEC2),
              ),
            ],
          ),
          const SizedBox(
            height: 14,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Text(
                    '${data.residue}',
                    style: TextStyle(height: 1.4, fontSize: 12, fontWeight: FontWeight.bold, color: MColor.xFFFFFFFF),
                  ),
                  const SizedBox(
                    width: 8,
                  ),
                  Text(
                    '¥${data.issuedBill}',
                    style: TextStyle(height: 1.4, fontSize: 20, color: MColor.xFFFFFFFF),
                  ),
                ],
              ),
              const Spacer(),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () async {
                  if (isPaid) {
                    RouterHelper.router.pushNamed(Routes.repayBillDialogPath).then((result) async {
                      if (result != null && result is Map && result.containsKey('price') && result['price']?.isNotEmpty == true) {
                        await _switchPaidStatus('${data.id}', '2', issuedBill: result['price']!);
                        _loadData();
                      }
                    });
                  } else {
                    await _switchPaidStatus('${data.id}', '1');
                    _loadData();
                  }
                },
                child: Row(
                  children: [
                    Image.asset(
                      isPaid ? 'assets/images/ic_repayment_paid.png' : 'assets/images/ic_repayment_unpaid.png',
                      width: 20,
                      height: 20,
                      fit: BoxFit.scaleDown,
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Text(
                      isPaid ? '已还' : '未还',
                      style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFFFFFFFF),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                width: 14,
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  RouterHelper.router.pushNamed(Routes.editAccountPath, extra: {'accountId': data.id}).then((value) {
                    _loadData();
                  });
                },
                child: Row(
                  children: [
                    Image.asset(
                      'assets/images/ic_repayment_manage.png',
                      width: 18,
                      height: 18,
                      fit: BoxFit.scaleDown,
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Text(
                      '管理',
                      style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFFFFFFFF),
                    ),
                  ],
                ),
              )
            ],
          )
        ],
      ),
    );
  }

  Future<void> _switchPaidStatus(String accountId, String type, {String? issuedBill}) async {
    Loading.show();
    try {
      var resp = await UserRepo.switchRepayStatus(accountId, type, issuedBill: issuedBill);
      if (resp.code == 1) {
        _loadData();
        showToast('操作成功');
      } else {
        showToast(resp.msg ?? '操作失败');
      }
    } catch (e) {
      showToast('操作失败 $e');
    } finally {
      Loading.dismiss();
    }
  }
}

class RepayBillDialog extends StatefulWidget {
  const RepayBillDialog({super.key});

  @override
  State<StatefulWidget> createState() => _RepayBillState();
}

class _RepayBillState extends State<RepayBillDialog> {
  final TextEditingController _textEditingController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(14),
      decoration: BoxDecoration(
          color: MColor.xFFFFFFFF,
          borderRadius: BorderRadius.circular(28),
          image: DecorationImage(
            image: AssetImage(
              "assets/images/mine_bg.png",
            ),
            fit: BoxFit.cover,
          )),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '输入金额',
            style: TextStyle(color: MColor.xFF1B1C1A, fontSize: 16, height: 1.4),
          ),
          const SizedBox(
            height: 24,
          ),
          TextField(
            controller: _textEditingController,
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
            decoration: InputDecoration(
              hintText: '输入金额',
              hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 15),
              isDense: true,
            ),
          ),
          const SizedBox(
            height: 24,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              GestureDetector(
                onTap: () {
                  RouterHelper.router.pop();
                },
                child: Container(
                    height: 36,
                    padding: EdgeInsets.symmetric(horizontal: 18),
                    decoration: BoxDecoration(color: MColor.xFFE8E8E8, borderRadius: BorderRadius.circular(10)),
                    child: Center(
                      child: Text(
                        '取消',
                        style: TextStyle(fontSize: 14, color: MColor.xFF999999),
                      ),
                    )),
              ),
              const SizedBox(
                width: 14,
              ),
              GestureDetector(
                onTap: () {
                  RouterHelper.router.pop({'price': _textEditingController.text});
                },
                child: Container(
                    height: 36,
                    padding: EdgeInsets.symmetric(horizontal: 18),
                    decoration: BoxDecoration(color: MColor.skin, borderRadius: BorderRadius.circular(10)),
                    child: Center(
                      child: Text(
                        '保存',
                        style: TextStyle(fontSize: 14, color: MColor.xFFFFFFFF),
                      ),
                    )),
              )
            ],
          )
        ],
      ),
    );
  }

  // Future<void> _addSaving(String price) async {
  //   if (price.isEmpty) {
  //     showToast('请填写有效的金额');
  //     return;
  //   }
  //   Loading.show();
  //   try {
  //     var resp = await PlanRepo.manualSave(widget.planId, price);
  //     if (resp.code == 1) {
  //       showToast('操作成功');
  //       RouterHelper.router.pop();
  //     } else {
  //       showToast(resp.msg ?? '');
  //     }
  //   } catch (e) {
  //     showToast(e.toString());
  //   } finally {
  //     Loading.dismiss();
  //   }
  // }
}
