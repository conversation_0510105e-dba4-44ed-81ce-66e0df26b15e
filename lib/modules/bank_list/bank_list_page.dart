import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/round_image.dart';

class BankListPage extends StatefulWidget {
  final bool onlyBank;
  const BankListPage({required this.onlyBank, super.key});
  @override
  State<StatefulWidget> createState() => _BankListState();
}

class _BankListState extends State<BankListPage> {
  final List<BankItem> _bankList = [];

  @override
  void initState() {
    _initData();
    super.initState();
  }

  Future<void> _initData() async {
    Loading.show();
    try {
      var resp = await AccountRepo.getBankList();
      if (resp.code == 1) {
        _bankList.clear();
        // Filter banks based on hiddenType and cardType
        _bankList.addAll((resp.data ?? []).where((bank) => !(bank.hiddenType != '1' && widget.onlyBank)).toList());
        setState(() {});
      } else {
        showToast(resp.msg ?? '获取银行列表失败');
      }
    } catch (e) {
      showToast('获取银行列表失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
          backgroundColor: Colors.transparent,
          scrolledUnderElevation: 0,
          centerTitle: true,
          leading: IconButton(
            icon: Image.asset(
              'assets/images/ic_back.png',
              width: 24,
              height: 24,
            ),
            onPressed: () {
              RouterHelper.router.pop();
            },
          ),
          title: Image.asset(
            'assets/images/icon_title.png',
            width: 129,
            height: 30,
          )),
      body: SafeArea(
        top: false,
        child: Stack(
          children: [
            Container(
              color: Color(0xFFF5F5F5),
            ),
            Positioned(
              // top: 0,
              child: Container(
                height: 203,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                      colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                ),
              ),
            ),
            Positioned.fill(
              top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
              child: Container(
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
                padding: EdgeInsets.all(14),
                margin: EdgeInsets.symmetric(horizontal: 14),
                child: GridView.builder(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    mainAxisSpacing: 14,
                    crossAxisSpacing: 14,
                    childAspectRatio: 1.0,
                  ),
                  itemBuilder: (context, index) {
                    return _bankItemView(_bankList[index]);
                  },
                  itemCount: _bankList.length,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _bankItemView(BankItem item) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        RouterHelper.router.pop(item);
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            RoundImage(imageUrl: getImageUrl(item.bankImage), radius: 16, size: 32),
            const SizedBox(height: 8),
            Text(
              item.bankName ?? '',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 12),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
