import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/bookkeeping_controller.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/shortcut_setting_controller.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class ShortcutSettingMenu extends GetView<ShortcutSettingController> {
  const ShortcutSettingMenu({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: Color(0xFFF5F5F5))),
            ),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: const Icon(Icons.close, color: MColor.xFF999999),
                ),
                const Expanded(
                  child: Text(
                    '设置横滑组件',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: MColor.xFF1B1C1A,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    // onReorder(items);
                    Navigator.pop(context);
                  },
                  child: const Text(
                    '完成',
                    style: TextStyle(
                      fontSize: 16,
                      color: MColor.skin,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 提示文字
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              '长按拖动可以调整顺序',
              style: TextStyle(
                fontSize: 14,
                color: MColor.xFF999999,
              ),
            ),
          ),
          // 可拖动列表
          // Expanded(
          //   child: ReorderableListView.builder(
          //     padding: const EdgeInsets.symmetric(horizontal: 16),
          //     itemCount: items.length,
          //     onReorder: (oldIndex, newIndex) {
          //       if (newIndex > oldIndex) {
          //         newIndex -= 1;
          //       }
          //       final item = items.removeAt(oldIndex);
          //       items.insert(newIndex, item);
          //     },
          //     itemBuilder: (context, index) {
          //       final item = items[index];
          //       return Container(
          //         key: ValueKey(item),
          //         margin: const EdgeInsets.only(bottom: 8),
          //         padding: const EdgeInsets.all(16),
          //         decoration: BoxDecoration(
          //           color: const Color(0xFFF8F8F8),
          //           borderRadius: BorderRadius.circular(8),
          //         ),
          //         child: Row(
          //           children: [
          //             Icon(
          //               _getItemIcon(item),
          //               color: MColor.skin,
          //               size: 20,
          //             ),
          //             const SizedBox(width: 12),
          //             Expanded(
          //               child: Text(
          //                 _getItemName(item),
          //                 style: const TextStyle(
          //                   fontSize: 16,
          //                   color: MColor.xFF1B1C1A,
          //                 ),
          //               ),
          //             ),
          //             const Icon(
          //               Icons.drag_handle,
          //               color: MColor.xFF999999,
          //             ),
          //           ],
          //         ),
          //       );
          //     },
          //   ),
          // ),
        ],
      ),
    );
  }
}
