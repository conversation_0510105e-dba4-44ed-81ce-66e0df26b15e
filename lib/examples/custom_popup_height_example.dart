import 'package:flutter/material.dart';
import 'package:qiazhun/router/router.dart';

/// CustomPopupPanel 高度设置示例
/// 
/// 使用方法：
/// 1. 默认高度（屏幕的一半）
/// 2. 三分之一高度
/// 3. 三分之二高度
/// 4. 自定义高度
class CustomPopupHeightExample extends StatelessWidget {
  const CustomPopupHeightExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('CustomPopup 高度设置示例'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'CustomPopupPanel 高度设置示例',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            // 默认高度（一半）
            ElevatedButton(
              onPressed: () => _showPopup(
                context,
                title: '默认高度（1/2屏幕）',
                heightFraction: null, // 使用默认值 0.5
              ),
              child: const Text('显示默认高度弹窗（1/2屏幕）'),
            ),
            const SizedBox(height: 10),
            
            // 三分之一高度
            ElevatedButton(
              onPressed: () => _showPopup(
                context,
                title: '三分之一高度',
                heightFraction: 1/3, // 0.33
              ),
              child: const Text('显示三分之一高度弹窗'),
            ),
            const SizedBox(height: 10),
            
            // 三分之二高度
            ElevatedButton(
              onPressed: () => _showPopup(
                context,
                title: '三分之二高度',
                heightFraction: 2/3, // 0.67
              ),
              child: const Text('显示三分之二高度弹窗'),
            ),
            const SizedBox(height: 10),
            
            // 四分之三高度
            ElevatedButton(
              onPressed: () => _showPopup(
                context,
                title: '四分之三高度',
                heightFraction: 0.75,
              ),
              child: const Text('显示四分之三高度弹窗'),
            ),
            const SizedBox(height: 10),
            
            // 最小高度
            ElevatedButton(
              onPressed: () => _showPopup(
                context,
                title: '最小高度',
                heightFraction: 0.25,
              ),
              child: const Text('显示最小高度弹窗（1/4屏幕）'),
            ),
            const SizedBox(height: 20),
            
            const Text(
              '使用说明：',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            const Text(
              '• heightFraction 参数控制弹窗高度\n'
              '• 取值范围：0.25 - 0.95\n'
              '• 默认值：0.5（屏幕的一半）\n'
              '• 常用值：\n'
              '  - 1/3 ≈ 0.33（三分之一）\n'
              '  - 1/2 = 0.5（一半，默认）\n'
              '  - 2/3 ≈ 0.67（三分之二）\n'
              '  - 3/4 = 0.75（四分之三）',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  void _showPopup(BuildContext context, {
    required String title,
    double? heightFraction,
  }) {
    // 创建示例内容
    Widget content = Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Text(
            '这是一个 $title 的弹窗示例',
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          Text(
            'heightFraction: ${heightFraction ?? 0.5}',
            style: const TextStyle(fontSize: 14, color: Colors.grey),
          ),
          const SizedBox(height: 20),
          const Text(
            '这里可以放置任何内容，比如表单、列表、图片等。'
            '弹窗的高度会根据设置的 heightFraction 参数自动调整。',
          ),
          const SizedBox(height: 20),
          Container(
            height: 100,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue),
            ),
            child: const Center(
              child: Text('示例内容区域'),
            ),
          ),
        ],
      ),
    );

    // 使用 CustomPopupPanel
    RouterHelper.router.pushNamed(
      Routes.customPopupPath,
      extra: {
        'title': title,
        'widget': content,
        'heightFraction': heightFraction, // 设置高度比例
        'onConfirm': () async {
          // 确认按钮的回调
          print('确认按钮被点击');
        },
      },
    );
  }
}
