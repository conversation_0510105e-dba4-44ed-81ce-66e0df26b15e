
// Models
import 'package:qiazhun/models/transaction_model.dart';

class FinanceData {
  final double totalAssets;
  final double income;
  final double expenses;
  final double weeklyBudget;
  final double weeklySpent;
  final List<Transaction> transactions;

  FinanceData({
    required this.totalAssets,
    required this.income,
    required this.expenses,
    required this.weeklyBudget,
    required this.weeklySpent,
    required this.transactions,
  });
}
