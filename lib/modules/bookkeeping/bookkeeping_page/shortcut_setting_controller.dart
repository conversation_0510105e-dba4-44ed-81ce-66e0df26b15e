import 'package:get/get_state_manager/src/simple/get_controllers.dart';

class ShortcutSettingController extends GetxController {
  // final List<_ShortcutItem> items = [
  //   _ShortcutItem('date', '日期选择'),
  //   _ShortcutItem('unnecessary', '非必要'),
  //   _ShortcutItem('saving', '节省'),
  //   _ShortcutItem('accounts', '账户'),
  // ];

  // final RxList<_ShortcutItem> selectedItems = RxList<_ShortcutItem>();

  @override
  void onInit() {
    super.onInit();
    // selectedItems.assignAll(items);
  }
}
