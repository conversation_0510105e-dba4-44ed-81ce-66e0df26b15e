

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';


/// Toast工具类
class Toast {


  static void showToast (String? msg){
    if (msg == null) {
      return;
    }
    SmartDialog.showToast(
      msg,
      alignment: Alignment.bottomCenter,
      displayTime: const Duration(milliseconds: 2000),

    );
  }

  static void showError(String? msg, {int duration = 2000}) {
    if (msg == null) {
      return;
    }
    SmartDialog.show(

        displayTime: Duration(milliseconds: duration),
        maskColor: Colors.black.withOpacity(0.5),
      builder: (context) {
        return Center(
          child: Container(
            width: 266.w,
            padding:  EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10.w),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.error_outline, color: const Color(0xffD93B3B), size: 50.sp),
                SizedBox(height: 9.6.w,),
                Text('$msg'),
              ],
            )
          ),
        );
      },
    );
  }
  static Future<void> showSuccess(String? msg, {int duration = 2000}) async {
    if (msg == null) {
      return;
    }
   await SmartDialog.show(

      displayTime: Duration(milliseconds: duration),
      maskColor: Colors.black.withOpacity(0.5),
      builder: (context) {
        return Center(
          child: Container(
              width: 266.w,
              padding:  EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10.w),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.check_circle, color: const Color(0xff52C41A), size: 50.sp),
                  SizedBox(height: 9.6.w,),
                  Text('$msg'),
                ],
              )
          ),
        );
      },
    );
  }

  static void cancelToast() {
    SmartDialog.dismiss();
  }
}
