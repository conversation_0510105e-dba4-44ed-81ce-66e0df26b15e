import 'dart:convert';

import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/modules/settings/setting_model.dart';

class SettingRepo {
  static Future<BaseModel<IconResp>> getIconList() async {
    var resp = await HttpUtil().post('api/index/iconList');
    return BaseModel.fromJson(resp, (json) => IconResp.fromJson(json));
  }

  static Future<BaseModel<dynamic>> feedback(String content, String imageUrl, String phone) async {
    var resp = await HttpUtil().post('api/user/feedback', data: {'content': content, 'image': imageUrl, 'mobile': phone});
    return BaseModel.fromJson(resp, (json) => json);
  }
}
