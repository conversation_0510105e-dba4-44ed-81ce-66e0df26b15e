import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/modules/budget/budget_model.dart';

class BudgetRepo {
  static Future<BaseModel<BudgetIndexResp>> budgetIndex() async {
    var resp = await HttpUtil().post(
      'api/detail/budgetindex',
    );
    return BaseModel.fromJson(resp, (json) => BudgetIndexResp.fromJson(json));
  }

  static Future<BaseModel<dynamic>> budgetSetting(String budgetType, String budgetAmount, String accountBookNumbers) async {
    var resp =
        await HttpUtil().post('api/detail/budgetSet', data: {'budgetType': budgetType, 'budgetAmount': budgetAmount, 'accountBookNumbers': accountBookNumbers});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> budgetSetAmount(String budgetType, String budgetAmount) async {
    var resp = await <PERSON>ttpUtil().post('api/detail/budgetEditAmount', data: {'budgetType': budgetType, 'budgetAmount': budgetAmount});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> budgetSetAccountBooks(String budgetType, String accountBookNumbers) async {
    var resp = await HttpUtil().post('api/detail/budgetEdit', data: {'budgetType': budgetType, 'accountBookNumbers': accountBookNumbers});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> changeAccountModule(dynamic cardType) async {
    var resp = await HttpUtil().post('api/detail/changeAccountModule', data: {'cardType': cardType});
    return BaseModel.fromJson(resp, (json) => json);
  }
}
