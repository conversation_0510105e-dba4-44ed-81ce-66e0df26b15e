import 'package:flutter/material.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_group_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/modules/account/account_item_view.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/page_container_view.dart';

class AccountSelectionPage extends StatefulWidget {
  final int groupId;

  const AccountSelectionPage({required this.groupId, super.key});

  @override
  State<AccountSelectionPage> createState() => _AccountSelectionPageState();
}

class _AccountSelectionPageState extends State<AccountSelectionPage> {
  bool _isLoading = true;
  final List<AccountModel> _allAccounts = [];
  final Set<int> _selectedAccountIds = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });
    Loading.show();
    try {
      // Load all accounts using BookkeepingStore
      await BookkeepingStore.to.getAccountList();
      _allAccounts.clear();
      _allAccounts.addAll(BookkeepingStore.to.accountList);
    } catch (e) {
      showToast('获取账户列表失败 $e');
    } finally {
      Loading.dismiss();
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _addSelectedAccountsToGroup() async {
    if (_selectedAccountIds.isEmpty) {
      showToast('请选择要添加的账户');
      return;
    }

    Loading.show();
    try {
      var resp = await AccountGroupRepo.bindAccountsToGroup(
        groupId: widget.groupId,
        cardIds: _selectedAccountIds.toList(),
      );
      if (resp.code == 1) {
        showToast('添加成功');
        RouterHelper.router.pop();
      } else {
        showToast(resp.msg ?? '添加失败');
      }
    } catch (e) {
      showToast('添加失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
      title: '选择账户',
      actions: [
        TextButton(
          onPressed: _selectedAccountIds.isNotEmpty ? _addSelectedAccountsToGroup : null,
          child: Text(
            '确定',
            style: TextStyle(
              color: _selectedAccountIds.isNotEmpty ? MColor.xFF68C2BF : MColor.xFF999999,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _allAccounts.isEmpty
              ? const Center(
                  child: Text(
                    '暂无账户',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                )
              : ListView.separated(
                  padding: const EdgeInsets.all(14),
                  itemCount: _allAccounts.length,
                  separatorBuilder: (context, index) => const SizedBox(height: 14),
                  itemBuilder: (context, index) {
                    return _buildAccountItem(_allAccounts[index]);
                  },
                ),
    );
  }

  Widget _buildAccountItem(AccountModel account) {
    final isSelected = _selectedAccountIds.contains(account.id);

    return GestureDetector(
      onTap: () {
        setState(() {
          if (isSelected) {
            _selectedAccountIds.remove(account.id);
          } else {
            _selectedAccountIds.add(account.id!);
          }
        });
      },
      child: Container(
        decoration: BoxDecoration(
          color: MColor.xFFFFFFFF,
          borderRadius: BorderRadius.circular(20),
          border: isSelected ? Border.all(color: MColor.xFF68C2BF, width: 2) : null,
        ),
        child: Stack(
          children: [
            AbsorbPointer(child: AccountItemView(account)),
            if (isSelected)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: MColor.xFF68C2BF,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
