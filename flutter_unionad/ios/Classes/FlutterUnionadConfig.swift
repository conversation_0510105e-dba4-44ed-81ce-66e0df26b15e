//
//  File.swift
//  flutter_unionad
//
//  Created by 9Y on 2020/10/15.
//

import Foundation

class FlutterUnionadConfig{
    struct view {
        //BannerAdView
        static let bannerAdView = "com.gstory.flutter_unionad/BannerAdView"
        //SplashAdView
        static let splashAdView = "com.gstory.flutter_unionad/SplashAdView"
        //DrawFeedAdView
        static let drawFeedAdView = "com.gstory.flutter_unionad/DrawFeedAdView"
        //NativeAdView
        static let nativeAdView = "com.gstory.flutter_unionad/NativeAdView"
    }
    
    struct event {
        static let adevent = "com.gstory.flutter_unionad/adevent"
    }
}
