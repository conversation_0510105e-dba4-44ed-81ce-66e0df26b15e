import 'package:flutter/material.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_add_big_assets_section.dart';
import 'package:qiazhun/modules/account/account_add_cash_section.dart';
import 'package:qiazhun/modules/account/account_add_credit_section.dart';
import 'package:qiazhun/modules/account/account_add_donation_section.dart';
import 'package:qiazhun/modules/account/account_add_internet_account_section.dart';
import 'package:qiazhun/modules/account/account_add_investing_section.dart';
import 'package:qiazhun/modules/account/account_add_loan_section.dart';
import 'package:qiazhun/modules/account/account_add_page.dart';
import 'package:qiazhun/modules/account/account_add_saving_section.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class AccountEditPage extends StatefulWidget {
  final int? accountId;
  final int? initialTab;
  final int? initialSubTab;
  const AccountEditPage({this.accountId, this.initialTab, this.initialSubTab, super.key});

  @override
  State<StatefulWidget> createState() => _AccountEditState();
}

class _AccountEditState extends State<AccountEditPage> {
  int _selectedType = 0;
  bool _isLoading = false;

  AccountModel? _accountModel;
  final AccountAddController _addController = AccountAddController();

  final List<Map<String, dynamic>> _accountItems = [
    {
      'icon': 'assets/images/ic_credit_card.png',
      'selectedIcon': 'assets/images/ic_credit_card_selected.png',
      'label': '信用账户',
    },
    {
      'icon': 'assets/images/ic_saving_card.png',
      'selectedIcon': 'assets/images/ic_saving_card_selected.png',
      'label': '储蓄账户',
    },
    {
      'icon': 'assets/images/ic_internet_account.png',
      'selectedIcon': 'assets/images/ic_internet_account_selected.png',
      'label': '网络账号',
    },
    {'icon': 'assets/images/ic_cash_account.png', 'selectedIcon': 'assets/images/ic_cash_account_selected.png', 'label': '现金账户'},
    {'icon': 'assets/images/ic_investing_account.png', 'selectedIcon': 'assets/images/ic_investing_account_selected.png', 'label': '投资账户'},
    {'icon': 'assets/images/ic_loan_account.png', 'selectedIcon': 'assets/images/ic_loan_account_selected.png', 'label': '借贷往来'},
    {'icon': 'assets/images/ic_donation_account.png', 'selectedIcon': 'assets/images/ic_donation_account_selected.png', 'label': '捐赠账户'},
    {'icon': 'assets/images/ic_big_amount_assets.png', 'selectedIcon': 'assets/images/ic_big_amount_assets_selected.png', 'label': '大笔资产'},
  ];

  @override
  void initState() {
    if (widget.accountId != null) {
      _initData();
    } else if (widget.initialTab != null) {
      _selectedType = widget.initialTab!;
    }
    super.initState();
  }

  Future<void> _initData() async {
    _isLoading = true;
    Loading.show();
    try {
      var resp = await AccountRepo.getAccountDetail(widget.accountId!);
      if (resp.code == 1) {
        _accountModel = resp.data;
        int cardType = (int.tryParse(_accountModel?.cardType ?? '') ?? 1) - 1;
        _selectedType = cardType;
        if (cardType == 0) {
          _selectedType = 1;
        } else if (cardType == 1) {
          _selectedType = 0;
        }
      } else {
        showToast(resp.msg ?? '获取账号详情失败');
        RouterHelper.router.pop();
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace, maxFrames: 5);
      showToast('获取账号详情失败 $e');
      RouterHelper.router.pop();
    } finally {
      _isLoading = false;
      setState(() {});
      Loading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        dividerTheme: DividerThemeData(
          color: Colors.transparent,
        ),
      ),
      child: Scaffold(
        extendBody: true,
        persistentFooterButtons: [_submitButton],
        body: SafeArea(
          top: false,
          child: Container(
            color: Colors.yellow,
            child: Stack(
              children: [
                Container(
                  color: const Color(0xFFF5F5F5),
                ),
                Positioned(
                  // top: 0,
                  child: Container(
                    height: 203,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                          colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                    ),
                  ),
                ),
                Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: AppBar(
                        backgroundColor: Colors.transparent,
                        scrolledUnderElevation: 0,
                        centerTitle: true,
                        leading: IconButton(
                          icon: Image.asset(
                            'assets/images/ic_back.png',
                            width: 24,
                            height: 24,
                          ),
                          onPressed: () {
                            RouterHelper.router.pop();
                          },
                        ),
                        titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                        title: Text(
                          '编辑账号',
                          style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                        ))),
                Positioned.fill(
                    top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                    // top: 0,
                    child: _isLoading
                        ? const SizedBox()
                        : SingleChildScrollView(
                            child: Column(
                              children: [_accountTypeSection],
                            ),
                          ))
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget get _accountTypeSection {
    double width = (MediaQuery.of(context).size.width - 28 - 22 * 3) / 4;

    List<Widget> itemViews = List.generate(_accountItems.length, (index) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          if (_selectedType != index) {
            setState(() {
              _selectedType = index;
            });
          }
          // RouterHelper.router.pushNamed(items[index]['path']);
        },
        child: Container(
          width: width,
          color: Colors.transparent,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(_selectedType == index ? _accountItems[index]['selectedIcon'] : _accountItems[index]['icon'],
                  width: 36, height: 36, fit: BoxFit.fill),
              SizedBox(height: 4),
              Text(
                _accountItems[index]['label'],
                style: TextStyle(fontSize: 13, height: 1.4, color: _selectedType == index ? MColor.skin : MColor.xFF1B1C1A),
              ),
            ],
          ),
        ),
      );
    });
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 14.0),
      child: Column(
        children: [
          if (_accountModel == null) ...{
            _sectionHeader('选择账号类型'),
            const SizedBox(height: 14),
            Wrap(
              spacing: 22.0,
              runSpacing: 14.0,
              // crossAxisCount: 4, crossAxisSpacing: 0, mainAxisSpacing: 14, childAspectRatio: 1.3
              // padding: EdgeInsets.zero,
              // shrinkWrap: true,
              // physics: NeverScrollableScrollPhysics(),
              // gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(),
              // itemCount: items.length,
              // itemBuilder: (context, index) {

              // },weiiing
              children: itemViews,
            ),
            const SizedBox(
              height: 18,
            ),
          },
          _sectionHeader('填写账号信息'),
          const SizedBox(height: 14),
          _accountTypeContentView
        ],
      ),
    );
  }

  Widget _sectionHeader(String headerStr) {
    return Row(
      children: [
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.transparent, Colors.transparent, MColor.xFFFED58E, MColor.xFFFED58E],
              stops: [0.0, 0.6, 0.6, 1.0],
              begin: const FractionalOffset(0.0, 0.0),
              end: const FractionalOffset(0.0, 1.0),
            ),
          ),
          child: Text(headerStr, style: TextStyle(height: 1.4, fontSize: 16, color: MColor.xFF1B1C1A)),
        ),
      ],
    );
  }

  AccountAddSavingSection? _savingSection;
  AccountAddInternetAccountSection? _internetSection;
  AccountAddCashSection? _cashSection;

  Widget get _accountTypeContentView {
    if (_selectedType == 0) {
      return AccountAddCreditSection(
        addController: _addController,
        accountModel: _accountModel,
      );
    } else if (_selectedType == 1) {
      _savingSection ??= AccountAddSavingSection(addController: _addController, accountModel: _accountModel);
      return _savingSection!;
    } else if (_selectedType == 2) {
      _internetSection ??= AccountAddInternetAccountSection(addController: _addController, accountModel: _accountModel);
      return _internetSection!;
    } else if (_selectedType == 3) {
      _cashSection ??= AccountAddCashSection(addController: _addController, accountModel: _accountModel);
      return _cashSection!;
    } else if (_selectedType == 4) {
      return AccountAddInvestingSection(addController: _addController, accountModel: _accountModel);
    } else if (_selectedType == 5) {
      return AccountAddLoanSection(
        addController: _addController,
        accountModel: _accountModel,
        selectedSubType: widget.initialSubTab,
      );
    } else if (_selectedType == 6) {
      return AccountAddDonationSection(addController: _addController, accountModel: _accountModel);
    } else if (_selectedType == 7) {
      return AccountAddBigAssetsSection(addController: _addController, accountModel: _accountModel);
    } else {
      return const SizedBox();
    }
  }

  Widget get _submitButton {
    return GestureDetector(
      onTap: () {
        _addController.onAction?.call();
      },
      child: Container(
        height: 50,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            gradient:
                LinearGradient(begin: const FractionalOffset(0.0, 0.0), end: const FractionalOffset(0.0, 1.0), colors: [Color(0xFF68C2BF), Color(0xFF4C9B93)])),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '提交',
              style: TextStyle(color: MColor.xFFFFFFFF, fontWeight: FontWeight.w500, fontSize: 16, height: 1),
            ),
          ],
        ),
      ),
    );
  }
}
