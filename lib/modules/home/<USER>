import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/home_detail_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/detail_tab/detail_repo.dart';
import 'package:qiazhun/modules/transaction_item_view.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/loading_view.dart';
import 'package:qiazhun/widgets/progress_view.dart';
import 'package:qiazhun/widgets/round_image.dart';

class MoneyFlowPage extends StatefulWidget {
  final int pageType; // 类型:1=收入 2=支出 3=全部
  final String timeRangeType; //1 本周 2 本月 3 本年
  const MoneyFlowPage({required this.pageType, required this.timeRangeType, super.key});

  @override
  State createState() => _MoneyFlowState();
}

class _MoneyFlowState extends State<MoneyFlowPage> {
  bool _isLoading = true;
  final Map<String, String> _timeRangeTitle = {'1': '本周', '2': '本月', '3': '本年'};

  bool _sortByAmount = true;

  MoneyLogResp? _moneyLogResp;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    _isLoading = true;
    Loading.show();
    try {
      String timeInterval = generateDateRange(widget.timeRangeType);
      var resp = await DetailRepo.getMonthMoneyLogList(timeInterval: timeInterval, type: '${widget.pageType}', order: _sortByAmount ? '1' : '2');
      if (resp.code == 1) {
        _moneyLogResp = resp.data;
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      Loading.dismiss();
    }
    _isLoading = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: SafeArea(
        top: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        _timeRangeTitle[widget.timeRangeType] ?? '',
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: Builder(builder: (context) {
                    if (_isLoading) {
                      return LoadingView();
                    }
                    if (_moneyLogResp == null || _moneyLogResp?.list?.isEmpty == true) {
                      return EmptyView();
                    }
                    return Column(
                      children: [
                        Expanded(
                          child: ListView.separated(
                              padding: EdgeInsets.zero,
                              itemBuilder: (context, index) {
                                if (index == 0) {
                                  return widget.pageType == 3 ? _headerViewAll : _headerView;
                                }
                                return Container(color: MColor.xFFF5F5F5, child: _MoneyFlowItemView(_moneyLogResp!.list![index - 1]));
                              },
                              separatorBuilder: (context, index) {
                                return Divider(
                                  height: 0.5,
                                  thickness: 0.5,
                                  color: MColor.xFFD9D9D9,
                                  indent: 15,
                                );
                              },
                              itemCount: (_moneyLogResp?.list?.length ?? 0) + 1),
                        )
                      ],
                    );
                  }))
            ],
          ),
        ),
      ),
    );
  }

  String get _headerTitle {
    if (widget.pageType == 1) {
      return '单笔收入排行';
    } else if (widget.pageType == 2) {
      return '单笔支出排行';
    } else {
      return '单笔排行';
    }
  }

  Widget get _headerViewAll {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 14.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '总结余',
                style: TextStyle(fontSize: 14, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFF777777),
              ),
              Text(_moneyLogResp?.netTotal ?? '', style: TextStyle(fontSize: 32, height: 1.4, fontWeight: FontWeight.w400, color: MColor.xFF1B1C1A)),
              const SizedBox(
                height: 20,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Column(
                    children: [
                      Text(
                        '总收入',
                        style: TextStyle(fontSize: 14, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFF777777),
                      ),
                      Text(
                        _moneyLogResp?.totalIncome ?? '',
                        style: TextStyle(fontSize: 14, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFF777777),
                      )
                    ],
                  ),
                  const Spacer(),
                  Column(
                    children: [
                      Text(
                        '总支出',
                        style: TextStyle(fontSize: 14, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFF777777),
                      ),
                      Text(
                        _moneyLogResp?.totalExpense ?? '',
                        style: TextStyle(fontSize: 14, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFF777777),
                      )
                    ],
                  )
                ],
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 20,
        ),
        Container(
          height: 20,
          color: MColor.xFFFFFFFF,
        ),
        _sortView
      ],
    );
  }

  Widget get _headerView {
    return Column(
      children: [
        Text(
          widget.pageType == 1 ? '总收入' : '总支出',
          style: TextStyle(fontSize: 14, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFF777777),
        ),
        const SizedBox(
          height: 12,
        ),
        Text(widget.pageType == 1 ? (_moneyLogResp?.totalIncome ?? '') : (_moneyLogResp?.totalExpense ?? ''),
            style: TextStyle(fontSize: 32, height: 1.4, fontWeight: FontWeight.w400, color: MColor.xFF1B1C1A)),
        const SizedBox(
          height: 20,
        ),
        Container(
          height: 20,
          color: MColor.xFFFFFFFF,
        ),
        _sortView
      ],
    );
  }

  Widget get _sortView {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14, vertical: 4),
      // decoration: BoxDecoration(color: MColor.skin),
      child: Row(
        children: [
          Text(
            _headerTitle,
            style: TextStyle(fontSize: 14, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A),
          ),
          const Spacer(),
          Container(
              padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                color: MColor.xFFEEEEEE,
                borderRadius: BorderRadius.circular(15),
              ),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        if (!_sortByAmount) {
                          _sortByAmount = true;
                          _loadData();
                        }
                      });
                    },
                    child: Container(
                        height: 20,
                        padding: EdgeInsets.symmetric(horizontal: 6),
                        alignment: Alignment.center,
                        decoration: _sortByAmount ? BoxDecoration(borderRadius: BorderRadius.circular(14), color: MColor.xFFFFFFFF) : null,
                        child: Text(
                          '按金额',
                          style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF1B1C1A),
                        )),
                  ),
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: () {
                      if (_sortByAmount) {
                        _sortByAmount = false;
                        _loadData();
                      }
                    },
                    child: Container(
                        height: 20,
                        padding: EdgeInsets.symmetric(horizontal: 6),
                        alignment: Alignment.center,
                        decoration: !_sortByAmount ? BoxDecoration(borderRadius: BorderRadius.circular(14), color: MColor.xFFFFFFFF) : null,
                        child: Text(
                          '按时间',
                          style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF1B1C1A),
                        )),
                  )
                ],
              ))
        ],
      ),
    );
  }
}

class _MoneyFlowItemView extends StatelessWidget {
  final MoneyLog data;
  const _MoneyFlowItemView(this.data, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
        color: MColor.xFFF5F5F5,
        padding: EdgeInsets.symmetric(vertical: 6),
        child: Row(children: [
          const SizedBox(
            width: 14,
          ),
          RoundImage(imageUrl: data.icon ?? '', radius: 18, size: 36),
          const SizedBox(
            width: 6,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                        child: Row(
                      children: [
                        if (data.memo.isNotEmpty == true) ...{
                          Expanded(
                            flex: 0,
                            child: Text(
                              data.memo ?? '',
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(fontSize: 15, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A),
                            ),
                          ),
                          const SizedBox(
                            width: 6,
                          ),
                        },
                        Expanded(
                          child: Text(
                            data.percentage ?? '',
                            softWrap: false,
                            style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF1B1C1A),
                          ),
                        ),
                      ],
                    )),
                    const SizedBox(width: 12),
                    Expanded(
                      flex: 0,
                      child: Text(
                        textAlign: TextAlign.end,
                        data.money ?? '',
                        style: TextStyle(fontSize: 14, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A),
                      ),
                    )
                  ],
                ),
                const SizedBox(
                  height: 2,
                ),
                Builder(builder: (context) {
                  var fullWidth = (MediaQuery.of(context).size.width - 14 * 2 - 36 - 6);
                  var ratio = (double.tryParse(data?.actualPercentage ?? '') ?? 0.0) / 100;
                  return ProgressView(width: fullWidth, height: 12, progress: ratio, color: data.type == '1' ? MColor.xFFCB322E : MColor.skin);
                }),
                const SizedBox(
                  height: 6,
                ),
                Text(
                  data.date ?? '',
                  style: TextStyle(fontSize: 12, height: 1.4, fontWeight: FontWeight.w400, color: MColor.xFF999999),
                )
              ],
            ),
          ),
          const SizedBox(
            width: 14,
          ),
        ]));
  }
}
